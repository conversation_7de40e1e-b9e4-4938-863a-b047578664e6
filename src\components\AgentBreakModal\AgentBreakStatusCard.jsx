import React, { useEffect, useState } from "react";
import { ChatState } from "../../context/AllProviders";
import styles from "./AgentBreakStatusCard.module.css";
import { BASE_URL2 } from "../../api/api";
import axios from "axios";
import { toast } from "react-toastify";
const AgentBreakStatusCard = ({ currentUser }) => {
  const { breakInfo, endBreak } = ChatState();
  const [remaining, setRemaining] = useState("");
  const [isCollapsed, setIsCollapsed] = useState(false);


  useEffect(() => {
        
    if (!breakInfo?.active) return;

    const until = new Date(breakInfo.breakUntil);

    const updateRemaining = () => {
      const now = new Date();
      const diff = until - now;

      if (diff <= 0) {
        setRemaining("Ended");
        toast.info("Break ended");
        endBreak();
        return;
      }

      const secs = Math.floor((diff / 1000) % 60);
      const mins = Math.floor((diff / 1000 / 60) % 60);
      const hrs = Math.floor((diff / 1000 / 60 / 60) % 24);
      const days = Math.floor(diff / 1000 / 60 / 60 / 24);

      setRemaining(
        breakInfo.mode === "holiday"
          ? `Back on ${until.toLocaleDateString()}`
          : `Ends in ${days ? days + "d " : ""}${hrs}h ${mins}m ${secs}s`
      );
    };

    updateRemaining();
    const timerId = setInterval(updateRemaining, 1000);
    const autoCollapseId = setTimeout(() => setIsCollapsed(true), 8000); // Auto collapse

    return () => {
      clearInterval(timerId);
      clearTimeout(autoCollapseId);
    };
  }, [breakInfo]);

  const handleEndBreak = async () => {
    if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
    const payload = {
      user_id: currentUser.user_id,
      token: currentUser.token,
      method: "delete_agent_break",
      id:breakInfo?.id,
      user_type:currentUser.user_type

    }

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent_setting`, payload);
      if (data.success) {
        endBreak();
        toast.info("Break ended")
      }

    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.")

      } else {
        console.error(error);
         toast.error("Something went wrong, please try again later")
      }

    }
  }

  if (!breakInfo?.active) return null;

  const formattedEndTime = new Date(breakInfo?.breakUntil).toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <div className={styles.cardContainer}>
      {isCollapsed ? (
        <div className={styles.pill} onClick={() => setIsCollapsed(false)}>
          {breakInfo.mode === "holiday" ? "🏖️ Holiday" : "⏸️ Break"} – Click to Expand
        </div>
      ) : (
        <div className={styles.card}>
          <div className="d-flex justify-content-between align-items-center w-100">
            <span className="fw-bold text-primary me-2">
              {breakInfo.mode === "holiday" ? "🏖️ On Holiday" : "⏸️ On Break"}
            </span>
            <span className="text-muted small">{remaining}</span>
          </div>

          {breakInfo.mode !== "holiday" && (
            <div className="text-muted small">
              Back at: <strong>{formattedEndTime}</strong>
            </div>
          )}

          <div className="d-flex gap-2 mt-2">
            <button
              className="btn btn-sm btn-outline-danger w-100"
              onClick={handleEndBreak}
            >
              End Now
            </button>
            <button
              className="btn btn-sm btn-outline-secondary"
              onClick={() => setIsCollapsed(true)}
            >
              Collapse
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentBreakStatusCard;
