import React from "react";
import { 
    <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Car<PERSON><PERSON>G<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer 
} from "recharts";

// ChartSection Component with enhanced UI
const ChartSection = ({ title, data, type }) => {
    const renderChart = () => {
        if (type !== "line") return null;

        return (
            <LineChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 10 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#ddd" />
                <XAxis dataKey="Date" tick={{ fill: "#555" }} />
                <YAxis tick={{ fill: "#555" }} />
                <Tooltip 
                    contentStyle={{ backgroundColor: "rgba(0,0,0,0.75)", color: "#fff", borderRadius: "8px" }}
                    itemStyle={{ fontSize: "14px" }}
                />
                <Legend wrapperStyle={{ fontSize: "14px", marginTop: "10px" }} />
                <Line type="monotone" dataKey="Total Chats" stroke="#FF9F40" strokeWidth={2} dot={{ r: 4 }} />
                <Line type="monotone" dataKey="Read Chats" stroke="#36A2EB" strokeWidth={2} dot={{ r: 4 }} />
                <Line type="monotone" dataKey="Unread Chats" stroke="#FF6384" strokeWidth={2} dot={{ r: 4 }} />
                <Line type="monotone" dataKey="New Chats" stroke="#4BC0C0" strokeWidth={2} dot={{ r: 4 }} />
                <Line type="monotone" dataKey="Repeated Chats" stroke="#FFCD56" strokeWidth={2} dot={{ r: 4 }} />
            </LineChart>
        );
    };

    return (
        <div className="p-4 w-100 bg-white shadow rounded-lg w-full" >
            {type !== "pie" && <h2 className="text-lg font-semibold text-gray-700 mb-3">{title}</h2>}
            <ResponsiveContainer width="100%" height={350}>
                {renderChart()}
            </ResponsiveContainer>
        </div>
    );
};

export default ChartSection;
