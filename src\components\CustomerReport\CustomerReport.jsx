import axios from "axios";
import { useState, useEffect, useContext, useRef } from "react";
import { BASE_URL, BASE_URL2 } from "../../api/api";
import { AuthContext } from "../../context/AuthContext";
import { FiFilter } from "react-icons/fi";
import { MdDownload } from "react-icons/md";
import { CSVLink } from 'react-csv';
import styles from './customerReport.module.css';
import { useOutletContext } from "react-router-dom";
import { useDebounce } from "../../customHooks/useDebounce";
import { FaAngleUp } from "react-icons/fa";
import { CircularProgress } from "@mui/material";
import { useMaskNo } from "../../customHooks/useMaskNo";
import { formatLocalDate } from "../../utils/Utils";
import Select from "react-select";
import AssignModal from "./AssignModal";
import { ChatState } from "../../context/AllProviders";
import { toast } from "react-toastify";
import LabelCard from "../Labels/LabelCard";
const CustomerReport = () => {
    const maskNo = useMaskNo();
    const [loading, setLoading] = useState(false);
    const { currentUser } = useContext(AuthContext);
    const { agents, fetchAgents } = ChatState();
    const [contacts, setContacts] = useState([]);
    const [filteredContacts, setFilteredContacts] = useState([]);
    const [filters, setFilters] = useState({});
    const [selectedContacts, setSelectedContacts] = useState([])
    const [searchQuery, setSearchQuery] = useState("");
    const [headers, setHeaders] = useState([]);
    // const [dateRange, setDateRange] = useState([new Date().toISOString(), new Date().toISOString()]);
    // const [fromDate, toDate] = dateRange;
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [templatePopUp, setTemplatePopUp] = useState(false);
    const [page, setPage] = useState(1);
    const [showGoToTop, setShowGoToTop] = useState(false);
    const [labels, setLabels] = useState([]);
    const [showFilters, setShowFilters] = useState(false);
    const filterRef = useRef(null);
    const [isFetching, setIsFetching] = useState(false);
    const { fromDate, toDate, setFetchDataFn } = useOutletContext();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalType, setModalType] = useState(null);
    const [selectedOption, setSelectedOption] = useState(null);

    const scrollContainerRef = useRef(null);

    const debouncedSearchInput = useDebounce(searchQuery, 300);

    // Developer-configurable mapping for keys to display user-friendly labels
    const keyLabelMapping = {
        name: "Customer Name",
        email: "Email Address",
        mobile: "Mobile",
        update_time: "Date",
        team_name: "Team",
        agent_name: "Agent",
        first_msg: "First message",
        content: "Last Message",
        status: "Status",
        "label[0].name": "Label",
    };

    // Specify which keys should be displayed and filtered
    const headerKeys = ['name', 'email', 'mobile', 'team_name', 'agent_name', 'first_msg', 'content',
        'status', 'update_time', 'label[0].name']; // The keys for displaying in table

    // Keys for filtering (can be different from header keys)
    const filterKeys = ['team_name', 'first_msg'];

    useEffect(() => {
        if (currentUser.user_id && currentUser.token && currentUser.user_type
            && fromDate && toDate
        ) {

            fetchNewContactList();
            fetchAgents();
        }


    }, [currentUser])

    useEffect(() => {

        if (setFetchDataFn) setFetchDataFn(() => fetchNewContactList)

    }, [currentUser, fromDate, toDate]);


    useEffect(() => {
        const applyFiltersAndSearch = () => {
            let tempContacts = [...contacts];

            // Apply filters based on selected keys
            Object.keys(filters).forEach((key) => {
                if (filters[key]) {

                    if (key === "label.name" && Array.isArray(filters[key])) {
                        const selectedLabels = filters[key];

                        tempContacts = tempContacts.filter((contact) => {
                            // Case: No label assigned
                            if ((!contact.label || contact.label.length === 0)) {
                                return selectedLabels.includes("__NO_LABEL__");
                            }

                            // Case: Contact has one label, match against selected labels
                            return contact.label.some((lbl) => selectedLabels.includes(lbl.name));
                        });
                    }
                    else if (key === "team_name") {
                        // ✅ New logic: Filter by team name or agent name
                        tempContacts = tempContacts.filter((contact) =>
                            contact?.team_name?.toLowerCase().includes(filters[key].toLowerCase()) ||
                            contact?.mobile?.toLowerCase().includes(filters[key].toLowerCase()) ||
                            contact?.name?.toLowerCase().includes(filters[key].toLowerCase())
                        );
                    }
                    else if (key === "agent_name") {
                        tempContacts = tempContacts.filter((contact) =>
                            contact?.agent_id === filters[key]
                        );
                    }
                    else {
                        tempContacts = tempContacts.filter((contact) =>
                            key === "status"
                                ? contact?.status === filters[key] // Exact match for status
                                : String(contact[key])
                                    .toLowerCase()
                                    .includes(filters[key].toLowerCase())
                        );

                    }
                }
            });

            // Apply search query
            if (searchQuery) {
                const searchableKeys = ['name', 'email', 'mobile', 'content']; // Specify searchable fields
                tempContacts = tempContacts.filter((contact) =>
                    searchableKeys.some((key) =>
                        String(contact[key]).toLowerCase().includes(searchQuery.toLowerCase())
                    )
                );
            }

            setFilteredContacts(tempContacts);
        };

        applyFiltersAndSearch();
    }, [filters, searchQuery, contacts]);



    useEffect(() => {
        const fetchLabels = async () => {
            if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
            const body = {
                user_id: currentUser.parent_id,
                token: currentUser.parent_token,
                method: "wp_list_retrieve_all",
            };

            try {
                const response = await axios.post(`${BASE_URL2}/contact_list`, body);
                if (response.data.success) {
                    setLabels(response.data.data); // Store labels if fetch is successful

                }
            } catch (error) {
                console.error("Error fetching labels:", error);
            }
        };

        fetchLabels();
    }, [currentUser]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (filterRef.current && !filterRef.current.contains(event.target)) {
                setShowFilters(false);
            }
        };

        if (showFilters) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [showFilters]);


    const fetchNewContactList = async () => {
        if (!currentUser || !currentUser.token || !currentUser.user_id || !currentUser.user_type || !fromDate || !toDate) return;
        setIsFetching(true);
        setLoading(true);
        const payload = {
            token: currentUser.token,
            user_id: currentUser.user_id,
            method: "left_menu",
            brand_number: currentUser.brand_number,
            agent_id: 0,
            type: "whatsapp",
            from_date: formatLocalDate(fromDate),
            to_date: formatLocalDate(toDate),
            page: 1,
            user_type: currentUser.user_type
        };
        try {
            const { data } = await axios.post(
                `${BASE_URL2}/whatsapp_conv`,
                payload,

            );

            if (data.success === true) {
                // Helper function to determine status based on time
                const getStatus = (dateString) => {
                    const givenDate = new Date(dateString); // This is in UTC
                    const currentDate = new Date();

                    // Convert both to UTC timestamps for accurate comparison
                    const givenTimeUTC = givenDate.getTime();
                    const currentTimeUTC = currentDate.getTime();
                    const timeThresholdUTC = currentTimeUTC - 24 * 60 * 60 * 1000; // 24 hours ago in UTC

                    return givenTimeUTC < timeThresholdUTC ? "Inactive" : "Active";
                };


                const updatedContacts = data.data.map((contact) => ({
                    ...contact,
                    status: getStatus(contact.update_time),
                }));

                setContacts(updatedContacts);
                setFilteredContacts(updatedContacts)


                const dynamicHeaders = headerKeys.map((key) => ({
                    label: keyLabelMapping[key] || key.charAt(0).toUpperCase() + key.slice(1),
                    key,
                }));
                setHeaders(dynamicHeaders);

                // Initialize filters only for selected keys
                if (page === 1) {
                    setFilters(
                        filterKeys.reduce((acc, key) => {
                            acc[key] = "";
                            return acc;
                        }, {})
                    );
                }
            }
        } catch (error) {
            console.error("Error fetching contact list:", error);
        } finally {
            setIsFetching(false);
            setLoading(false);
        }

    }


    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters((prev) => ({ ...prev, [name]: value }));
    };



    const isFilterApplied = () => {
        return (
            Object.values(filters).some((value) => value)
        );
    };

    // Function to clear all filters
    const clearFilters = () => {
        setFilters({});
        setSearchQuery("");

    };
    const handleLabelFilterChange = (selectedOptions) => {
        const values = selectedOptions ? selectedOptions.map((opt) => opt.value) : [];

        setFilters((prev) => {
            const newFilters = { ...prev };

            if (values.length === 0) {
                // 🚫 If no label selected, remove label.name filter
                delete newFilters["label.name"];
            } else {
                // ✅ Set selected labels
                newFilters["label.name"] = values;
            }

            return newFilters;
        });
    };


    const toggleSelectContact = (contact) => {
        setSelectedContacts((prevSelectedContacts) => {
            const isSelected = prevSelectedContacts.some((c) => c.mobile === contact.mobile);
            if (isSelected) {
                // Remove the contact from the array
                return prevSelectedContacts.filter((c) => c.mobile !== contact.mobile);
            } else {
                // Add the contact to the array
                return [...prevSelectedContacts, {
                    name: contact.name,
                    email: contact.email,
                    mobile: contact.mobile,
                    team_name: contact.team_name,
                    content: contact.content,
                    status: contact.status,
                    oldest_message_content: contact.first_msg,
                    agent_name: contact.agent_name

                }];
            }
        });
    };
    const toggleSelectAll = () => {
        if (selectedContacts.length === filteredContacts.length) {
            setSelectedContacts([]); // Deselect all
        } else {
            setSelectedContacts(
                filteredContacts.map((contact) => ({
                    name: contact.name,
                    email: contact.email,
                    mobile: contact.mobile,
                    team_name: contact.team_name,
                    content: contact.content,
                    status: contact.status,
                    oldest_message_content: contact.first_msg,
                    agent_name: contact.agent_name
                }))
            ); // Select all
        }
    };

    const handleAssign = async (e) => {
        e.preventDefault();

        const chatList = selectedContacts.map((item) => item.mobile); // assuming selectedContacts holds objects with mobile

        const forAssignAgent = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "chat_transfer",
            transfer_type: "agent",
            transfer_to: selectedOption.value, // agent ID
            transfer_from: currentUser.user_id,
            chat_list: chatList
        };

        try {
            const { data } = await axios.post(
                `${BASE_URL2}/whatsapp_agent`,
                forAssignAgent
            );

            if (data.success === true) {

                setContacts((prevState) =>
                    prevState.map((item) => {
                        const match = selectedContacts.find((sc) => sc.mobile === item.mobile);
                        if (match) {
                            return {
                                ...item,
                                agent_name: selectedOption.label,
                                agent_id: selectedOption.value
                            };
                        }
                        return item;
                    })
                );
                setSelectedContacts([]);
                setSelectedOption(null);
                setIsModalOpen(false);
                toast.success(data.message);
            } else {
                toast.error(data.message);
            }

        } catch (error) {
            console.error(error);
            toast.error("Assignment failed. Please try again.");
        } finally {
            // setIsLoading(false);
        }
    };

    const handleCloseModal = () => {
        setSelectedOption(null);
        setIsModalOpen(false);
    };

    const labelOptions = labels.map(label => ({
        value: label.name,
        label: label.name
    }));

    const handleScroll = () => {
        const { scrollTop, clientHeight, scrollHeight } =
            scrollContainerRef.current;

        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

        if (isAtBottom && !isFetching) {
            setPage(page + 1);
            const nextPage = page + 1

        }
        setShowGoToTop(scrollTop > clientHeight);
    };

    const scrollToTop = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
        }
    };

    const getNestedValue = (obj, key) => {
        return key.split('.').reduce((acc, part) => {
            if (!acc) return undefined;

            // Handle array indexes like `label[0].name`
            if (part.includes('[') && part.includes(']')) {
                const [arrayKey, index] = part.split(/\[|\]/).filter(Boolean);
                return acc[arrayKey] && acc[arrayKey][parseInt(index, 10)];
            }

            return acc[part];
        }, obj);
    };

    const downloadCSV = () => {
        return filteredContacts.map(contact => {
            const row = {};
            headerKeys.forEach((key) => {
                if (key === "label[0].name") {
                    row[key] = contact.label?.length
                        ? contact.label.map(lbl => lbl.name).join(", ")
                        : "No Label";
                } else if (key === "update_time") {
                    row[key] = contact[key] ? `${contact[key].split("T")[0]} ${contact[key].split("T")[1]}` : "N/A";
                } else {
                    row[key] = getNestedValue(contact, key) || "N/A";
                }
            });
            return row;
        });
    };


    return (

        <div className={`d-flex flex-column justify-content-start align-items-center p-3 ${styles.scrollContainer}`}>

            <div className={`d-flex  w-100 justify-content-between
                 align-items-center mb-2`} >
                <div style={{
                    color: "maroon", fontSize: "1.2rem",
                    fontWeight: "bold"
                }}
                    className={`${styles.totalLengthContainer}`}
                >
                    Total Contacts: {filteredContacts.length}

                </div>

                {selectedContacts.length > 0 && (
                    <div className={`dropdown ${styles.bulkActionDropdown}`}>
                        <button
                            className={`btn btn-sm dropdown-toggle ${styles.bulkActionPill}`}
                            type="button"
                            id="bulkActionDropdown"
                            data-bs-toggle="dropdown"
                            aria-expanded="false"
                        >
                            <i className="bx bx-dots-horizontal-rounded me-2" />
                            Actions
                        </button>


                        <ul className={`dropdown-menu shadow-sm ${styles.dropdownMenu}`} aria-labelledby="bulkActionDropdown">
                            {/* <li>
                                <button
                                    className={`dropdown-item d-flex align-items-center ${styles.dropdownItem}`}
                                    onClick={() => {
                                        setModalType('label');
                                        setIsModalOpen(true);
                                    }}
                                >
                                    <i className="bx bx-purchase-tag me-2 text-primary" />
                                    Assign Label
                                </button>
                            </li> */}
                            <li>
                                <button
                                    className={`dropdown-item d-flex align-items-center ${styles.dropdownItem}`}
                                    onClick={() => {
                                        setModalType('agent');
                                        setIsModalOpen(true);
                                    }}
                                >
                                    <i className="bx bxs-user-voice me-2 text-success" />
                                    Assign Agent
                                </button>
                            </li>
                            <li><hr className="dropdown-divider" /></li>
                            <li>
                                <button
                                    className={`dropdown-item d-flex align-items-center text-danger ${styles.dropdownItem}`}
                                    onClick={() => setSelectedContacts([])}
                                >
                                    <i className="bx bx-x-circle me-2" />
                                    Clear Selection
                                </button>
                            </li>
                        </ul>

                    </div>
                )}
                <div className="d-flex align-items-center gap-3">

                    <div className={`${styles.iconWrapper} d-flex d-md-none `}
                        onClick={() => setShowFilters(!showFilters)}
                    >
                        {isFilterApplied() && <div className={styles.dot} />}
                        <FiFilter size={24} role="button" className="text-primary" />
                    </div>
                    <div className={`${styles.iconWrapper} d-flex d-md-none`}>
                        <CSVLink
                            data={downloadCSV()}
                            filename="contacts.csv"
                            target="_blank"
                            className="text-black"
                        >
                            <MdDownload size={24} />
                        </CSVLink>
                    </div>
                </div>


                <div className="d-flex justify-content-betweeen align-items-center gap-2">
                    <div ref={filterRef} className={`${styles.filterRow} ${showFilters ? styles.showFilter : ""}`}>
                        <div className="d-flex align-items-center justify-content-start flex-wrap">
                            {filterKeys.map((key) => (
                                <div key={key} className={`me-2 mb-2 ${styles.filterItem}`}>
                                    <input
                                        disabled={loading}
                                        type="text"
                                        id={`${key}Filter`}
                                        name={key}
                                        value={filters[key] || ""}
                                        onChange={handleFilterChange}
                                        className="form-control"
                                        placeholder={`Filter by ${keyLabelMapping[key]}${key === "team_name" ? '/Customer' : ''}`} />
                                </div>
                            ))}

                            <div className={`me-2 mb-2 ${styles.filterItem}`}>
                                <Select
                                    options={[{ label: "All Agents", value: "" }, ...agents]}
                                    placeholder={`Select agent`}
                                    value={{ label: "All Agents", value: "" }}
                                    classNamePrefix="react-select"
                                    menuPortalTarget={document.body}
                                    styles={{
                                        menuPortal: (base) => ({ ...base, zIndex: 99999 }),
                                    }}
                                    onChange={(selected) => {
                                        setFilters((prev) => ({
                                            ...prev,
                                            agent_name: selected?.value || "", // clear if null
                                        }));
                                    }}
                                />
                            </div>

                            <div className={`me-2 mb-2 ${styles.filterItem}`}>
                                <select name="status" id="statusFilter" className="form-control"
                                    value={filters.status ? filters.status : ""}
                                    onChange={handleFilterChange}
                                    disabled={loading}
                                >
                                    <option value="">Select Status</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>

                            <div className={`me-2 mb-2 ${styles.filterItem}`}>

                                <Select
                                    name="label.name"
                                    id="labelFilter"
                                    classNamePrefix="react-select"
                                    isDisabled={loading}
                                    isMulti
                                    closeMenuOnSelect={false}
                                    options={[
                                        { value: "__NO_LABEL__", label: "No Label" },
                                        ...labelOptions
                                    ]}
                                    value={
                                        filters["label.name"]
                                            ? filters["label.name"].map((val) => ({
                                                value: val,
                                                label: val === "__NO_LABEL__" ? "No Label" : val,
                                            }))
                                            : []
                                    }
                                    // onChange={(selectedOptions) => {
                                    //     const values = selectedOptions ? selectedOptions.map((opt) => opt.value) : [];
                                    //     setFilters((prev) => ({ ...prev, ["label.name"]: values }));
                                    // }}
                                    styles={{
                                        menuPortal: (base) => ({ ...base, zIndex: 99999 }),
                                    }}
                                    menuPortalTarget={document.body}
                                    onChange={handleLabelFilterChange}
                                    placeholder="Filter by Label(s)"
                                />


                            </div>
                            {isFilterApplied() && (
                                <div>
                                    <button
                                        className={`${styles.filterRemoveBtn} btn-sm`}
                                        onClick={clearFilters}
                                    >
                                        Remove
                                    </button>
                                </div>


                            )}
                        </div>
                    </div>
                    <div className={`${styles.iconWrapper} d-none d-md-flex`}>
                        <CSVLink
                            data={downloadCSV()}
                            filename="contacts.csv"
                            target="_blank"
                            className="text-black"
                        >
                            <MdDownload size={24} />
                        </CSVLink>
                    </div>
                </div>

            </div>


            {/* Table Section */}

            {loading ? <CircularProgress
                style={{
                    position: 'fixed',
                    border: 'none',
                    width: '40px',
                    height: '40px',
                    bottom: "15vh",
                    cursor: 'pointer',

                }}
            /> : <div
                className={styles.tableContainer}

                ref={scrollContainerRef}
                onScroll={handleScroll}

            >
                <table className="table">
                    <thead
                        style={{
                            position: "sticky",
                            top: 0, // Stick the header to the top
                            backgroundColor: "#f8f9fa", // Match Bootstrap table header color
                            zIndex: "50"
                        }}
                    >
                        <tr>
                            <th style={{ width: "50px", whiteSpace: "nowrap" }}>
                                <div className="d-flex justify-content-start align-items-center ">
                                    <input
                                        type="checkbox"
                                        checked={
                                            selectedContacts.length === filteredContacts.length &&
                                            filteredContacts.length !== 0
                                        }
                                        onChange={toggleSelectAll}
                                        className={styles.customCheckbox}

                                    />
                                    <label
                                        htmlFor=""
                                        className="ml-2"
                                        style={{ fontWeight: "bold" }}
                                    >
                                        Select All
                                    </label>
                                    {selectedContacts.length !== 0 && <div>({selectedContacts.length})</div>}
                                </div>
                            </th>
                            {headers.map((header) => (
                                <th
                                    key={header.key}
                                    style={{ fontWeight: "bold" }}
                                >
                                    {header.label}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {filteredContacts.length ? (
                            filteredContacts.map((contact, index) => (
                                <tr
                                    key={index}
                                    style={{
                                        backgroundColor: index % 2 === 0 ? "white" : "whiteSmoke", // Alternating row colors

                                    }}

                                >
                                    <td style={{ width: "50px" }}>
                                        <input
                                            type="checkbox"
                                            checked={selectedContacts.some((c) => c.mobile === contact.mobile)} // Track selected contacts by ID
                                            onChange={() => toggleSelectContact(contact)}
                                            className={styles.customCheckbox}

                                        />
                                    </td>
                                    {headers.map((header) => (
                                        <td
                                            key={`${index}-${header.key}`}
                                            style={{
                                                width: "10px", // Adjust as needed
                                                textOverflow: "ellipsis",
                                                ...(header.key === "status" && {
                                                    color: contact.status === "Active" ? "green" : "red",
                                                }),
                                            }}
                                        >

                                            {header.key === "mobile" ? (
                                                currentUser.user_type === "admin" ? (
                                                    contact[header.key]
                                                ) : (
                                                    maskNo(contact[header.key])
                                                )
                                            ) : header.key === "content" ? (
                                                <>

                                                    {contact[header.key]}
                                                </>
                                            )
                                                : header.key === "update_time" ? (
                                                    <>
                                                        {new Date(contact[header.key]).toLocaleDateString()}{" "}
                                                        {new Date(contact[header.key]).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                                    </>
                                                )

                                                    : header.key === "label[0].name" ? (
                                                        contact.label?.length ? (
                                                            <div className="d-flex flex-wrap gap-2">
                                                                {contact.label.map((lbl, idx) => (
                                                                    <LabelCard key={idx} labelData={lbl} />
                                                                ))}
                                                            </div>
                                                        ) : (
                                                            "No Label"
                                                        )
                                                    ) : (
                                                        getNestedValue(contact, header.key) || "N/A"

                                                    )
                                            }

                                        </td>
                                    ))}

                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan={headers.length + 1} className="text-center">
                                    No Data Available
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>}

            <AssignModal
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onAssign={handleAssign}
                modalType={modalType}
                options={modalType === 'agent' ? agents : labelOptions}
                selectedOption={selectedOption}
                setSelectedOption={setSelectedOption}
            />

            {showGoToTop && (
                <button
                    onClick={scrollToTop}
                    className={styles.goToTop}
                    style={{
                        position: 'fixed',
                        backgroundColor: 'lightGray',
                        color: 'black',
                        border: 'none',
                        borderRadius: '50%',
                        width: '40px',
                        height: '40px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        cursor: 'pointer',
                        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                    }}
                >
                    <FaAngleUp size={20} />
                </button>)}

        </div>

    );
}

export default CustomerReport;
