{"name": "agentchat", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^6.0.0", "@mui/x-data-grid": "^7.14.0", "axios": "^1.7.2", "bootstrap": "^5.3.3", "dayjs": "^1.11.11", "emoji-picker-react": "^4.11.1", "firebase": "^9.23.0", "react": "^18.0.0", "react-bootstrap": "^2.10.5", "react-calendar": "^5.1.0", "react-csv": "^2.2.2", "react-datepicker": "^7.3.0", "react-dom": "^18.0.0", "react-icons": "^5.3.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "socket.io-client": "^4.7.5", "uuid": "^8.3.2"}, "scripts": {"dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}