import React, { useState, useContext, useEffect } from "react";
import styles from "./agentDetailPage.module.css";
import { Card, Modal } from "react-bootstrap";
import {
    <PERSON>aEnvelope,
    <PERSON>a<PERSON>ser,
    <PERSON>a<PERSON>lock,
    FaCheck,
    FaTimes,
    FaCalendarAlt,
    FaRedo
} from "react-icons/fa";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import { AuthContext } from "../../context/AuthContext";
import { useParams } from "react-router-dom";
import axios from "axios";
import { BASE_URL2 } from "../../api/api";
import { toast } from "react-toastify";
import LeftMenu from "../../components/LeftMenu";
import DatePicker from "react-datepicker";
import { IoMdSettings } from "react-icons/io";
import { Link } from "react-router-dom";
import { Switch, CircularProgress } from "@mui/material";
import { ChatState } from "../../context/AllProviders";
// Static dayMap for Calendar
const dayMap = {
    "2023-09-01": { holiday: false, active: "4h" },
    "2023-09-02": { holiday: true, active: "" },
    "2023-09-03": { holiday: false, active: "6h" },
    // ...and so on
};

/**
 * Renders custom tile content for each date cell.
 */
function tileContent({ date, view }) {
    if (view === "month") {
        const dayStr = date.toISOString().split("T")[0];
        const data = dayMap[dayStr];
        if (data) {
            if (data.holiday) {
                return <div className={styles.holidayText}>Holiday</div>;
            }
            return <div className={styles.activeText}>Active: {data.active}</div>;
        }
    }
    return null;
}

const AgentDetailPage = () => {
    // Example static agent data (You can remove or replace if needed)
    const agent = {
        name: "John Doe",
        email: "<EMAIL>",
        role: "Customer Support",
        profilePic: "https://via.placeholder.com/100",
        stats: {
            totalChats: 150,
            readChats: 120,
            unreadChats: 30,
            activeTime: "5h 30m",
        },
        activityLogs: [
            { id: 1, action: "Replied to a customer", time: "2 mins ago" },
            { id: 2, action: "Marked a chat as resolved", time: "10 mins ago" },
            { id: 3, action: "Started a new conversation", time: "1 hour ago" },
        ],
    };

    // Helper to get start and end date of the previous month
    const getStartAndEndOfPreviousMonth = () => {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);
        return { start: firstDay, end: lastDay };
    };

    const { start, end } = getStartAndEndOfPreviousMonth();

    const { currentUser } = useContext(AuthContext);

    // Initialize agentData as an empty array to avoid errors with .reduce
    const [agentData, setAgentData] = useState([]);
    const [activityLogs, setActivityLogs] = useState([]);
    const [dateRange, setDateRange] = useState([start, end]);
    const [fromDate, toDate] = dateRange;
    // State for Calendar Modal
    const [showCalendar, setShowCalendar] = useState(false);
    const [loading, setLoading] = useState(false);
    const { agentId } = useParams();
    const [activeTab, setActiveTab] = useState("overview");
    const { mobileVisible, setMobileVisible } = ChatState();
    const [keywords, setKeywords] = useState("");
    const [addedKeywords, setAddedKeywords] = useState([]);
    const [keywordSuggestions, setKeywordSuggestions] = useState([]);


    useEffect(() => {
        if (agentId) {
            fetchAgentData();
            fetchActivityLog();
        }
    }, [currentUser, agentId, fromDate, toDate]);

    /**
     * Compute totals from agentData array
     * If agentData is empty, default to zero values.
     */
    const { totalChats, unreadChats, repeatChats } = agentData.length
        ? agentData.reduce(
            (acc, obj) => {
                acc.totalChats += obj.totalnumber || 0;
                acc.unreadChats += obj.replynumcount || 0;
                acc.repeatChats += obj.totalrepeatnumber || 0;
                return acc;
            },
            { totalChats: 0, unreadChats: 0, repeatChats: 0 }
        )
        : { totalChats: 0, unreadChats: 0, repeatChats: 0 };

    // Format date in YYYY-MM-DD for the API
    const formatDate = (date) => {
        if (!date) return "";
        const options = { timeZone: "Asia/Kolkata", year: "numeric", month: "2-digit", day: "2-digit" };
        const formattedDate = new Date(date).toLocaleString("en-IN", options);
        const [day, month, year] = formattedDate.split("/");
        return `${year}-${month}-${day}`;
    };

    // Fetch agent data from your API
    const fetchAgentData = async () => {
        if (!currentUser || !currentUser.parent_id || !currentUser.parent_token || !fromDate || !toDate) return;
        setLoading(true);

        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "agentwise_report",
            brand_number: `${currentUser.brand_number}`,
            user_type: "admin",
            agent_id: agentId,
            from_date: new Date(fromDate).toISOString().split("T")[0],
            to_date: new Date(toDate).toISOString().split("T")[0],
        };

        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_report`, payload);
            if (data.success) {
                setAgentData(data.data);
            } else {
                toast.error("Failed to fetch agent data.");
            }
        } catch (error) {
            console.error(error);
            toast.error("Something went wrong. Please try again later.");
        } finally {
            setLoading(false);
        }
    };

    const fetchActivityLog = async () => {
        if (!currentUser || !currentUser.parent_id || !currentUser.parent_token || !fromDate || !toDate) return;
        setLoading(true);
        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "agent_login_history",
            agent_id: agentId,
            fromdate: new Date(fromDate).toISOString().split("T")[0],
            todate: new Date(toDate).toISOString().split("T")[0],
        };

        try {
            const { data } = await axios.post(`${BASE_URL2}/agent_login_history`, payload);
            if (data.success) {

                setActivityLogs(data.data);
            } else {
                setActivityLogs([])
            }

        } catch (error) {
            console.error(error);
            toast.error("Something went wrong. Please try again later.");

        } finally {
            setLoading(false);
        }
    }

    const handleDateChange = (dates) => {
       
        setDateRange(dates);
        if (dates[0] && dates[1]) {
            fetchActivityLog();
            fetchAgentData();

        }

    };

    const handleShowMobile = async () => {
        if (!agentId) return;
        const newStatus = mobileVisible ? 0 : 1;

        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "number_visibility",
            agent_id: agentId,
            status: newStatus.toString(),
        };

        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);
            if (data.success) {
                setMobileVisible(prevState => !prevState);
                toast.success("Mobile number visibility updated.");
            } else {
                throw new Error("Failed to update.");
            }
        } catch (error) {
            console.error(error);
            toast.error("Error updating visibility.");
        }
    };

    const handleAssignKeyword = async () => {

        if (!currentUser || !currentUser.parent_id || !currentUser.parent_token || !agentId || !keywords.trim()) return;

        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "update_keyword",
            agent_id: agentId,
            keyword: keywords,
        };

        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);
            if (data.success) {
                toast.success("Keywords assigned successfully.");
                setAddedKeywords([...addedKeywords, keywords]);
                setKeywords("");
            } else {
                throw new Error("Failed to assign keywords.");
            }
        } catch (error) {
            console.error(error);
            toast.error("Error assigning keywords.");
        }

    };

    const handleRemoveKeyword = async (index, kw) => {
        if (!currentUser || !currentUser.parent_id || !currentUser.parent_token || !agentId) return;
        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "delete_keyword",
            agent_id: agentId,
            keyword: kw,

        }

        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);
            if (data.success) {
                const updatedKeywords = addedKeywords.filter((_, i) => i !== index);
                setAddedKeywords(updatedKeywords);
                toast.success("Keyword removed successfully");
            }

        } catch (error) {
            console.log(error);
            toast.error("Something went wrong, please try again later");

        }


    };

    const handleSelectSuggestion = (suggestion) => {
        setKeywords(suggestion);
        setKeywordSuggestions([]); // Hide suggestions after selection
    };


    const handleCalendarToggle = () => {
        setShowCalendar(!showCalendar);
    };

    const formatActivityName = (name) => {
        const formattedNames = {
            agentlogin: "Agent Logged In",
            agentonline: "Agent Online",
            agentlogout: "Agent Logged Out",
            agentoffline: "Agent Offline",
        };
        return formattedNames[name] || name;
    };

    const getActivityBadgeClass = (activity) => {
        switch (activity) {
            case "agentlogin":
                return "bg-success text-white"; // Green for login
            case "agentonline":
                return "bg-primary text-white"; // Blue for online
            case "agentlogout":
                return "bg-danger text-white"; // Red for logout
            case "agentoffline":
                return "bg-secondary text-white"; // Gray for offline
            default:
                return "bg-dark text-white";
        }
    };

    const formatTime = (datetime) => {
        const date = new Date(datetime);
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
        });
    };

    return (
        <div className={styles.pageContainer}>
            <LeftMenu />
            <div className={`d-flex flex-column flex-lg-row justify-content-center gap-3 align-items-center w-100 ${styles.main}`}>
                {/* <div className="d-flex justify-content-between align-items-center w-100 mb-3">
                    <h3>Agent Details</h3>
                    
                </div> */}

                {/* 1) If no agentId is provided */}
                {!agentId ? (
                    <div className="text-center">
                        <h4 className="text-muted">No Agent Selected</h4>
                        <p>Please select an agent to view details.</p>
                    </div>
                ) : loading ? (
                    /* 2) If loading data */
                    <div className="text-center">
                        <p>Loading agent data...</p>
                    </div>
                ) : (
                    /* 3) If data is loaded */
                    <>
                        {/* Agent Personal Info */}

                        <div className={`${styles.profileCard} ${styles.card}`}>
                            <div className="d-flex flex-lg-column w-100 justify-content-center align-itmes-center">
                                <div className="flex-shrink-0 chat-user-img online user-own-img align-self-center me-2 ms-0 mb-4" >
                                    <svg
                                        viewBox="0 0 212 212"
                                        height="212"
                                        width="212"
                                        preserveAspectRatio="xMidYMid meet"
                                        className="xh8yej3 x5yr21d"
                                        version="1.1"
                                        x="0px"
                                        y="0px"
                                        enableBackground="new 0 0 212 212"
                                    >
                                        <title>default-user</title>
                                        <path
                                            fill="#DFE5E7"
                                            className="background"
                                            d="M106.251,0.5C164.653,0.5,212,47.846,212,106.25S164.653,212,106.25,212C47.846,212,0.5,164.654,0.5,106.25 S47.846,0.5,106.251,0.5z"
                                        ></path>
                                        <g>
                                            <path
                                                fill="#FFFFFF"
                                                className="primary"
                                                d="M173.561,171.615c-0.601-0.915-1.287-1.907-2.065-2.955c-0.777-1.049-1.645-2.155-2.608-3.299 c-0.964-1.144-2.024-2.326-3.184-3.527c-1.741-1.802-3.71-3.646-5.924-5.47c-2.952-2.431-6.339-4.824-10.204-7.026 c-1.877-1.07-3.873-2.092-5.98-3.055c-0.062-0.028-0.118-0.059-0.18-0.087c-9.792-4.44-22.106-7.529-37.416-7.529 s-27.624,3.089-37.416,7.529c-0.338,0.153-0.653,0.318-0.985,0.474c-1.431,0.674-2.806,1.376-4.128,2.101 c-0.716,0.393-1.417,0.792-2.101,1.197c-3.421,2.027-6.475,4.191-9.15,6.395c-2.213,1.823-4.182,3.668-5.924,5.47 c-1.161,1.201-2.22,2.384-3.184,3.527c-0.964,1.144-1.832,2.25-2.609,3.299c-0.778,1.049-1.464,2.04-2.065,2.955 c-0.557,0.848-1.033,1.622-1.447,2.324c-0.033,0.056-0.073,0.119-0.104,0.174c-0.435,0.744-0.79,1.392-1.07,1.926 c-0.559,1.068-0.818,1.678-0.818,1.678v0.398c18.285,17.927,43.322,28.985,70.945,28.985c27.678,0,52.761-11.103,71.055-29.095 v-0.289c0,0-0.619-1.45-1.992-3.778C174.594,173.238,174.117,172.463,173.561,171.615z"
                                            ></path>
                                            <path
                                                fill="#FFFFFF"
                                                className="primary"
                                                d="M106.002,125.5c2.645,0,5.212-0.253,7.68-0.737c1.234-0.242,2.443-0.542,3.624-0.896 c1.772-0.532,3.482-1.188,5.12-1.958c2.184-1.027,4.242-2.258,6.15-3.67c2.863-2.119,5.39-4.646,7.509-7.509 c0.706-0.954,1.367-1.945,1.98-2.971c0.919-1.539,1.729-3.155,2.422-4.84c0.462-1.123,0.872-2.277,1.226-3.458 c0.177-0.591,0.341-1.188,0.49-1.792c0.299-1.208,0.542-2.443,0.725-3.701c0.275-1.887,0.417-3.827,0.417-5.811 c0-1.984-0.142-3.925-0.417-5.811c-0.184-1.258-0.426-2.493-0.725-3.701c-0.15-0.604-0.313-1.202-0.49-1.793 c-0.354-1.181-0.764-2.335-1.226-3.458c-0.693-1.685-1.504-3.301-2.422-4.84c-0.613-1.026-1.274-2.017-1.98-2.971 c-2.119-2.863-4.646-5.39-7.509-7.509c-1.909-1.412-3.966-2.643-6.15-3.67c-1.638-0.77-3.348-1.426-5.12-1.958 c-1.181-0.355-2.39-0.655-3.624-0.896c-2.468-0.484-5.035-0.737-7.68-0.737c-21.162,0-37.345,16.183-37.345,37.345 C68.657,109.317,84.84,125.5,106.002,125.5z"
                                            ></path>
                                        </g>
                                    </svg>
                                </div>
                                <div className="d-flex flex-column w-100 justify-content-center align-items-center">
                                    <h4>{agentData[0]?.agentname || "No Name"}</h4>
                                    <p className="text-primary">
                                        <FaUser /> Agent
                                    </p>
                                </div>
                                {/* <div className="flex-grow-1  d-flex justify-content-end align-items-start">

                                    <Link to={`/agent-management/agent/setting/?id=${agentId}&name=${agentData[0]?.agentname}`}>
                                        <IoMdSettings className="text-black" size={20} /></Link>

                                </div> */}

                            </div>

                        </div>

                        <div className={`${styles.detailCard} ${styles.card}`}>

                            <div className="d-flex justify-content-between align-items-center w-100">
                                <ul className={`nav nav-tabs ${styles.tabsContainer}`}>
                                    <li className="nav-item">
                                        <button
                                            className={`${styles.navLink} ${activeTab === "overview" ? styles.active : ""}`}
                                            onClick={() => setActiveTab("overview")}
                                        >
                                            Overview
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`${styles.navLink} ${activeTab === "activity" ? styles.active : ""}`}
                                            onClick={() => setActiveTab("activity")}
                                        >
                                            Activity
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`${styles.navLink} ${activeTab === "settings" ? styles.active : ""}`}
                                            onClick={() => setActiveTab("settings")}
                                        >
                                            Settings
                                        </button>
                                    </li>

                                </ul>

                                <div className="d-flex align-items-center justify-content-end w-25 gap-2">
                                    <FaCalendarAlt className="text-secondary" size={24} />
                                    <DatePicker
                                        selectsRange
                                        startDate={fromDate}
                                        endDate={toDate}
                                        onChange={(update) => handleDateChange(update)}
                                        maxDate={fromDate instanceof Date ? new Date(fromDate.getFullYear(), fromDate.getMonth() + 1, fromDate.getDate()) : new Date()}
                                        placeholderText="Select a date range"
                                        className="form-control"
                                    />

                                </div>

                            </div>
                            <div className=" p-3 " style={{ height: "95%" }} >
                                {activeTab === "overview" && <>
                                    <div className="d-flex justify-content-between align-items-center">
                                        <div className="d-flex align-items-center gap-2">
                                            <h5 className="mb-0">Chat Statistics</h5>


                                        </div>

                                    </div>

                                    <div className="row mt-3">
                                        <div className="col-6 col-md-3 mb-3">
                                            <p>
                                                <FaUser className="text-primary" /> Total Chats
                                            </p>
                                            <h4>{totalChats}</h4>
                                        </div>
                                        <div className="col-6 col-md-3 mb-3">
                                            <p>
                                                <FaCheck className="text-success" /> Read Chats
                                            </p>
                                            <h4>{totalChats - unreadChats}</h4>
                                        </div>
                                        <div className="col-6 col-md-3 mb-3">
                                            <p>
                                                <FaTimes className="text-danger" /> Unread Chats
                                            </p>
                                            <h4>{unreadChats}</h4>
                                        </div>
                                        <div className="col-6 col-md-3 mb-3">
                                            <p>
                                                <FaRedo className="text-primary" /> Repeat Chats
                                            </p>
                                            <h4>{repeatChats}</h4>
                                        </div>

                                    </div>

                                </>}
                                {activeTab === "activity" &&
                                    <ul className={styles.activityList}>
                                        {activityLogs.length > 0 ? (
                                            activityLogs.map((log) => (
                                                <li key={log.id} className="d-flex justify-content-between align-items-center p-2 border-bottom">
                                                    <div>
                                                        <span className={`badge ${getActivityBadgeClass(log.activity_name)}`}>
                                                            {formatActivityName(log.activity_name)}
                                                        </span>
                                                        <div className="mt-1">
                                                            <strong>{log.agent_name}</strong> <small className="text-muted">({log.ip})</small>
                                                        </div>
                                                    </div>
                                                    <span className="text-muted">{formatTime(log.login_date)}</span>
                                                </li>
                                            ))
                                        ) : (
                                            <li className="text-muted text-center py-3">No activity logs found.</li>
                                        )}
                                    </ul>
                                }
                                {activeTab === "settings" &&
                                    <div className={`w-100`}>

                                        <div className="w-100 d-flex align-items-center">
                                            <label className="me-3">Show Mobile Number</label>
                                            <Switch checked={mobileVisible} onChange={handleShowMobile} color="primary" />
                                        </div>

                                        {/* Keyword Management */}
                                        <div className="d-flex flex-column w-100 gap-2 mt-3">
                                            <label>Keyword Management</label>
                                            <div className="d-flex flex-column flex-md-row w-100 justify-content-between align-items-center gap-3">
                                                <div className={styles.keywordInputContainer}>

                                                    <input
                                                        type="text"
                                                        placeholder="Add keyword"
                                                        className={`form-control ${styles.keywordInput}`}
                                                        value={keywords}
                                                        onChange={(e) => setKeywords(e.target.value)}
                                                        onKeyDown={(e) => e.key === "Enter" && handleAssignKeyword()}
                                                    />


                                                    {keywordSuggestions.length > 0 && (
                                                        <ul className={styles.suggestionList}>
                                                            {keywordSuggestions.map((suggestion, index) => (
                                                                <li
                                                                    key={index}
                                                                    className={styles.suggestionItem}
                                                                    onClick={() => handleSelectSuggestion(suggestion)}
                                                                >
                                                                    {suggestion}
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    )}
                                                </div>

                                                <button className={`btn btn-primary ${styles.addBtn}`} onClick={handleAssignKeyword}>
                                                    Add Keyword
                                                </button>

                                            </div>


                                            <div className={styles.keywordContainer}>
                                                {addedKeywords.length > 0 && addedKeywords[0] !== "" ? (
                                                    addedKeywords.map((kw, index) => (
                                                        <span key={index} className={styles.keywordBadge}>
                                                            {kw}
                                                            <button className={styles.removeBtn} onClick={() => handleRemoveKeyword(index, kw)}>
                                                                &times;
                                                            </button>
                                                        </span>
                                                    ))
                                                ) : (
                                                    <div className="text-muted w-100 p-2"
                                                        style={{ minHeight: "200px", backgroundColor: "whitesmoke", borderRadius: ".5rem" }}>
                                                        No keywords added yet.
                                                    </div>
                                                )}
                                            </div>

                                        </div>
                                    </div>
                                }

                            </div>



                        </div>

                        {/* Activity Calendar Modal */}
                        <Modal show={showCalendar} onHide={handleCalendarToggle} centered>
                            <Modal.Header closeButton>
                                <Modal.Title>Agent Availability Calendar</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <div className="d-flex justify-content-center h-100">
                                    <Calendar
                                        className={styles.reactCalendar}
                                        defaultValue={new Date(2023, 8, 1)} // Show September 2023
                                        tileContent={tileContent}
                                    />
                                </div>
                            </Modal.Body>
                        </Modal>
                    </>
                )}
            </div>
        </div>
    );
};

export default AgentDetailPage;
