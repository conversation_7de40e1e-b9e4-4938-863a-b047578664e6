import React from 'react';
import Select from 'react-select';


export default function AssignModal({
  isOpen,
  onClose,
  onAssign,
  modalType,
  options,
  selectedOption,
  setSelectedOption
}) {
  if (!isOpen) return null;

  return (
    <div className={`modal fade show d-block`} tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog">
        <div className="modal-content">

          <div className="modal-header">
            <h5 className="modal-title">
              Assign {modalType === 'agent' ? 'Agent' : 'Label'}
            </h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>

          <div className="modal-body">
            <Select
              options={options}
              value={selectedOption}
              onChange={setSelectedOption}
              placeholder={`Select a ${modalType}`}
              classNamePrefix="react-select"
            />
          </div>

          <div className="modal-footer">
            <button className="btn btn-outline-secondary" onClick={onClose}>
              Cancel
            </button>
            <button className="btn btn-primary" onClick={onAssign} disabled={!selectedOption}>
              Assign
            </button>
          </div>

        </div>
      </div>
    </div>
  );
}
