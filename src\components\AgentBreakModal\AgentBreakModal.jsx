import React, { useState, useEffect } from "react";
import { ChatState } from "../../context/AllProviders";
import { BASE_URL2 } from "../../api/api";
import axios from "axios";
import { toast } from "react-toastify";
const AgentBreakModal = ({ onClose, onConfirm, currentUser, agentId = null }) => {
  const { startBreak } = ChatState();
  const [breakType, setBreakType] = useState("break");
  const [time, setTime] = useState({ sec: 0, min: 0, hour: 0, day: 0 });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Convert entered time into total seconds
  const calculateTotalSeconds = () => {
    return time.sec + time.min * 60 + time.hour * 3600 + time.day * 86400;
  };

  const handleStartBreak = async () => {
    const totalSec = calculateTotalSeconds();

    // Validation for holiday and break type
    if (breakType === "holiday" && time.day > 15) {
      setError("Holiday cannot exceed 15 days.");
      return;
    }

    if (breakType === "break" && (time.min > 60 || time.min < 1)) {
      setError("Break duration must be between 1 and 60 minutes.");
      return;
    }
    setError("");
    const breakUntil = new Date(Date.now() + totalSec * 1000).toISOString();

    const payload = currentUser?.user_type === "agent" ? {
      user_id: currentUser.user_id,
      token: currentUser.token,
      method: "agent_break",
      mode: breakType,
      breakUntil,
      user_type:currentUser.user_type
    } : {
      user_id: currentUser.user_id,
      token: currentUser.token,
      method: "agent_break_by_parent",
      mode: breakType,
      breakUntil,
      agent_id: agentId,
      user_type:currentUser.user_type
    };

    setIsSubmitting(true);

    try {

      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent_setting`, payload);

      if (data.success && currentUser.user_type==="agent") {
        startBreak({ type: breakType, breakUntil, id: data.id });
      }
      toast.success("Break started")
      if(onConfirm){
        onConfirm();
      }else{
        onClose();
      }

    } catch (error) {
      console.log(error);
    }
    finally {
      setIsSubmitting(false)
    }
  };

  const handleTypeChange = (type) => {
    setBreakType(type);
    setError("");
    setTime({ sec: 0, min: 0, hour: 0, day: 0 });
  };

  const handleInputChange = (field, value) => {
    setTime({ ...time, [field]: parseInt(value) || 0 });
    setError("");
  };

  return (
    <div
      className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50"
      style={{ zIndex: 1050 }}
    >
      <div className="bg-white p-4 rounded-4 shadow-lg text-center" style={{ maxWidth: "450px", width: "90%" }}>
        <h2 className="h4 fw-bold mb-3 text-primary">Take a Break</h2>

        <div className="mb-3">
          <label className="form-label fw-semibold text-secondary">Select Break Type:</label>
          <div className="d-flex gap-3 justify-content-center">
            <div className="form-check">
              <input
                type="radio"
                className="form-check-input"
                id="break"
                name="breakType"
                value="break"
                checked={breakType === "break"}
                onChange={() => handleTypeChange("break")}
              />
              <label className="form-check-label" htmlFor="break">
                Break
              </label>
            </div>
            <div className="form-check">
              <input
                type="radio"
                className="form-check-input"
                id="holiday"
                name="breakType"
                value="holiday"
                checked={breakType === "holiday"}
                onChange={() => handleTypeChange("holiday")}
              />
              <label className="form-check-label" htmlFor="holiday">
                Holiday
              </label>
            </div>
          </div>
        </div>

        <div className="mb-3">
          <label className="form-label fw-semibold text-secondary">
            Set {breakType === "holiday" ? "Holiday" : "Break"} Duration:
          </label>

          <div className="d-flex gap-2 justify-content-between">
            {breakType === "holiday" ? (
              <div className="flex-fill">
                <label className="form-label small">Days</label>
                <input
                  type="number"
                  className="form-control text-center rounded-pill"
                  min="0"
                  max="15"
                  value={time.day}
                  onChange={(e) => handleInputChange("day", e.target.value)}
                />
              </div>
            ) : (
              <>
                <div className="flex-fill">
                  <label className="form-label small">Minutes</label>
                  <input
                    type="number"
                    className="form-control text-center rounded-pill"
                    min="1"
                    max="60"
                    value={time.min}
                    onChange={(e) => handleInputChange("min", e.target.value)}
                  />
                </div>
              </>
            )}
          </div>

          {error && <div className="text-danger mt-2 small fw-semibold">{error}</div>}
        </div>

        <button
          className="btn btn-success w-100 py-2 fw-semibold shadow-sm"
          onClick={handleStartBreak}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Setting Break..." : "Start Break"}
        </button>

        <button className="btn btn-outline-secondary w-100 mt-2 py-2 fw-semibold shadow-sm" onClick={onClose}>
          Close
        </button>
      </div>
    </div>

  );
};

export default AgentBreakModal;
