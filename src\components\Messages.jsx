import React, { useContext, useEffect, useRef, useState } from "react";
import { AuthContext } from "../context/AuthContext";
import { ChatContext } from "../context/ChatContext";
import Message from "./Message";
import { ChatState } from "../context/AllProviders";
import axios from "axios";
import { BASE_URL, BASE_URL2 } from "../api/api";
const Messages = () => {
  const [loadingnewmsg, setLoadingnewmsg] = useState(false);
  const [previousScrollHeight, setPreviousScrollerHeight] = useState(0);
  const scrollContainerRef = useRef(null);
  const [messages, setMessages] = useState([]);
  const { data } = useContext(ChatContext);
  const { convpage, setConvPage, setScrolarinmiddle,
    selectedName, selectedMobileNumber, selectedChannel, nextConvId, setNextConvId
  } = ChatState();
  const { dispatch } = useContext(ChatContext);
  const { currentUser } = useContext(AuthContext);
  useEffect(() => {
    setMessages(data.conversion);
    if (data.conversion.length > 30) {
      setTimeout(() => {
        scrollContainerRef.current.scrollTop =
          scrollContainerRef.current.scrollHeight - previousScrollHeight;
      }, 100);

    }

    setTimeout(() => {
      setLoadingnewmsg(false)
    }, 200)

  }, [data, previousScrollHeight]);

  const handleScroll = () => {
    const { scrollTop, clientHeight, scrollHeight } =
      scrollContainerRef.current;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1500;

    if (isAtBottom === false) {
      setScrolarinmiddle(true);
    } else {
      setScrolarinmiddle(false);
    }
    const isAtTop = scrollTop === 0;

    if (isAtTop) {
      if (loadingnewmsg === false) {
        let addmore = convpage + 30;
        updateConv();
        setConvPage(addmore);
        if (scrollContainerRef.current) {

          setPreviousScrollerHeight(scrollContainerRef.current.scrollHeight);
        }
        setLoadingnewmsg(true);
      }
    }
  };

  const updateConv = async () => {
    if (!currentUser.token || !currentUser.user_id || !nextConvId || !selectedChannel) return;
    try {
      const forconvdata = {
        token: currentUser.token,
        user_id: currentUser.user_id,
        user_type: currentUser.user_type,
        method: "conv",
        channel: selectedChannel,
        brand_number: currentUser.brand_number,
        last_id: { "whatsapp": nextConvId },
        from_mobile: selectedMobileNumber,
      };

      const res = await axios.post(
        `${BASE_URL2}/conversation`,
        forconvdata
      );

      if (res.data.success === true) {
        let updateddata = [...res.data.data];

        setNextConvId(res.data.next_id);

        dispatch({
          type: "CHANGE_USER",
          payload: {
            mobile: selectedMobileNumber,
            conversation: [...updateddata, ...data.conversion],
            name: selectedName,
          },
        });
      } else {
        setLoadingnewmsg(false);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  return (

    <ul
      className={`list-unstyled chat-conversation-list new-conv  w-100`}
      id="users-conversation"
      style={{
        maxHeight: "100%",
        overflowY: "auto",
        padding: "10px",
        flexGrow: 1,
      }}
      ref={scrollContainerRef}
      onScroll={handleScroll}
    >
      <Message chatData={messages} chatLoading={loadingnewmsg} />

    </ul>

  );
};

export default Messages;
