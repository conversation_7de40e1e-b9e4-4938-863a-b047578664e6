import styles from "./StageCard.module.css";

const StageCard = ({ id, title, description, onDelete, onUpdateStage, }) => {

    return (
        <div className={`${styles.card} position-relative`}
        >
            <div className={`${styles.header}`}>
                {title}
            </div>
            <hr className={styles.separator} />
            <div className={styles.body}>
                <div className="mb-3" >
                    <label htmlFor="">Name</label>
                    <input type="text" className="form-control"
                        value={title}
                        onChange={(e) =>
                            onUpdateStage(id, { name: e.target.value })
                        }
                    />
                </div>
                <div className="mb-3">
                    <label htmlFor="">Description</label>
                    <input type="text" className="form-control"
                        value={description}
                        onChange={(e) =>
                            onUpdateStage(id, { description: e.target.value })
                        }
                    />
                </div>
            </div>
            <div className={styles.footer}>
                <button
                    className="btn btn-outline-secondary"
                    onClick={() => onDelete(id)}
                >
                    <i className="mdi mdi-delete me-2" style={{ fontSize: "18px" }}></i>
                    Delete Stage
                </button>
            </div>
        </div>
    );
};

export default StageCard;
