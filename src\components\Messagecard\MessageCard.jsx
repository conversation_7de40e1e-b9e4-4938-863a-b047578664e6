import { IoCheckmarkDoneOutline, IoCheckmark, } from "react-icons/io5";
import { MdOutlineSmsFailed } from "react-icons/md";
import { formatTimestamp } from "../../utils/Utils";
import style from "./messageCard.module.css";
import MenuList from "../MenuList/MenuList";
import React, { useState, useRef, useEffect } from "react";
import { FaWpforms } from "react-icons/fa";
import { RiArrowDropDownLine } from "react-icons/ri";
import OrderModal from "./OrderModal";
import ReplyDropdown from "./ReplyDropdown";
import { ChatState } from "../../context/AllProviders";
import ReplyPreview from "../ReplyPreview/ReplyPreview";
const MessageCard = ({
  position,
  type,
  status,
  text,
  date,
  user,
  data,
  onClick,
  href,
  src,
  button,
  list,
  isReply,
  replyData,
  messageid,
  id,
  agent
}) => {
  const [listOpen, setListOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { setReply, reply } = ChatState();
  // console.log(id);

  const handleClick = () => {
    setListOpen(true);
  };

  const handleDropdownToggle = () => {
    setDropdownOpen(!dropdownOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };

    if (dropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownOpen]);

  const dropdownOptions = [
    {
      label: "Reply", action: () => setReply({
        id,
        message_content: text,
        message_id: messageid,
        message_type: type,
        req_from: user,
        file_url: "",
      })
    },
    // { label: "Copy", action: () => console.log("Copy clicked") },
    // { label: "Delete", action: () => console.log("Delete clicked") },
  ];

  return (
    <div
      className={`d-flex ${position === "left" ? "justify-content-start" : "justify-content-end"
        } mb-4  ${style["max-width-lg"]} ${style.messageCard}`}
    >
      <div
        className={`text-white p-2 rounded-3 shadow ${position === "left" ? "bg-light" : `${style.agentCard}`
          }`}
      >

        {isReply === 1 && (
          <ReplyPreview replied={true}
            reply={replyData} />


        )}

        {user !== "USER" && <div className={`d-flex ${user !== "USER" ? 'justify-content-between' : 'justify-content-end'} align-items-center 
         `}>
          {user !== "USER" ? (
            <div className={style.agentName}>
              {user === "BOT_REPLY"
                ? "Bot"
                : user === "AGENT_REPLY"
                  ? agent || "Agent"
                  : null}
            </div>
          ) : null}
          <div ref={dropdownRef}
            className={`${style.dropdownIcon}`}
            onClick={handleDropdownToggle}>
            {/* <RiArrowDropDownLine style={{ fontSize: "1.5rem" }} /> */}
            <ReplyDropdown right={user !== "USER"} isOpen={dropdownOpen} onClose={() => setDropdownOpen(false)} options={dropdownOptions} />
          </div>

        </div>}
        {/* User name */}



        {/* Content based on message type */}

        <div className={`d-flex align-items-start justify-content-between`}>
          {type === "TEXT" && (
            <span className={style.text}>
              {text.split("\n").map((line, index) => (
                <React.Fragment key={index}>
                  {line
                    .split(/(\*[^*]\*|_[^_]_+|~[^~]~)/) // Splitting based on single delimiters
                    .map((part, partIndex) => {
                      if (!part) return null;

                      if (part.startsWith("*") && part.endsWith("*") && part.length > 2) {
                        // Apply bold to the middle part and remove the surrounding '*'
                        return (
                          <strong key={partIndex}>
                            {part.slice(1, -1)} {/* Remove first and last '*' */}
                          </strong>
                        );
                      }
                      if (part.startsWith("_") && part.endsWith("_") && part.length > 2) {
                        // Apply italic to the middle part and remove the surrounding '_'
                        return (
                          <em key={partIndex}>
                            {part.slice(1, -1)} {/* Remove first and last '_' */}
                          </em>
                        );
                      }
                      if (part.startsWith("~") && part.endsWith("~") && part.length > 2) {
                        // Apply strikethrough to the middle part and remove the surrounding '~'
                        return (
                          <del key={partIndex}>
                            {part.slice(1, -1)} {/* Remove first and last '~' */}
                          </del>
                        );
                      }

                      // In case the part is just normal text
                      return <span key={partIndex}>{part}</span>;
                    })}
                  <br />
                </React.Fragment>
              ))}
            </span>
          )}


          {type === "IMAGE" && (
            <div
              onClick={onClick}
              style={{ cursor: "pointer", maxWidth: "150px", overflow: "hidden" }}
            >
              <img
                src={data.uri}
                alt="Img"
                className="img-fluid rounded"
                style={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                }}
              />
              <p className="text-dark mt-2">{text}</p>
            </div>
          )}

          {type === "VIDEO" && (
            <div onClick={onClick} style={{ cursor: "pointer" }}>
              <video
                src={data?.videoURL}
                controls
                className="img-fluid rounded"
              />
              <p className="text-dark mt-2">{text}</p>
            </div>
          )}

          {type === "AUDIO" && (
            <div style={{ width: "300px" }}>
              {" "}
              {/* Expand container width if needed */}
              <audio src={data?.audioURL} controls style={{ width: "100%" }} />
            </div>
          )}

          {type === "LOCATION" && (
            <div
              onClick={() => window.open({ href }, "_blank")}
              style={{ cursor: "pointer" }}
            >
              <img
                src={src}
                alt="Map"
                className="img-fluid rounded"
                style={{ maxWidth: "150px" }}
              />
              <p className="text-dark mt-2">{text}</p>
            </div>
          )}
          {type === "REQUEST_LOCATION" && (
            <div
              style={{ cursor: "pointer" }}
            >
              <p className="text-dark mt-2">{data}</p>
              <button className="btn btn-primary mt-2 w-100">
                Request Location
              </button>
            </div>
          )}

          {type === "DOCUMENT" && (
            <div
              onClick={onClick}
              style={{ cursor: "pointer", display: "flex", alignItems: "center" }}
            >
              <img
                src="https://cdn.pixabay.com/photo/2016/03/31/14/48/sheet-1292828_640.png"
                alt="Document"
                style={{
                  width: "40px",
                  height: "40px",
                  objectFit: "contain",
                  marginRight: "8px",
                }}
              />
              <span className="text-dark">{text}</span>
            </div>
          )}

          {type === "MENU" && (
            <div className="d-flex flex-column">
              <span className={style.text}>{text}</span>
              <button className="btn btn-primary mt-2" onClick={handleClick}>
                {button}
              </button>
            </div>
          )}
          {type === "CATALOGUE" && (
            <div className="d-flex align-items-center gap-1 ">
              <strong className="text-black" style={{ fontSize: ".8rem" }}>CATALOGUE: </strong>
              <span className={style.text}>{text}</span>
            </div>
          )}
          {type === "ORDER" && (
            <div className="d-flex flex-column text-black w-100">
              <strong className="mb-2">Order Details: </strong>
              <button className="btn btn-primary w-100" onClick={() => setIsModalOpen(true)}>
                View order
              </button>
              <OrderModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} orderData={text} />
            </div>
          )}
          {type === "CONTACTS" && (
            <div className="d-flex justify-content-start text-black align-items-center w-100">
              <div className="flex-shrink-0 chat-user-img online user-own-img align-self-center me-2 ms-0" >
                <svg
                  viewBox="0 0 212 212"
                  height="212"
                  width="212"
                  preserveAspectRatio="xMidYMid meet"
                  className="xh8yej3 x5yr21d"
                  version="1.1"
                  x="0px"
                  y="0px"
                  enableBackground="new 0 0 212 212"
                >
                  <title>default-user</title>
                  <path
                    fill="#DFE5E7"
                    className="background"
                    d="M106.251,0.5C164.653,0.5,212,47.846,212,106.25S164.653,212,106.25,212C47.846,212,0.5,164.654,0.5,106.25 S47.846,0.5,106.251,0.5z"
                  ></path>
                  <g>
                    <path
                      fill="#FFFFFF"
                      className="primary"
                      d="M173.561,171.615c-0.601-0.915-1.287-1.907-2.065-2.955c-0.777-1.049-1.645-2.155-2.608-3.299 c-0.964-1.144-2.024-2.326-3.184-3.527c-1.741-1.802-3.71-3.646-5.924-5.47c-2.952-2.431-6.339-4.824-10.204-7.026 c-1.877-1.07-3.873-2.092-5.98-3.055c-0.062-0.028-0.118-0.059-0.18-0.087c-9.792-4.44-22.106-7.529-37.416-7.529 s-27.624,3.089-37.416,7.529c-0.338,0.153-0.653,0.318-0.985,0.474c-1.431,0.674-2.806,1.376-4.128,2.101 c-0.716,0.393-1.417,0.792-2.101,1.197c-3.421,2.027-6.475,4.191-9.15,6.395c-2.213,1.823-4.182,3.668-5.924,5.47 c-1.161,1.201-2.22,2.384-3.184,3.527c-0.964,1.144-1.832,2.25-2.609,3.299c-0.778,1.049-1.464,2.04-2.065,2.955 c-0.557,0.848-1.033,1.622-1.447,2.324c-0.033,0.056-0.073,0.119-0.104,0.174c-0.435,0.744-0.79,1.392-1.07,1.926 c-0.559,1.068-0.818,1.678-0.818,1.678v0.398c18.285,17.927,43.322,28.985,70.945,28.985c27.678,0,52.761-11.103,71.055-29.095 v-0.289c0,0-0.619-1.45-1.992-3.778C174.594,173.238,174.117,172.463,173.561,171.615z"
                    ></path>
                    <path
                      fill="#FFFFFF"
                      className="primary"
                      d="M106.002,125.5c2.645,0,5.212-0.253,7.68-0.737c1.234-0.242,2.443-0.542,3.624-0.896 c1.772-0.532,3.482-1.188,5.12-1.958c2.184-1.027,4.242-2.258,6.15-3.67c2.863-2.119,5.39-4.646,7.509-7.509 c0.706-0.954,1.367-1.945,1.98-2.971c0.919-1.539,1.729-3.155,2.422-4.84c0.462-1.123,0.872-2.277,1.226-3.458 c0.177-0.591,0.341-1.188,0.49-1.792c0.299-1.208,0.542-2.443,0.725-3.701c0.275-1.887,0.417-3.827,0.417-5.811 c0-1.984-0.142-3.925-0.417-5.811c-0.184-1.258-0.426-2.493-0.725-3.701c-0.15-0.604-0.313-1.202-0.49-1.793 c-0.354-1.181-0.764-2.335-1.226-3.458c-0.693-1.685-1.504-3.301-2.422-4.84c-0.613-1.026-1.274-2.017-1.98-2.971 c-2.119-2.863-4.646-5.39-7.509-7.509c-1.909-1.412-3.966-2.643-6.15-3.67c-1.638-0.77-3.348-1.426-5.12-1.958 c-1.181-0.355-2.39-0.655-3.624-0.896c-2.468-0.484-5.035-0.737-7.68-0.737c-21.162,0-37.345,16.183-37.345,37.345 C68.657,109.317,84.84,125.5,106.002,125.5z"
                    ></path>
                  </g>
                </svg>
              </div>
              <div className="d-flex flex-1 flex-column" style={{ fontSize: ".8rem" }}>
                <div>
                  {text?.name?.formatted_name}
                </div>
                <div>
                  {text?.phones[0]?.phone}
                </div>
              </div>

            </div>
          )}
          {type === "FLOW" && user === "BOT_REPLY" ? (
            <div>
              <span className={style.text}>
                {data?.content?.split("\n").map((line, index) => (
                  <React.Fragment key={index}>
                    {line}
                    <br />
                  </React.Fragment>
                ))}
              </span>
              <button className="btn btn-primary mt-2 w-100">Continue</button>
            </div>
          ) : (
            type === "FLOW" && (
              <div className="d-flex flex-column justify-content-center ">
                <div className="d-flex w-100 align-items-center text-primary mb-2">
                  <FaWpforms />
                  <strong className="ml-2 ">Form Data</strong>
                </div>
                <div className={style.formDetailsCard}>
                  <ul className={style.listUnstyled}>
                    {Object.entries(data?.content).map(
                      ([label, value], index) => (
                        <li key={index} className={style.formDetailsItem}>
                          <strong>{label.replace(/_/g, " ")}: </strong> {value}
                        </li>
                      )
                    )}
                  </ul>
                </div>
              </div>
            )
          )}
          {user === "USER" &&
            <div ref={dropdownRef}
              className={`${style.dropdownIcon}`}
              onClick={handleDropdownToggle}>
              {/* <RiArrowDropDownLine style={{ fontSize: "1.5rem" }} /> */}
              <ReplyDropdown right={user !== "USER"} isOpen={dropdownOpen} onClose={() => setDropdownOpen(false)} options={dropdownOptions} />
            </div>

          }
        </div>

        {/* Time and checkmarks */}
        <footer className="d-flex justify-content-end align-items-center text-muted mt-1">
          <span className="me-1">{formatTimestamp(date)}</span>
          {status === "fail" ? (
            <MdOutlineSmsFailed />
          ) : status === "read" ? (
            <IoCheckmarkDoneOutline />
          ) : status === "received" ? (
            <IoCheckmarkDoneOutline />
          ) : status === "sent" ? (
            <IoCheckmark />
          ) : null}
        </footer>
      </div>
      {listOpen && (
        <MenuList list={list} button={button} setListOpen={setListOpen} />
      )}
    </div>
  );
};

export default MessageCard;
