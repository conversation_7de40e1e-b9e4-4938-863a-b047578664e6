import { useState, useContext, useEffect, useRef } from "react";
import { AuthContext } from "../../context/AuthContext";
import style from './broadcast.module.css'
import axios from "axios";
import { BASE_URL3 } from "../../api/api";
import TemplatePrev from "../TemplatePrev";
import bg_whatsapp from "../../assets/mob-bg.png";
import { ChatState } from "../../context/AllProviders";
import { toast } from "react-toastify";
import { CircularProgress } from "@mui/material";
import { SlCalender } from "react-icons/sl";
import { formatTimestamp } from "../../utils/Utils";
import Select from "react-select";

const Broadcast = ({ setSelectedContacts, setTemplatePopUp, headers, selectedContacts }) => {
    const [campaignName, setCampaignName] = useState('');
    const [loading, setLoading] = useState(false);
    const [templateList, setTemplateList] = useState([]);
    const [filteredTemplateList, setFilteredTemplateList] = useState([]);
    const [templatePreView, setTemplatePreView] = useState(null);
    const [templateData, setTemplateData] = useState(null);
    const [headVar, setHeadVar] = useState([]);
    const [bodyVar, setBodyVar] = useState([]);
    const [bodyValue, setBodyValue] = useState(
        bodyVar ? Array(bodyVar.length).fill("") : 0
    );
    const [headValue, setHeadValue] = useState(
        headVar ? Array(headVar.length).fill("") : 0
    );
    const [loadingBtn, setLoadingBtn] = useState(false);
    const [variableTypes, setVariableTypes] = useState([]);

    const { currentUser } = useContext(AuthContext);
    const { wpProfile } = ChatState();
    const [parameter, setParameter] = useState({
        body: {},
        header: {},
    });

    const [headVariableType, setHeadVariableType] = useState("");
    const [countryCode, setCountryCode] = useState("");
    const [mediaUrl, setMediaUrl] = useState("");
    const [isSchedule, setIsSchedule] = useState(false);
    const [scheduleDate_time, setScheduleDate_time] = useState(null);
    const [step, setStep] = useState(1);
    const formRef = useRef(null);

    useEffect(() => {
        if (currentUser.parent_id) {
            fetchTemplate();
        }
    }, [currentUser]);

    useEffect(() => {
        // Scroll to the bottom of the form whenever `headVar` or `bodyVar` changes
        if (formRef.current) {
            formRef.current.scrollIntoView({ behavior: "smooth" });
        }
    }, [headVar, bodyVar, mediaUrl, isSchedule]);

    const fetchTemplate = async () => {
        const datafortemplate = {
            token: currentUser.parent_token,
            user_id: currentUser.parent_id,
            method: "retrieve",
        };
        setLoading(true);
        try {
            const { data } = await axios.post(
                `${BASE_URL3}/whatsapp_template.php`,
                datafortemplate
            );
            if (data.success === true) {
                setFilteredTemplateList(filterTemplate(data.data));
            }
        } catch (error) {
            console.log(error.message);
            toast.error(error.message);
        }
        finally {
            setLoading(false);
        }
    };

    // const selectedTemplate = (e) => {
    //     if (e.target.value === "") {
    //         setTemplatePreView(null);
    //         setTemplateData(null);
    //         return;
    //     }
    //     setHeadVar([]);
    //     setBodyVar([]);
    //     setBodyValue([])
    //     setHeadValue([]);
    //     setParameter({
    //         body: {},
    //         header: {},
    //     });
    //     setHeadVariableType("");
    //     setVariableTypes([]);
    //     setMediaUrl("");
    //     const selectedTempId = parseInt(e.target.value);

    //     const tempDtl = filteredTemplateList.filter((list) => list.id === selectedTempId);

    //     let urlbtn = [];
    //     let urlbtn2 = [];
    //     let callbtn = [];
    //     let buttonData = JSON.parse(tempDtl[0].temp_button);

    //     buttonData.forEach((item, index) => {
    //         let key = Object.keys(item);
    //         if (key.includes("urltext2")) {
    //             urlbtn2[0] = item;
    //         }
    //         if (key.includes("urltext")) {
    //             urlbtn[0] = item;
    //         }
    //         if (key.includes("phone")) {
    //             callbtn[0] = item;
    //         }
    //     });
    //     let PreviewTemplateData = {
    //         wid: tempDtl[0].id,
    //         tampleName: tempDtl[0].temp_name,
    //         language: tempDtl[0].temp_language,
    //         accountType: tempDtl[0].temp_category,
    //         templateType: tempDtl[0].temp_type,
    //         headerText: tempDtl[0].temp_header,
    //         headerOptions: tempDtl[0].temp_header === "" ? false : true,
    //         footerText: tempDtl[0].temp_footer,
    //         footerOptions: tempDtl[0].temp_header === "" ? false : true,
    //         temp_button: tempDtl[0].temp_button,
    //         bodyMessage: tempDtl[0].temp_body,
    //         button: tempDtl[0].temp_button,
    //         temp_status: tempDtl[0].temp_status,
    //         Urlbutton: urlbtn,
    //         Urlbutton2: urlbtn2,
    //         Callbutton: callbtn,
    //         quickButton:
    //             tempDtl[0].temp_button.length > 0 &&
    //                 tempDtl[0].temp_button[0].actionType === "quickReply"
    //                 ? [tempDtl[0].temp_button[0]]
    //                 : "",
    //         temp_attribute: [],
    //         isLoading: false,
    //     };
    //     const hd = JSON.parse(tempDtl[0].temp_header);

    //     let headdata;
    //     if (hd.text) {
    //         const headVariables = hd.text.match(/\{\{(\d+)\}\}/g);
    //         setHeadVar(headVariables);
    //         headdata = headVariables
    //             ? formatMessage(hd.text, headVariables, headValue)
    //             : hd.text;
    //     }
    //     if (hd.image) {
    //         setMediaUrl(hd.image)
    //     }

    //     const bodyVariables = tempDtl[0].temp_body.match(/\{\{(\d+)\}\}/g);

    //     setBodyVar(bodyVariables);

    //     setTemplateData(PreviewTemplateData);
    //     const bodydata = bodyVariables
    //         ? formatMessage(PreviewTemplateData.bodyMessage, bodyVariables, bodyValue)
    //         : PreviewTemplateData.bodyMessage;

    //     setTemplatePreView({
    //         ...PreviewTemplateData,
    //         bodyMessage: bodydata,
    //         headerText: hd.text ? { text: headdata } : hd,
    //     });
    // };

    const selectedTemplate = (e) => {
        if (!e) {
            setTemplatePreView(null);
            setTemplateData(null);
            return;
        }

        setHeadVar([]);
        setBodyVar([]);
        setBodyValue([]);
        setHeadValue([]);
        setParameter({
            body: {},
            header: {},
        });
        setHeadVariableType("");
        setVariableTypes([]);
        setMediaUrl("");

        const selectedTempId = parseInt(e);
        const tempDtl = filteredTemplateList.filter((list) => list.id === selectedTempId);

        if (!tempDtl.length) {
            console.error("Template not found");
            return;
        }

        let urlbtn = [];
        let urlbtn2 = [];
        let callbtn = [];

        let buttonData = [];
        try {
            buttonData = tempDtl[0].temp_button ? JSON.parse(tempDtl[0].temp_button) : [];
        } catch (error) {
            console.error("Error parsing temp_button:", error);
            buttonData = [];
        }


        buttonData.forEach((item) => {
            let key = Object.keys(item);
            if (key.includes("urltext2")) urlbtn[0] = item;
            if (key.includes("urltext")) urlbtn2[0] = item;
            if (key.includes("phone")) callbtn[0] = item;
        });

        let PreviewTemplateData = {
            wid: tempDtl[0].id,
            tampleName: tempDtl[0].temp_name,
            language: tempDtl[0].temp_language,
            accountType: tempDtl[0].temp_category,
            templateType: tempDtl[0].temp_type,
            headerText: tempDtl[0].temp_header,
            headerOptions: tempDtl[0].temp_header !== "",
            footerText: tempDtl[0].temp_footer,
            footerOptions: tempDtl[0].temp_footer !== "",
            temp_button: tempDtl[0].temp_button,
            bodyMessage: tempDtl[0].temp_body,
            button: tempDtl[0].temp_button,
            temp_status: tempDtl[0].temp_status,
            Urlbutton: urlbtn,
            Urlbutton2: urlbtn2,
            Callbutton: callbtn,
            quickButton:
                buttonData.length > 0 && buttonData[0]?.actionType === "quickReply"
                    ? [buttonData[0]]
                    : "",
            temp_attribute: [],
            isLoading: false,
        };

        let headdata = "";
        let hd = {};

        try {
            hd = tempDtl[0].temp_header ? JSON.parse(tempDtl[0].temp_header) : {};
        } catch (error) {
            console.error("Error parsing temp_header:", error);
            hd = {};
        }

        if (hd.text) {
            const headVariables = hd.text.match(/\{\{(\d+)\}\}/g);
            setHeadVar(headVariables);
            headdata = headVariables
                ? formatMessage(hd.text, headVariables, headValue)
                : hd.text;
        }

        if (hd.image) {
            setMediaUrl(hd.image);
        }

        const bodyVariables = tempDtl[0].temp_body
            ? tempDtl[0].temp_body.match(/\{\{(\d+)\}\}/g)
            : null;

        setBodyVar(bodyVariables);

        const bodydata = bodyVariables
            ? formatMessage(PreviewTemplateData.bodyMessage, bodyVariables, bodyValue)
            : PreviewTemplateData.bodyMessage;

        setTemplateData(PreviewTemplateData);
        setTemplatePreView({
            ...PreviewTemplateData,
            bodyMessage: bodydata,
            headerText: hd.text ? { text: headdata } : hd,
        });
    };

    const replaceBodyVal = (index, value) => {
        const updatedValues = [...bodyValue];
        const headerValue = selectedContacts[0][value];
        if (headerValue) {
            updatedValues[index] = selectedContacts[0][value];
        } else {
            updatedValues[index] = value;
        }
        setBodyValue(updatedValues);
        createParameterMapping(index, value, "body");
        const bodydata = formatMessage(
            templateData.bodyMessage,
            bodyVar,
            updatedValues
        );
        setTemplatePreView((preState) => ({
            ...preState,
            bodyMessage: bodydata,
        }));

    };

    const filterTemplate = (templateList) => {
        return templateList.filter((template) => {
            if (template.temp_type === "Carousel") {
                return false;
            }
            try {
                const header = JSON.parse(template.temp_header);

                if (header) {
                    const headVariables = header?.text?.match(/\{\{(\d+)\}\}/g);
                    return !headVariables; // Keep templates that do not have variables
                }
            } catch (error) {
                // console.error("Invalid JSON in temp_header:", template.temp_header);
            }
            return true; // If JSON parsing fails, keep the template
        });
    };

    const createParameterMapping = (index, value, section) => {
        const updatedBody = { ...parameter.body };

        // Check if the value exists in headers (add `#` if it does)
        const isHeader = headers.some((header) => header.key === value);
        updatedBody[index + 1] = isHeader ? `#${value}#` : value;

        // Update the parameter state
        if (section === "body") {
            setParameter((prevState) => ({
                ...prevState,
                body: updatedBody
            }));

        } else if (section === "header") {
            setParameter((prevState) => ({
                ...prevState,
                header: updatedBody
            }));

        }
    };


    const sendTemp = async (e) => {
        e.preventDefault();
        setLoadingBtn(true);

        const payload = {
            country_code: "91",
            template_id: templateData?.wid,
            media_url: mediaUrl,
            camp_name: campaignName,
            brand_number: currentUser.brand_number,
            message: templatePreView?.bodyMessage,
            total_count: selectedContacts.length,
            parameter: parameter,
            data: selectedContacts,
            is_schedule: isSchedule,
            schedule_datetime: scheduleDate_time,
            user_id: currentUser.parent_id,
            method: "agent_broadcast",
            token: currentUser.parent_token,
            channel: "whatsapp",
            created_by: "AGENT",
            created_by_name: currentUser.name,
            created_by_id: currentUser.user_id

        }
        try {

            const response = await axios.post(`${BASE_URL3}/bulk_campaign_sms.php`, payload);
            if (response.data.success) {
                setSelectedContacts([]);
                toast.success("Broadcast sent successfully");

            } else {
                toast.error(response.data.message)
            }

        } catch (error) {
            toast.error(error.message);
        }
        finally {
            setLoadingBtn(false);
            setTemplatePopUp(false);

        }
    };

    const replaceHeadVal = (index, value) => {
        const updatedValues = [...headValue];
        const headerValue = selectedContacts[0][value];
        if (headerValue) {
            updatedValues[index] = selectedContacts[0][value];
        } else {
            updatedValues[index] = value;
        }
        setHeadValue(updatedValues);
        createParameterMapping(index, value, "header");
        const hdData = JSON.parse(templateData.headerText);

        const headdata = formatMessage(hdData.text, headVar, updatedValues);

        setTemplatePreView((prevState) => ({
            ...prevState,
            headerText: {
                ...prevState.headerText,
                text: headdata,
            },
        }));
    };

    const formatMessage = (msg, placeholders, inputValues) => {
        const parts = msg.split(/(\{\{[0-9]+\}\})/);
        return parts.map((part, index) => {
            const placeholderIndex = placeholders.indexOf(part);
            if (placeholderIndex !== -1) {
                return (
                    <span key={index} style={{ color: "red" }}>
                        {inputValues[placeholderIndex] || part}
                    </span>
                );
            }
            return part;
        });
    };



    const handleVariableTypeChange = (index, value) => {
        const updatedVariableTypes = [...variableTypes];
        updatedVariableTypes[index] = value;
        setVariableTypes(updatedVariableTypes);
    };

    const scheduleHandler = (e) => {
        if (e.target.checked === true) {
            setIsSchedule(true);
        } else {
            setIsSchedule(false);
        }
    }

    const handlePrev = () => {
        if (step !== 1) {
            setStep(prevState => prevState - 1);
        }

    }
    const handleNext = () => {
        if (step !== 2) {
            setStep(prevState => prevState + 1);
        }

    }

    const options = filteredTemplateList.map((item) => ({
        value: item.id,
        label: item.temp_name,
    }));


    return (
        <div className="mt-4 w-100" style={{ height: "100%" }}>
            <div className="card w-100">
                <div className="card-body">

                    <ul className={style.progressbar}>
                        <li className={step >= 1 ? style.active : ""}>Broadcast Details</li>
                        <li className={step >= 2 ? style.active : ""}>Preview</li>
                    </ul>

                </div>
            </div>

            <div className="container d-flex justify-content-between 
            align-items-start w-100 p-0" style={{ height: "70%" }}
            >
                {step === 1 ?
                    <div className=" d-flex flex-wrap w-100 h-100">
                        <form id="myForm"
                            onSubmit={handleNext}
                            className={`${style.campaignForm}`}
                        >
                            <div className="mb-2">
                                <label className="formlabel mb-2">Broadcast Title</label>
                                <input type="text" className="form-control"
                                    placeholder="Enter title"
                                    required
                                    onChange={(e) => setCampaignName(e.target.value)}
                                    value={campaignName}
                                />

                            </div>
                            {/* <div className="mb-2">
                            <label className="formlabel mb-2">Select Country</label>
                            <select name="" id="" className="form-control"
                                value={countryCode}
                                onChange={(e) => setCountryCode(e.target.value)}
                                required
                            >
                                <option value="">Select Country</option>
                                <option value="91">India</option>
                            </select>

                        </div> */}
                            <div>
                                <label className="formlabel mb-2">Select template</label>
                                <div className=" mb-3">
                                    {/* <select
                                        className="form-control"
                                        style={{ cursor: "pointer" }}
                                        onChange={selectedTemplate}
                                        required
                                        value={templatePreView?.wid}
                                    >
                                        <option value="">Select Template</option>
                                        {filteredTemplateList.map((item, index) => (
                                            <option key={index} value={item.id}>
                                                {item.temp_name}
                                            </option>
                                        ))}
                                    </select> */}
                                    <Select
                                        options={options}
                                        onChange={(selectedOption) => selectedTemplate(selectedOption.value)}
                                        value={options.find(option => option.value === templatePreView?.wid)}
                                        placeholder="Select Template"
                                        isSearchable
                                        styles={{
                                            control: (provided) => ({
                                                ...provided,
                                                cursor: "pointer",
                                            }),
                                        }}
                                    />
                                </div>
                            </div>
                            <div className="WhatBodyHgt">
                                {headVar?.length > 0 && (
                                    <div className="mb-3 px-2 py-2 bgGray">
                                        <label className="formlabel">Head Variable</label>
                                        <div className="input-group">
                                            <span className="input-group-text" id="basic-addon1">
                                                {"{{1}}"}
                                            </span>

                                            <select
                                                name=""
                                                id=""
                                                className="form-select"
                                                value={headVariableType}
                                                onChange={(e) => setHeadVariableType(e.target.value)}
                                                required
                                            >
                                                <option value="">Select Type</option>
                                                <option value="static">Static</option>
                                                <option value="dynamic">Dynamic</option>
                                            </select>
                                            {headVariableType === "dynamic" ?
                                                <select name="" id=""
                                                    className="form-control"
                                                    onChange={(e) => replaceHeadVal(0, e.target.value)}
                                                    required
                                                >
                                                    <option value="">Select Column</option>
                                                    {headers.map((item) =>
                                                        <option value={item.key}>{item.label}</option>
                                                    )}

                                                </select>

                                                : headVariableType === "static" ? <input
                                                    type="text"
                                                    className="form-control"
                                                    placeholder="{{1}}"
                                                    onChange={(e) => replaceHeadVal(0, e.target.value)}
                                                    required
                                                /> : null}
                                        </div>
                                    </div>
                                )}
                                {bodyVar?.length > 0 && (
                                    <div className="mb-3 px-2 py-2 bgGray">
                                        <label className="formlabel mb-2">Body Variable</label>
                                        {bodyVar.map((item, i) => (
                                            <div className="input-group mb-2" key={i}>
                                                <span className="input-group-text" id="basic-addon1">
                                                    {item}
                                                </span>
                                                <select name="" id=""
                                                    className="form-select"
                                                    value={variableTypes[i]}
                                                    onChange={(e) => handleVariableTypeChange(i, e.target.value)}
                                                    required
                                                >
                                                    <option value="">Select Type</option>
                                                    <option value="static">Static</option>
                                                    <option value="dynamic">Dynamic</option>
                                                </select>
                                                {variableTypes[i] === 'static' ? <input
                                                    type="text"
                                                    className="form-control"
                                                    placeholder={item}
                                                    onChange={(e) => replaceBodyVal(i, e.target.value)}
                                                    required
                                                    value={bodyValue[i]}
                                                /> : variableTypes[i] === 'dynamic' ?
                                                    <select name="" id=""
                                                        className="form-select"
                                                        onChange={(e) => replaceBodyVal(i, e.target.value)}
                                                        required

                                                    >
                                                        <option value="">Select Column</option>
                                                        {headers.map((item) =>
                                                            <option value={item.key} key={item.key}>{item.label}</option>
                                                        )}

                                                    </select>

                                                    : null}
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {mediaUrl && <div>
                                    <label htmlFor="">Media Url</label>
                                    <input type="text" className="form-control"
                                        value={mediaUrl} onChange={(e) => setMediaUrl(e.target.value)}
                                    />
                                </div>
                                }
                                <div className="my-2 d-flex items-align-center">
                                    <label className={style.switch} style={{ marginRight: ".5rem" }}>
                                        <input
                                            className="form-check-input mb-1"
                                            value={isSchedule}
                                            onChange={scheduleHandler}
                                            type="checkbox"
                                        />
                                        <span className={style.slider}></span>
                                    </label>
                                    <strong>Schedule </strong>

                                </div>
                                {isSchedule &&

                                    <div className={`input-group mb-2`}>
                                        <div className="input-group-text">
                                            <SlCalender />
                                        </div>
                                        <input
                                            type="datetime-local"
                                            className="form-control"
                                            placeholder="Example: campaign for digital marketing"
                                            onChange={(e) => setScheduleDate_time(e.target.value)}
                                            required
                                            value={scheduleDate_time}
                                        />
                                    </div>
                                }
                            </div>
                            {loading && <div className="text-center">
                                <CircularProgress />
                            </div>
                            }

                            <div ref={formRef} />


                        </form>
                        <div className="w-50 d-none d-md-block">
                            {templatePreView ? (
                                <>
                                    {/* {selectedContacts.length > 1 && <div className="text-danger text-center">Preview is for one contact only </div>} */}
                                    <TemplatePrev previewData={templatePreView} />
                                </>
                            ) : (
                                <div className="whatsappPreview">
                                    <div
                                        className="whatsapp-review whatsapp-bg"
                                        style={{ backgroundImage: `url(${bg_whatsapp})` }}
                                    />
                                    <div className="whatsappFront">
                                        <div className="whats-app-header">
                                            <img alt="company img" src={wpProfile.image_url} />
                                            <div className="whatsappPTxt">
                                                {" "}
                                                <p>{wpProfile.comp_name} </p>{" "}
                                            </div>
                                        </div>
                                        <div className="msgTxt">
                                            <p className="text-right">
                                                Please select WhatsApp Template to preview
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>


                    </div>

                    : step === 2 ?
                        <div className="d-flex flex-column flex-md-row justify-content-between align-items-center w-100"
                            style={{
                                marginBottom: "1rem",
                                borderRadius: ".5rem", overflowY: "auto", maxHeight: "100%"
                            }}>

                            <div
                                className="details-section w-100 w-md-50 px-3 py-4"
                                style={{
                                    backgroundColor: "#f8f9fa", // Light gray background for contrast
                                    borderRadius: ".5rem",
                                    boxShadow: "0 2px 5px rgba(0, 0, 0, 0.1)", // Subtle shadow for depth
                                }}
                            >
                                <div className="mb-3 w-100">
                                    <label className="fw-bold d-block mb-1" style={{ color: "#495057" }}>
                                        Broadcast Title:
                                    </label>
                                    <span style={{ fontSize: "1rem", color: "#212529" }}>{campaignName}</span>
                                </div>

                                <div className="mb-3 w-100">
                                    <label className="fw-bold d-block mb-1" style={{ color: "#495057" }}>
                                        Template:
                                    </label>
                                    <span style={{ fontSize: "1rem", color: "#212529" }}>
                                        {templatePreView?.tampleName}
                                    </span>
                                </div>

                                <div className="mb-3 w-100">
                                    <label className="fw-bold d-block mb-1" style={{ color: "#495057" }}>
                                        Audience:
                                    </label>
                                    <span style={{ fontSize: "1rem", color: "#212529" }}>
                                        {selectedContacts.length}
                                    </span>
                                </div>

                                {isSchedule && (
                                    <div className="mb-3 w-100">
                                        <label className="fw-bold d-block mb-1" style={{ color: "#495057" }}>
                                            Schedule:
                                        </label>
                                        <span style={{ fontSize: "1rem", color: "#212529" }}>
                                            {formatTimestamp(scheduleDate_time)}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div> : null
                }

            </div>
            <div className="mt-2 d-flex justify-content-betwenn align-items-center w-100">
                <button className={`w-50 btn ${step === 1 ? 'btn-secondary' : 'btn-success'} mr-2`}
                    onClick={handlePrev}

                >
                    Previous
                </button>
                {step === 1 ?
                    <button className={`btn btn-success w-50`} type="submit" form="myForm">
                        Next
                    </button> :
                    loadingBtn ? (
                        <button className="btn btn-success w-100" type="button" disabled>
                            <span
                                className="spinner-border spinner-border-sm"
                                aria-hidden="true"
                            ></span>
                            <span role="status">Loading...</span>
                        </button>
                    ) : step === 2 ? <button className="btn w-50" style={{ backgroundColor: "#50b5ff", color: "white" }} onClick={sendTemp}>
                        Send
                    </button> : null
                }
            </div>
        </div>
    )

}

export default Broadcast;