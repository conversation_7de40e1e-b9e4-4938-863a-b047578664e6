import React, { useContext, useEffect, useRef, useState } from "react";
import { AuthContext } from "../context/AuthContext";
import Search from "./Search";
import Chats from "./Chats";
import Bookmark from "./bookmark/Bookmark";
import UserProfile from "./profile/UserProfile";
import { deleteCookie } from "../utils/Utils";
// import UnreadChats from "./UnreadChats";
import style from './sidebar.module.css';
import { ChatState } from "../context/AllProviders";
import axios from "axios";
import { BASE_URL, BASE_URL2 } from "../api/api";
import Select from "react-select";
import dayjs from "dayjs";
import { toast } from "react-toastify";
import { Link, useSearchParams, useNavigate, useLocation } from "react-router-dom";
import ChangePassword from "./ChangePassword";
import { FaClockRotate<PERSON>eft, FaFilter } from "react-icons/fa6";
import Reminder from "./Reminder/Reminder";
import useReminders from "../customHooks/useReminders";
import ReminderToasts from "./Reminder/RemiderToast";
import { MdSupportAgent } from "react-icons/md";
import { BsCalendarDate } from "react-icons/bs";
import DatePicker from "react-datepicker";
import { IoIosSettings } from "react-icons/io";
import { MdOutlineSpaceDashboard } from "react-icons/md";
import { Dropdown } from "react-bootstrap";
import { BsFilter } from "react-icons/bs";
import { AiOutlineClose } from "react-icons/ai";

const SideBar = ({ showAllReminder, setShowAllReminder, }) => {
  const [searchParams] = useSearchParams();
  const params = searchParams.get("tab");
  const disable = searchParams.get("disable");
  const encodedFilter = searchParams.get("filter");
  const chatFilter = encodedFilter ? atob(encodedFilter) : null;
  const urlQuery = params ? params : "";
  const navigate = useNavigate();
  const location = useLocation();

  const [isLoading, setIsLoading] = useState(false);
  const [agentList, setAgentList] = useState([]);
  const [selectedType, setSelectedType] = useState("");
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState([new Date().toISOString(), new Date().toISOString()]);
  const [fromDate, toDate] = dateRange;
  const [filterMode, setFilterMode] = useState(null);
  const datePickerRef = useRef(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [nextFilterId, setNextFilterId] = useState(null)
  const [selectedAgent, setSelectedAgent] = useState();
  const [noMoreChats, setNoMoreChats] = useState(false);
  const { currentUser } = useContext(AuthContext);
  const {
    unReadChat,
    setPage,
    page,
    channel,
    setChannel,
    chatCategory,
    setChatCategory,
    chatFilterType,
    setChatFilterType,
    roleFilterType,
    setRoleFilterType,
    chats,
    setAllChats,
    starChats,
    setStarChats,
    checkboxList,
    setCheckboxList,
    setChats,
    setChatsLoading,
    chatsLoading,
    socket,
    waitingChats,
    setWaitingChats,
    allChats,
    filteredChats,
    setSelectedMobileNumber,
    setSelectedUserDetails,
    setConvPage,
    agents,
    fetchAgents,
    unreadCount,
    setUnreadCount,


  } = ChatState();

  const { reminders, setReminders, loading, groupedReminders } = useReminders("retrieve_all");

  const scrollContainerRef = useRef(null);
  const modalRef = useRef(null);


  useEffect(() => {
    fetchAgents();
  }, [currentUser]);



  const handleReminder = () => {
    setShowAllReminder(prevState => !prevState);
  }


  const handleScroll = () => {
    const { scrollTop, clientHeight, scrollHeight } =
      scrollContainerRef.current;

    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1000;

    if (isAtBottom && !disable) {
      if (chatCategory === "star") {
        fetchStarredChats();
      } else {
        fetchChats(chatFilterType.value);

      }

    }
  };

  const fetchChats = async (agentId = "",) => {

    if (!currentUser || !currentUser.parent_token
      || !currentUser.parent_id || chatsLoading || disable || noMoreChats) return;
    setChatsLoading(true);
    if (nextFilterId === null) {
      setAllChats([]);
    }
    try {
      const payload = {
        token: currentUser.token,
        user_id: currentUser.user_id,
        method: "left_menu",
        channel: "whatsapp",
        filter: chatCategory,
        user_type: currentUser.user_type,
        filter_data: {
          type: (agentId === "All Agents" || agentId === "") ? "" : "agent",
          agent: agentId === "All Agents" ? "" : agentId,
          agent_type: roleFilterType || "agent",
        },
        next_id: nextFilterId,

      }
      const { data } = await axios.post(`${BASE_URL2}/conversation`, payload);
      if (data.success) {
        if (nextFilterId === null) {
          setAllChats(data.data);

          if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTop = 0;
          }

        } else {
          setAllChats((prevState) => [...prevState, ...data.data])
        }
        setNextFilterId(data.next_id);
        setUnreadCount(data.unread_count)
      } else {

        if (data.message === "data not found") {
          setNoMoreChats(true);
        }
      }

    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.")

      } else {
        console.error(error);
        toast.error("Something went wrong, please try again later")
      }

    } finally {
      setChatsLoading(false);
    }
  }

  const fetchStarredChats = async () => {
    if (!currentUser || !currentUser.parent_token
      || !currentUser.parent_id || chatsLoading || disable || noMoreChats) return;
    setChatsLoading(true);
    if (nextFilterId === null) {
      setAllChats([]);
    }
    try {
      const payload = {
        token: currentUser.token,
        user_id: currentUser.user_id,
        user_type: currentUser.user_type,
        method: "left_menu_stared",
        next_id: nextFilterId,

      }
      const { data } = await axios.post(`${BASE_URL2}/conversation`, payload);
      if (data.success) {
        if (nextFilterId === null) {
          setAllChats(data.data);

          if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTop = 0;
          }

        } else {
          setAllChats((prevState) => [...prevState, ...data.data])
        }
        setNextFilterId(data.next_id);
      } else {
        // 👇 stop further fetching on "data not found"
        if (data.message === "data not found") {
          setNoMoreChats(true);
        }
      }

    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.")

      } else {
        console.error(error);
        toast.error("Something went wrong, please try again later")
      }

    }
    finally {
      setChatsLoading(false);
    }
  }
  // useEffect(() => {
  //   const getWhatsaAppNumberList = async (page) => {
  //     if (currentUser.parent_id && !disable) {
  //       setChatsLoading(true);
  //       let datafornolist = {
  //         token: currentUser.parent_token,
  //         user_id: currentUser.parent_id,
  //         method: "left_menunew",
  //         start: 0,
  //         brand_number: currentUser.brand_number,
  //         user_type: currentUser.user_type,
  //         search_id: currentUser.user_id ? currentUser.user_id : "",
  //       };
  //       try {
  //         const { data } = await axios.post(
  //           `${BASE_URL}/netcore_conversation.php`,
  //           datafornolist
  //         );
  //         if (data.success === true) {
  //           const uniqueChats = Object.values(
  //             data.data.reduce((acc, item) => {
  //               acc[item.mobile] = item;
  //               return acc;
  //             }, {})
  //           );

  //           setChats(uniqueChats);
  //         }
  //         setChatsLoading(false);
  //       } catch (error) {
  //         console.error(error);
  //       }
  //     }
  //     if (currentUser.parent_id && disable) {
  //       setChats([...filteredChats])
  //     }
  //   };
  //   getWhatsaAppNumberList(page);
  // }, [currentUser, setChatsLoading, disable]);

  useEffect(() => {

    if (chatCategory === "star") {
      fetchStarredChats();
    } else {
      fetchChats(chatFilterType.value);

    }



  }, [currentUser, setChatsLoading, disable, chatCategory, chatFilterType.value])

  const getWhatsaAppNumberList = async (page) => {
    if (chatsLoading) return;
    if (currentUser.parent_id) {
      setChatsLoading(true);
      let datafornolist = {
        token: currentUser.parent_token,
        user_id: currentUser.parent_id,
        method: "left_menunew",
        start: page,
        brand_number: currentUser.brand_number,
        user_type: currentUser.user_type,
        search_id: currentUser.user_id ? currentUser.user_id : "",
      };
      try {
        const { data } = await axios.post(
          `${BASE_URL}/netcore_conversation.php`,
          datafornolist
        );

        if (data.success === true) {
          const combined = chats.length === 0
            ? data.data
            : [...chats, ...data.data];


          const uniqueChats = Object.values(
            combined.reduce((acc, item) => {
              acc[item.mobile] = item;
              return acc;
            }, {})
          );

          setChats(uniqueChats);
        }
        setChatsLoading(false);
      } catch (error) {
        console.error(error);
      }
    }
  };
  //socket
  useEffect(() => {
    // let socket = io(SOCKET_URL);
    // socket.emit("setup", currentUser);
    if (!socket) return;
    socket.on("online agent", (data) => {

      if (data?.user_type !== "admin") {
        const index = agentList.findIndex(
          (selecteditem) => selecteditem.value === data.user_id
        );


        if (index !== -1) {
          const updatedItems = [...agentList];
          updatedItems[index] = {
            ...updatedItems[index],

            label: `${data.name} (${"online"})`,
          };
          setAgentList(updatedItems);
        }
      }
    });
    socket.on("offline agent", (data) => {


      if (data?.user_type !== "admin") {
        const index = agentList.findIndex(
          (selecteditem) => selecteditem.value === data.user_id
        );


        const date = new Date();
        if (index !== -1) {
          const updatedItems = [...agentList];
          updatedItems[index] = {
            ...updatedItems[index],

            label: `${data.name} ( Last seen :${dayjs(date).format(
              "DD/MM/YYYY h:mm A"
            )})`,
          };


          setAgentList(updatedItems);
        }
      }
    });
    return () => {
      socket.off("online agent");
      socket.off("offline agent");
      // socket.disconnect();
    };
  }, [currentUser, agentList, socket]);

  useEffect(() => {
    const staredChats = async () => {
      if (currentUser.parent_id) {
        let dataforstarChats = {
          token: currentUser.parent_token,
          user_id: currentUser.parent_id,
          method: "get_star_list",
          brand_number: currentUser.brand_number,
          user_type: currentUser.user_type,
          agent_id: currentUser.user_id
        };
        try {
          const { data } = await axios.post(
            `${BASE_URL}/netcore_conversation.php`,
            dataforstarChats
          );

          if (data.success === true) {
            setStarChats(data.data);
          }
        } catch (error) {
          console.error(error);
        }
      }
    };
    staredChats();
  }, [currentUser, setStarChats]);

  const allBtn = () => {
    setChatCategory("all");
    setNextFilterId(null);
    setNoMoreChats(false);
  };
  const unreadBtn = () => {
    setChatCategory("unread");
    setNextFilterId(null);
    setNoMoreChats(false);
  };
  const starBtn = () => {
    setChatCategory("star");
    setNextFilterId(null);
    setNoMoreChats(false);
  };
  const waitingBtn = () => {
    setChatCategory("waiting");
    setAllChats(waitingChats);
    setNextFilterId(null);
  };
  const designationOptions = [
    { value: "agent", label: "Agent" },
    { value: "manager", label: "Manager" },
  ];
  const handleDesignation = (selected) => {
    setAgentList([]);
    setSelectedType(selected);
    fetchAgent(selected.value);
  };
  const fetchAgent = async (selectType) => {
    setIsLoading(true);
    const forAgentdata = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "retrieve",
      agent_type: selectType,
    };
    try {
      const { data } = await axios.post(
        `${BASE_URL2}/whatsapp_agent`,
        forAgentdata
      );

      if (data.success === true) {
        const newAgentList = data.data.map((item) => ({
          value: item.id,
          label: `${item.name} (${item.online === 0
            ? ` Last seen :${dayjs(item.last_seen_datetime).format(
              "DD/MM/YYYY h:mm A"
            )}`
            : "online"
            })`,
        }));
        setAgentList(newAgentList);
        setIsLoading(false);
      } else {
        setAgentList([]);
        setIsLoading(false);
      }
    } catch (error) {
      console.error(error.message);
    }

  };
  const handleAgent = (selected) => {
    setSelectedAgent(selected.value);
  };

  const handleAssignAgent = (data) => {

    const filteredChat = allChats.find(item => item.mobile === data.mobile);
    if (currentUser.user_type === "agent") {
      if (currentUser.user_id !== data.agent_id && filteredChat) {
        const newChats = allChats.filter(item => item.mobile !== data.mobile);
        setChats(newChats);
        setAllChats(newChats);
        setSelectedMobileNumber(null);
        setSelectedUserDetails({
          email: "",
          company: ""
        })
        setConvPage(0);

      } else if (currentUser.user_id === data.agent_id && !filteredChat) {
        setAllChats((prevState) => [...prevState, data])
        setChats((prevState) => [...prevState, data])

      }
    }
    if (currentUser.user_type === "manager") {
      if (currentUser.user_id !== data.manager_id && filteredChat) {
        const newChats = allChats.filter(item => item.mobile !== data.mobile);
        console.log(newChats);
        setChats(newChats);
        setAllChats(newChats);
        setSelectedMobileNumber(null);
        setSelectedUserDetails({
          email: "",
          company: ""
        })
        setConvPage(0);
      } else if (currentUser.user_id === data.manager_id && !filteredChat) {
        setAllChats((prevState) => [...prevState, data])
        setChats((prevState) => [...prevState, data])
      } else if (currentUser.user_id === data.manager_id && filteredChat) {
        console.log("run");

        setAllChats((prevState) => prevState.map((item) => {
          if (item.mobile === data.mobile && item.agent_id !== data.agent_id) {
            return { ...item, agent_name: data.agent_name, agent_id: data.agent_id }
          }
          return item;
        }))
        // setChats((prevState) => prevState.map((item) => {
        //   if (item.mobile === data.mobile && item.agent_id !== data.agent_id) {
        //     return { ...item, agent_name: data.agent_name, agent_id: data.agent_id }
        //   }
        //   return item;
        // }))

      }

    }
    if (currentUser.user_type === "team") {
      if (currentUser.user_id !== data.team_id && filteredChat) {
        const newChats = allChats.filter(item => item.mobile !== data.mobile);
        setChats(newChats);
        setSelectedMobileNumber(null);
        setSelectedUserDetails({
          email: "",
          company: ""
        })
        setConvPage(0);

      } else if (currentUser.user_id === data.team_id && !filteredChat) {
        setAllChats((prevState) => [...prevState, data])

      } else if (currentUser.user_id === data.team_id && filteredChat) {
        setAllChats((prevState) => prevState.map((item) => {
          if (item.mobile === data.mobile && item.agent_id !== data.agent_id) {
            return { ...item, agent_name: data.agent_name, agent_id: data.agent_id }
          }
          if (item.mobile === data.mobile && item.manager_id !== data.manager_id) {
            return { ...item, manager_name: data.manager_name, manager_id: data.manager_id }
          }

          return item;
        }))
      }

    }
    if (currentUser.user_type === "admin") {
      setAllChats((prevState) => prevState.map((item) => {
        if (item.mobile === data.mobile && item.agent_id !== data.agent_id) {
          return { ...item, agent_name: data.agent_name, agent_id: data.agent_id }
        }
        if (item.mobile === data.mobile && item.manager_id !== data.manager_id) {
          return { ...item, manager_name: data.manager_name, manager_id: data.manager_id }
        }
        if (item.mobile === data.mobile && item.team_id !== data.team_id) {
          return { ...item, team_name: data.team_name, team_id: data.team_id }
        }
        return item;
      }))

    }

  }
  const handleAssign = async (e) => {
    e.preventDefault();
    const forAssignAgent = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "chat_transfer",
      transfer_type: selectedType.value,
      transfer_to: selectedAgent,
      transfer_from: currentUser.user_id,
      chat_list: checkboxList.map((item) => item.mobile),
    };
    try {
      const { data } = await axios.post(
        `${BASE_URL2}/whatsapp_agent`,
        forAssignAgent
      );
      if (data.success === true) {
        checkboxList.forEach((item) => {
          handleAssignAgent({
            mobile: item.mobile,
            agent_id: selectedAgent,
            agent_name: agentList.find((a) => a.value === selectedAgent)?.label.split(" ")[0] || '',
            manager_id: currentUser.user_type === 'manager' ? currentUser.user_id : undefined,
            manager_name: currentUser.user_type === 'manager' ? currentUser.name : undefined,
            team_id: currentUser.user_type === 'team' ? currentUser.user_id : undefined,
            team_name: currentUser.user_type === 'team' ? currentUser.name : undefined
          });
        });
        setCheckboxList([]);
        toast.success(data.message);
        setIsLoading(false);
        setIsModalOpen(false);
      } else {
        toast.error(data.message);
        setIsLoading(false);
      }
    } catch (error) {
      toast.error(error.message);
      console.error(error.message);
    }
  };
  const handleAssignChat = () => {
    if (currentUser.user_type === "manager") {
      setSelectedType({ value: "agent", label: "Agent" })
      fetchAgent("agent");
    }
    setIsModalOpen(true);
  }

  useEffect(() => {
    const bootstrap = require('bootstrap/dist/js/bootstrap.bundle.min.js');
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map((tooltipTriggerEl) => new bootstrap.Tooltip(tooltipTriggerEl));
  }, []);


  const handleClearQuery = () => {
    if (disable && chatFilter) {
      navigate('/home')
    }
  }
  const logOut = async () => {
    try {
      const dataforlogout = {
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "logout",
        agent_id: currentUser.user_id,
        agent_name: currentUser.name,
        user_type: currentUser.user_type
      }
      axios.post(
        `${BASE_URL2}/whatsapp_user`,
        dataforlogout
      );
    } catch (error) {
      console.log(error);

    }
    deleteCookie("user");
  }

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target)) {
        setShowDatePicker(false);
      }
    };

    if (showDatePicker) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showDatePicker]);

  const handleAgentSelect = (data) => {

    setChatFilterType(data);
    setNextFilterId(null);
  }


  return (
    <div className={`d-flex flex-grow-1 justify-content-center align-items-center w-100 ${style['max-width-lg']}`}>
      <div className="side-menu w-100 w-lg-25 d-flex flex-grow-1 flex-lg-column">
        <div className="flex-lg-column my-0 sidemenu-navigation">
          <ul className="nav nav-pills side-menu-nav" role="tablist"

          >
            {/* <li
              className="nav-item d-none d-lg-block mb-3"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Profile"
            >
              <a
                className={`nav-link ${urlQuery === "user" ? "active" : ""}`}
                id="pills-user-tab"
                data-bs-toggle="pill"
                href="#pills-user"
                role="tab"
              >
                <i className="mdi mdi-account-outline text-dark" />
                <h6 className="iconicTxt">Profile</h6>
              </a>
            </li> */}
            {currentUser?.user_type !== "agent" && <li
              className="nav-item mb-3"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Dashboard"
            >
              <Link to="/dashboard" className={`nav-link ${location.pathname === "/dashboard" ? "active" : ""}`}>
                <MdOutlineSpaceDashboard className="text-dark" />

                <h6 className="iconicTxt">Dashboard</h6>
              </Link>
            </li>}
            <li
              className="nav-item"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Chats"
            >
              <a
                className={`nav-link ${urlQuery === "" ? "active" : ""}`}
                id="pills-chat-tab"
                data-bs-toggle="pill"
                href="#pills-chat"
                role="tab"
              >
                <i className="mdi mdi-message-text-outline text-dark" />
                <h6 className="iconicTxt">Chats</h6>
              </a>
            </li>
            {currentUser.user_type !== "agent" && (
              <li
                className="nav-item mb-3"
                data-bs-toggle="tooltip"
                data-bs-placement="right"
                data-bs-trigger="hover"
                data-bs-container=".sidemenu-navigation"
                title="Settings"
              >
                <Link to="/agent-management/agent" className="nav-link">
                  <IoIosSettings className="text-dark" />
                  <h6 className="iconicTxt">Settings</h6>
                </Link>
              </li>
            )}
            <li
              className="nav-item d-none d-md-block"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Broadcast"
            >
              <Link to="/broadcast" className="nav-link">
                <i className="mdi mdi-access-point text-dark"></i>

                <h6 className="iconicTxt">Broadcast</h6>
              </Link>
            </li>
            <li
              className="nav-item d-none d-md-block"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Reports"
            >
              <Link to="/report" className="nav-link">
                <i className="mdi mdi-file-chart text-dark"></i>
                <h6 className="iconicTxt">Reports</h6>
              </Link>
            </li>
            <li
              className="nav-item d-none d-md-block"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Leads"
            >
              <Link to="/leads-dashboard" className="nav-link">
                <i className="mdi mdi-account-multiple-outline text-dark"></i>
                <h6 className="iconicTxt">Leads</h6>
              </Link>
            </li>
            {/* <li
              className="nav-item"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Contacts"
            >
              <a
                className="nav-link"
                id="pills-contacts-tab"
                data-bs-toggle="pill"
                href="#pills-contacts"
                role="tab"
              >
                <div>
                  <i className="bx bx-conversation" />
                  <span className="position-absolute  start-1 translate-middle badge rounded-pill bg-danger" style={{top:"20px", height:"20px"}}>
                    <p style={{fontSize:"10px"}}>{unReadChat.length}</p>
                    <span class="visually-hidden">unread messages</span>
                  </span>
                </div>
              </a>
            </li>
            <li
              className="nav-item"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Bookmark"
              onClick={staredChats}
            >
              <a
                className="nav-link"
                id="pills-bookmark-tab"
                data-bs-toggle="pill"
                href="#pills-bookmark"
                role="tab"
              >
                <i className="bx bx-star" />
              </a>
            </li> */}

            <li className="nav-item marginAuto dropdown profile-user-dropdown">
              <div
                className="nav-link dropdown-toggle d-flex flex-column mb-2"
                role="button"
                data-bs-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i className="mdi mdi-account-cog-outline text-dark" />
                <span className="iconicTxt">Account</span>
              </div>
              <div className="dropdown-menu">
                <a
                  className="dropdown-item d-flex align-items-center justify-content-between"
                  id="pills-user-tab"
                  data-bs-toggle="pill"
                  href="#pills-user"
                  role="tab"
                >
                  Profile
                </a>
                {/* <a
                  className="dropdown-item d-flex align-items-center justify-content-between"
                  id="pills-setting-tab"
                  data-bs-toggle="pill"
                  href="#pills-setting"
                  role="tab"
                >
                  Setting <i className="bx bx-cog text-muted ms-1" />
                </a> */}
                <a
                  className="dropdown-item d-flex align-items-center justify-content-between"
                  id="pills-change-password-tab"
                  data-bs-toggle="pill"
                  href="#pills-change-password"
                  role="tab"
                >
                  Change Password

                </a>
                <button
                  className="dropdown-item d-flex align-items-center justify-content-between"
                  onClick={() => logOut()}
                >
                  Log out
                </button>
              </div>
            </li>
            <li className=" d-md-none nav-item marginAuto dropdown profile-user-dropdown"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="More">

              <div
                className="nav-link dropdown-toggle d-flex flex-column mb-2"
                role="button"
                data-bs-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i className="mdi mdi-dots-vertical"></i>

                <span className="iconicTxt">More</span>
              </div>
              <div className="dropdown-menu">

                <Link to="/broadcast"
                  className={`dropdown-item d-flex align-items-center justify-content-between ${location.pathname === "/broadcast" ? "active" : ""}`}
                >

                  <h6 className="iconicTxt">Broadcast</h6>
                </Link>
                <Link
                  to="/report"
                  className={`dropdown-item d-flex align-items-center justify-content-between ${["/report/broadcast-report", "/report/customer-report", "/report/agent-report"].includes(location.pathname) ? "active" : ""}`}
                >

                  <h6 className="iconicTxt">Reports</h6>
                </Link>
                <Link to="/leads-dashboard" className={`dropdown-item d-flex align-items-center justify-content-between ${location.pathname === "/leads-dashboard" ? "active" : ""}`}>
                  <h6 className="iconicTxt">Leads</h6>
                </Link>
              </div>


            </li>
          </ul>
        </div>
      </div>
      <div className="chat-leftsidebar d-flex flex-column  w-100 w-lg-50 ">
        <div className="tab-content">
          <div
            className={`tab-pane ${urlQuery === "user" ? "show active" : ""}`}
            id="pills-user"
            role="tabpanel"
            aria-labelledby="pills-user-tab"
          >
            <UserProfile currentUser={currentUser} />
          </div>
          <div
            className={`tab-pane ${urlQuery === "change-password" ? "show active" : ""
              }`}
            id="pills-change-password"
            role="tabpanel"
            aria-labelledby="pills-change-password-tab"
          >
            <ChangePassword />
          </div>
          <div
            className={`tab-pane ${urlQuery === "" ? "show active" : ""}`}
            id="pills-chat"
            role="tabpanel"
            aria-labelledby="pills-chat-tab"
          >
            <div>
              <div className="pt-3 px-3 pb-2">
                <div className="d-flex justify-content-between align-items-center">
                  <div className="d-flex justify-content-between align-items-center flex-grow-1">
                    <h4 className="mb-2 fw-bold">Chats</h4>
                    <div style={{ position: "relative", marginRight: ".5rem", padding: ".5rem", cursor: "pointer" }} className="reminder-toggle-all mb-2" >
                      {groupedReminders.today.length > 0 && (
                        <span
                          style={{
                            position: "absolute",
                            top: "-5px",
                            right: "-5px",
                            background: "red",
                            color: "white",
                            borderRadius: "50%",
                            padding: "1px 6px",
                            fontSize: "12px",
                            fontWeight: "bold",
                            zIndex: 1,
                          }}
                        >
                          {groupedReminders.today.length}
                        </span>
                      )}
                      <ReminderToasts groupedReminders={groupedReminders} />
                      <FaClockRotateLeft onClick={handleReminder} />
                      {showAllReminder && <Reminder fetchType={"retrieve_all"} reminders={reminders} loading={loading} setReminders={setReminders} groupedReminders={groupedReminders} />}
                    </div>

                  </div>
                  {/* <div className="flex-shrink-0">
                    <div
                      data-bs-toggle="tooltip"
                      data-bs-trigger="hover"
                      data-bs-placement="bottom"
                      title="Add Contact"
                    >
                      <button
                        type="button"
                        className="btn btn-soft-primary btn-sm"
                        data-bs-toggle="modal"
                        data-bs-target="#addContact-exampleModal"
                      >
                        <i className="bx bx-plus" />
                      </button>
                    </div>
                  </div> */}
                  {checkboxList.length > 0 && (
                    <button
                      type="button"
                      data-bs-toggle="modal"
                      data-bs-target="#assignmodel"
                      title="Assign"
                      style={{
                        padding: "0px",
                        border: "none",
                        background: "transparent",
                      }}
                      onClick={handleAssignChat}
                    >
                      <i
                        className="bx bxs-user-plus"
                        style={{ fontSize: "30px", color: "#4EAC6D" }}
                      />
                    </button>
                  )}

                  {isModalOpen && (
                    <>
                      {/* Optional backdrop */}
                      <div className="modal-backdrop fade show"></div>

                      <div className="modal d-block" tabIndex="-1" ref={modalRef}>
                        <div className="modal-dialog">
                          <div className="modal-content">
                            <div className="modal-header">
                              <h1 className="modal-title fs-5">Assign Agent/Manager</h1>
                              <button
                                type="button"
                                className="btn-close"
                                onClick={() => setIsModalOpen(false)}
                              ></button>
                            </div>
                            <div className="modal-body">
                              {currentUser.user_type !== "manager" && (
                                <Select
                                  placeholder="Select designation"
                                  isLoading={isLoading}
                                  onChange={handleDesignation}
                                  options={designationOptions}
                                />
                              )}
                              {agentList.length > 0 && (
                                <div className="mt-4">
                                  <Select
                                    placeholder={
                                      currentUser.user_type === "manager"
                                        ? "Select Agent"
                                        : `Select ${selectedType.value}`
                                    }
                                    onChange={handleAgent}
                                    options={agentList}
                                  />
                                </div>
                              )}
                            </div>
                            <div className="modal-footer">
                              <button
                                type="button"
                                className="btn btn-danger"
                                onClick={() => setIsModalOpen(false)}
                              >
                                Cancel
                              </button>
                              <button
                                type="button"
                                className="btn btn-primary"
                                onClick={handleAssign}
                              >
                                Assign
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}

                </div>
                <Search />
              </div>
              <div className="d-flex px-3 gap-3 justify-content-between w-100 align-items-center">

                <div className={`d-flex gap-1 mb-3 justify-content-start align-items-center responsiveFont ${disable && 'justify-content-end'}`}>
                  {!disable && <>
                    <button
                      className={`py-1 btn-border-none rounded-pill ${chatCategory === "all" ? "active-btn" : ""
                        }`}
                      onClick={allBtn}
                    >
                      All
                    </button>
                    <button
                      className={`py-1 btn-border-none relative rounded-pill ${chatCategory === "unread" ? "active-btn" : ""
                        }`}
                      onClick={unreadBtn}
                    >
                      Unread{" "}
                      {unreadCount > 0 && (
                        <span
                          className="position-absolute top-0 start-100 translate-middle badge rounded-pill badge-primary "
                          style={{ background: "#4eac6d" }}
                        >
                          {unreadCount}
                        </span>
                      )}
                    </button>
                    <button
                      className={`py-1 btn-border-none rounded-pill ${chatCategory === "star" ? "active-btn" : ""
                        }`}
                      onClick={starBtn}
                    >
                      Star
                    </button>


                  </>

                  }

                  {chatFilter &&
                    <button
                      className="mx-2 cursor-pointer py-1 btn-border-none rounded-pill active-btn position-relative"
                      onClick={handleClearQuery}
                    >
                      <div
                        className="remove-filter position-absolute text-danger"
                        style={{
                          top: '-0.5rem', // Position it slightly above the top of the button
                          right: '-0.5rem', // Position it slightly outside the right edge
                          fontSize: '1.5rem',
                          cursor: 'pointer',
                          textAlign: 'center',
                          width: '1.5rem',
                          height: '1.5rem',
                          lineHeight: '1.5rem', // Vertically center the minus sign
                          background: 'whiteSmoke', // Optional: Add background for better visibility
                          borderRadius: '50%', // Make it a circular button
                          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // Optional: Add some shadow
                        }}
                      >
                        -
                      </div>
                      {chatFilter}
                    </button>

                  }
                  {/* <button
                  className={`mx-2  mb-3 py-1 btn-border-none rounded-pill ${chatCategory === "waiting" ? "active-btn" : ""
                    }`}
                  onClick={waitingBtn}
                >
                  Waiting
                </button> */}
                </div>

                {!disable && currentUser.user_type !== "agent" && chatCategory !== "star" &&
                  <div className="mb-3 d-flex gap-2 justify-content-end align-items-center">
                    {/* Filter Menu */}
                    {!filterMode && (
                      <Dropdown>
                        <Dropdown.Toggle variant="light" size="sm" className="border">
                          <FaFilter />
                        </Dropdown.Toggle>

                        <Dropdown.Menu>
                          <Dropdown.Item onClick={() => setFilterMode("agent")}>
                            By Agent
                          </Dropdown.Item>
                          {/* <Dropdown.Item onClick={() => setFilterMode("date")}>
                            By Date
                          </Dropdown.Item> */}
                        </Dropdown.Menu>
                      </Dropdown>
                    )}

                    {/* Agent Dropdown */}
                    {filterMode === "agent" && (
                      <>
                        <div className="flex-grow-1">
                          <Select
                            classNamePrefix="react-select"
                            options={[
                              { value: "All Agents", label: "All Agents" },
                              { value: 0, label: "Unassigned" },
                              ...agents,
                            ]}
                            value={[
                              { value: "All Agents", label: "All Agents" },
                              { value: 0, label: "Unassigned" },
                              ...agents,
                            ].find((option) => option.value === chatFilterType.value)}
                            onChange={(selectedOption) => handleAgentSelect(selectedOption)}
                            isSearchable
                            placeholder="Select Agent"
                          />
                        </div>

                        <button className="btn btn-outline-danger btn-sm"
                          onClick={() => {
                            setFilterMode(null);
                            setNextFilterId(null);
                            setChatFilterType({ value: "All Agents", label: "All Agents" })
                          }}>
                          Close
                        </button>
                      </>
                    )}

                    {/* Date Filter */}
                    {filterMode === "date" && (
                      <div className="position-relative">
                        <BsCalendarDate
                          size={24}
                          role="button"
                          onClick={() => setShowDatePicker(!showDatePicker)}
                        />
                        <button
                          className="btn btn-outline-danger btn-sm ms-2"
                          onClick={() => {
                            setFilterMode(null);
                            setShowDatePicker(false);
                          }}
                        >
                          Close
                        </button>

                        {showDatePicker && (
                          <div
                            ref={datePickerRef}
                            className="position-absolute bg-white shadow p-2 rounded"
                            style={{ top: "35px", right: 0, width: "230px", zIndex: "10" }}
                          >
                            <DatePicker
                              selectsRange
                              startDate={fromDate}
                              endDate={toDate}
                              onChange={(update) => {
                                if (update) {
                                  const [start, end] = update;
                                  setDateRange([
                                    start ? new Date(start.getFullYear(), start.getMonth(), start.getDate(), 0, 0, 0, 0) : null,
                                    end ? new Date(end.getFullYear(), end.getMonth(), end.getDate(), 23, 59, 59, 999) : null,
                                  ]);
                                } else {
                                  setDateRange([null, null]);
                                }
                              }}
                              isClearable
                              className="form-control"
                              maxDate={new Date()}
                              placeholderText="Select Date Range"
                            />
                            <button
                              className="btn btn-sm btn-primary w-100 mt-2"
                              onClick={() => {
                                setShowDatePicker(false);

                              }}
                            >
                              Search
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                }
              </div>
              <hr className="m-0 p-0"></hr>
              <div
                className="chat-room-list"
                data-simplebar
                ref={scrollContainerRef}
                onScroll={handleScroll}
              >
                {/* <h5 className="mb-2 px-4 mt-2 font-size-11 text-muted text-uppercase">
                  Recent Chat
                </h5> */}
                <Chats page={page} />
              </div>
            </div>
          </div>
          <div
            className="tab-pane"
            id="pills-bookmark"
            role="tabpanel"
            aria-labelledby="pills-bookmark-tab"
          >
            <Bookmark />
          </div>
          <div
            className="tab-pane"
            id="pills-contacts"
            role="tabpanel"
            aria-labelledby="pills-contacts-tab"
          >
            <h4 className="m-4">Unread Chats</h4>
            <hr></hr>
            {/* <UnreadChats /> */}
          </div>
        </div>
      </div>
    </div >
  );
};

export default SideBar;
