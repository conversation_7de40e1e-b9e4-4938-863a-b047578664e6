import React, { useContext, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { deleteCookie } from "../utils/Utils";
import axios from "axios";
import { BASE_URL2 } from "../api/api";
import { AuthContext } from "../context/AuthContext";
import { useLocation } from "react-router-dom";
import { MdSupportAgent } from "react-icons/md";
import { IoIosSettings } from "react-icons/io";
import { MdOutlineSpaceDashboard } from "react-icons/md";

const LeftMenu = () => {
  const { currentUser } = useContext(AuthContext);
  const location = useLocation();
  useEffect(() => {
    const bootstrap = require("bootstrap/dist/js/bootstrap.bundle.min.js");
    const tooltipTriggerList = [].slice.call(
      document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    tooltipTriggerList.map(
      (tooltipTriggerEl) => new bootstrap.Tooltip(tooltipTriggerEl)
    );
  }, []);
  const logOut = async () => {
    try {
      const dataforlogout = {
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "logout",
        agent_id: currentUser.user_id,
        agent_name: currentUser.name,
        user_type: currentUser.user_type
      }
      axios.post(
        `${BASE_URL2}/whatsapp_user`,
        dataforlogout
      );
    } catch (error) {
      console.log(error);

    }
    deleteCookie("user");
  }
  return (
    <>
      <div className="flex-lg-column margnRight">
        <div className="side-menu flex-lg-column my-0 sidemenu-navigation left-menu">
          <ul className="nav nav-pills side-menu-nav" role="tablist"
          >
            {/* <li
              className="nav-item d-none d-lg-block mb-3"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Profile"
            >
              <Link to="/home?tab=user" className="nav-link">
                <i className="mdi mdi-account-outline text-dark" />
                <h6 className="iconicTxt">Profile</h6>
              </Link>
            </li> */}
           {currentUser.user_type!=="agent" && <li
              className="nav-item  mb-3"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Dashboard"
            >
              <Link to="/dashboard"  className={`nav-link ${location.pathname === "/dashboard" ? "active" : ""}`}>
                <MdOutlineSpaceDashboard className="text-dark" />

                <h6 className="iconicTxt">Dashboard</h6>
              </Link>
            </li>}
            <li
              className="nav-item"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Chats"
            >
              <Link to="/home" className="nav-link">
                <i className="mdi mdi-message-text-outline text-dark" />
                <h6 className="iconicTxt">Chats</h6>
              </Link>
            </li>
            {currentUser.user_type !== "agent" && <li
              className="nav-item mb-3"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Settings"
            >
              <Link to="/agent-management/agent"
                className={`nav-link ${location.pathname.includes("agent-management") ? "active" : ""}`}
              >
                <IoIosSettings className="text-dark" />
                <h6 className="iconicTxt">Settings</h6>
              </Link>
            </li>}
            <li
              className="nav-item d-none d-md-block"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Broadcast"
              style={{ zIndex: 1 }}
            >
              <Link to="/broadcast"
                className={`nav-link ${location.pathname === "/broadcast" ? "active" : ""}`}
              >
                <i className="mdi mdi-access-point text-dark"></i>
                <h6 className="iconicTxt">Broadcast</h6>
              </Link>
            </li>
            <li
              className="nav-item d-none d-md-block"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Reports"
            >
              <Link
                to="/report"
                className={`nav-link ${["/report/broadcast-report", "/report/customer-report", "/report/agent-report"].includes(location.pathname) ? "active" : ""}`}
              >
                <i className="mdi mdi-file-chart text-dark"></i>
                <h6 className="iconicTxt">Reports</h6>
              </Link>

            </li>
            <li
              className="nav-item d-none d-md-block"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Leads"
            >
              <Link to="/leads-dashboard" className={`nav-link ${location.pathname === "/leads-dashboard" ? "active" : ""}`}>
                <i className="mdi mdi-account-multiple-outline text-dark"></i>
                <h6 className="iconicTxt">Leads</h6>
              </Link>
            </li>


            <li
              className="nav-item marginAuto dropdown profile-user-dropdown"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="Profile"
            >
              <div
                className="nav-link dropdown-toggle d-flex flex-column mb-2"
                role="button"
                data-bs-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i className="mdi mdi-account-cog-outline text-dark" />
                <span className="iconicTxt">Account</span>
              </div>
              <div className="dropdown-menu">
                <Link
                  to="/home?tab=user"
                  className="dropdown-item d-flex align-items-center justify-content-between"
                >
                  Profile
                </Link>
                <button
                  className="dropdown-item d-flex align-items-center justify-content-between"
                  onClick={() => logOut()}
                >
                  Log out
                </button>
              </div>
            </li>
            <li className=" d-md-none nav-item marginAuto dropdown profile-user-dropdown"
              data-bs-toggle="tooltip"
              data-bs-placement="right"
              data-bs-trigger="hover"
              data-bs-container=".sidemenu-navigation"
              title="More">

              <div
                className="nav-link dropdown-toggle d-flex flex-column mb-2"
                role="button"
                data-bs-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i className="mdi mdi-dots-vertical"></i>

                <span className="iconicTxt">More</span>
              </div>
              <div className="dropdown-menu">

                <Link to="/broadcast"
                  className={`dropdown-item d-flex align-items-center justify-content-between ${location.pathname === "/broadcast" ? "active" : ""}`}
                >

                  <h6 className="iconicTxt">Broadcast</h6>
                </Link>
                <Link
                  to="/report"
                  className={`dropdown-item d-flex align-items-center justify-content-between ${["/report/broadcast-report", "/report/customer-report", "/report/agent-report"].includes(location.pathname) ? "active" : ""}`}
                >

                  <h6 className="iconicTxt">Reports</h6>
                </Link>
                <Link to="/leads-dashboard" className={`dropdown-item d-flex align-items-center justify-content-between ${location.pathname === "/leads-dashboard" ? "active" : ""}`}>
                  <h6 className="iconicTxt">Leads</h6>
                </Link>
              </div>


            </li>

          </ul>
        </div>
      </div>
    </>
  );
};

export default LeftMenu;
