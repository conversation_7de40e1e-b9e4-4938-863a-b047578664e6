import React from "react";
import styles from "./inputBar.module.css"; // Import the CSS module
import { useContext, useState, useEffect, useRef, useCallback } from "react";
import { ChatContext } from "../../context/ChatContext";
import Input from "../Input";
import useSentences from "../../customHooks/useSentences";
import { ChatState } from "../../context/AllProviders";
import { FaBold } from "react-icons/fa";
import { FaItalic } from "react-icons/fa";
import { FaStrikethrough } from "react-icons/fa";
import { FaLanguage } from "react-icons/fa";
import { FaInfoCircle } from "react-icons/fa";
import ReplyPreview from "../ReplyPreview/ReplyPreview";
import BlockCard from "../BlockCard";
import { AuthContext } from "../../context/AuthContext";
import axios from "axios";
import { BASE_URL2 } from "../../api/api";
import { toast } from "react-toastify";
import { TRANSLATION_CONFIG, getTranslationUrl } from "../../config/translation";
const Inputbar = ({
  setFaqOpen,
  notes,
  setshowNotesCard,
  setShowContactDetail,
  activeTab,
  setActiveTab,
  setshowQuickReply,

}) => {
  const { data } = useContext(ChatContext);
  const { text, setText, reply, setReply, selectedMobileNumber, selectedUserDetails } = ChatState();
  const textareaRef = useRef(null);
  const [showPreview, setShowPreview] = useState(false);
  const { currentUser } = useContext(AuthContext);
  const lastTypingTimeRef = useRef(0);

  // Translation states
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [isTranslationEnabled, setIsTranslationEnabled] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [languages, setLanguages] = useState([]);
  const [filteredLanguages, setFilteredLanguages] = useState([]);
  const [languageSearch, setLanguageSearch] = useState('');
  const [loadingLanguages, setLoadingLanguages] = useState(false);
  const [translationError, setTranslationError] = useState("");

  // Translation refs
  const debounceTimeoutRef = useRef(null);
  const searchDebounceRef = useRef(null);
  const lastTranslatedWordRef = useRef("");
  const isTranslatingRef = useRef(false);
  const DEBOUNCE_DELAY = TRANSLATION_CONFIG.SETTINGS.DEBOUNCE_DELAY;

  const { filteredSentences, saveSentences, splitParagraphIntoSentences } = useSentences();

  const handleSuggestionClick = (suggestedText) => {
    setText((prevState) => {
      // Split the current input into words
      const words = prevState.split(/\s+/);

      // Check if the last word matches the suggestion
      const lastWord = words[words.length - 1];
      const suggestionFirstWord = suggestedText.split(/\s+/)[0];

      if (suggestionFirstWord.toLowerCase().includes(lastWord.toLowerCase())) {
        // If the last word matches the suggestion, replace it
        words[words.length - 1] = suggestedText;
      } else {
        // Otherwise, append the suggestion
        words.push(suggestedText);
      }
      // Join the words back into a single string and return
      return words.join(" ");
    });
    textareaRef.current?.focus();
  };

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);

    if (tabName === "Note") {
      setshowNotesCard((prevState) => {
        const newState = !prevState; // Calculate the new state
        if (!newState) {
          // If the new state is `false`, reset the active tab to ""
          setActiveTab("");
        }
        return newState;
      });
      setShowContactDetail(false);
      setFaqOpen(false);
      setshowQuickReply(false);
    } else if (tabName === "quickReplies") {
      setshowQuickReply((prevState) => {
        const newState = !prevState; // Calculate the new state
        if (!newState) {
          // If the new state is `false`, reset the active tab to ""
          setActiveTab("");
        }
        return newState;
      });
      setShowContactDetail(false);
      setFaqOpen(false);
      setshowNotesCard(false);
    }
  };

  const showTypingStatus = async () => {
    const now = Date.now();
    if (now - lastTypingTimeRef.current < 25000) {
      return;
    }

    lastTypingTimeRef.current = now;
    const payload = {
      user_id: currentUser.user_id,
      token: currentUser.token,
      method: "typing",
      user_type: currentUser.user_type,
      mobile: selectedMobileNumber,
      brand_number: currentUser.brand_number,
    };

    try {
      const { data } = await axios.post(`${BASE_URL2}/conversation`, payload);
    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.");
      } else {
        console.error(error);
        toast.error("Something went wrong, please try again later");
      }
    }
  };

  useEffect(() => {
    if (!text || !currentUser.user_id || !currentUser.user_type || !currentUser.brand_number || !currentUser.token) return;

    showTypingStatus();
  }, [text, currentUser]);

  const applyFormat = (wrapSymbol) => {
    const input = textareaRef.current;
    if (!input) return;

    const startPos = input.selectionStart;
    const endPos = input.selectionEnd;

    const beforeSelection = text.substring(0, startPos);
    const selectedText = text.substring(startPos, endPos);
    const afterSelection = text.substring(endPos);

    if (selectedText === "") {
      const newText = beforeSelection + wrapSymbol + wrapSymbol + afterSelection;
      setText(newText);
      setShowPreview(true);

      // Move cursor between added symbols
      setTimeout(() => {
        input.focus();
        input.setSelectionRange(startPos + wrapSymbol.length, startPos + wrapSymbol.length);
      }, 10);
    } else {
      const newText = beforeSelection + wrapSymbol + selectedText + wrapSymbol + afterSelection;
      setText(newText);
      setShowPreview(true);

      setTimeout(() => input.focus(), 10);
    }
  };

  // Translation functions
  const fetchLanguages = useCallback(async () => {
    // Always return the current languages immediately if we have them
    if (languages.length > 0) {
      return languages;
    }
    if (!currentUser?.parent_id || !currentUser?.parent_token) return []; // No auth data

    setLoadingLanguages(true);
    try {
      const payload = {
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "language_list",
      };

      const response = await axios.post(getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE), payload);
      if (response.data.success) {
        const languagesData = response.data.data;

        let languageArray = [];

        // Handle different possible data structures
        if (Array.isArray(languagesData)) {
          // AuthKey API returns array with 'language' and 'name' fields
          languageArray = languagesData.map((item) => ({
            code: item.language, // Use 'language' field as the code
            name: String(item.name || item.language), // Use 'name' field as the display name
          })).filter((item) => item.code); // Filter out any items without a language code
        } else if (typeof languagesData === "object" && languagesData !== null) {
          // If it's an object, convert to array
          languageArray = Object.entries(languagesData).map(([code, name]) => ({
            code,
            name: String(name || code),
          }));
        }

        // Sort languages alphabetically by name (with safety check)
        if (languageArray.length > 0) {
          languageArray.sort((a, b) => {
            const nameA = String(a.name || "");
            const nameB = String(b.name || "");
            return nameA.localeCompare(nameB);
          });
        }

        if (languageArray.length === 0) {
          console.warn("No languages found in API response");
          // Set a fallback with common languages
          languageArray = [
            { code: "en", name: "English" },
            { code: "hi", name: "Hindi" },
            { code: "es", name: "Spanish" },
            { code: "fr", name: "French" },
            { code: "de", name: "German" },
          ];
        }
        
        // Ensure English is first in the list
        languageArray.sort((a, b) => {
          if (a.code === 'en') return -1;
          if (b.code === 'en') return 1;
          return a.name.localeCompare(b.name);
        });
        
        setLanguages(languageArray);
        setFilteredLanguages(languageArray);
        return languageArray;
      } else {
        throw new Error(response.data.message || "Failed to fetch languages");
      }
    } catch (error) {
      console.error("Failed to fetch languages:", error);
      setTranslationError("Failed to load languages");
      setTimeout(() => setTranslationError(""), 3000);
    } finally {
      setLoadingLanguages(false);
    }
  }, [languages.length, currentUser]);

  const translateWordSilently = useCallback(
    async (word, wordStart, wordEnd, currentCursorPos) => {
      // Skip if word is empty, no language selected, or already translating
      if (!word.trim() || !selectedLanguage || isTranslatingRef.current || !isTranslationEnabled) {
        return;
      }
      
      // Skip translation if the word is already in English or the target language is English
      if (selectedLanguage === 'en') {
        // If English is selected, just add a space and return
        const textarea = textareaRef.current;
        if (textarea) {
          const currentText = textarea.value;
          const newText = currentText.substring(0, wordEnd) + ' ' + currentText.substring(wordEnd);
          setText(newText);
          setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(wordEnd + 1, wordEnd + 1);
          }, 10);
        }
        return;
      }

      if (!currentUser?.parent_id || !currentUser?.parent_token) {
        return;
      }

      if (word === lastTranslatedWordRef.current) {
        return;
      }

      const textarea = textareaRef.current;
      if (!textarea) return;

      // Prevent multiple simultaneous translations
      isTranslatingRef.current = true;

      try {
        // Store current selection to preserve it
        const selectionStart = textarea.selectionStart;
        const selectionEnd = textarea.selectionEnd;

        // Get current text
        const currentText = textarea.value;

        // Translate the word
        const payload = {
          user_id: currentUser.parent_id,
          token: currentUser.parent_token,
          method: "translate",
          target_lang: selectedLanguage,
          text: word,
        };

        const response = await axios.post(
          getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE),
          payload
        );

        // Handle different response formats
        let translatedWord;
        
        // Format 1: { success: true, data: 'translated text' }
        if (response?.data?.success && response.data.data) {
          translatedWord = String(response.data.data).trim();
        }
        // Format 2: { success: true, translated_text: 'translated text' }
        else if (response?.data?.success && response.data.translated_text) {
          translatedWord = String(response.data.translated_text).trim();
        } else {
          console.error("Unexpected translation response format:", response?.data);
          throw new Error(response?.data?.message || "Unexpected translation response format");
        }

        // Validate translated word
        if (!translatedWord) {
          throw new Error("Empty translation result");
        }

        // Create new text with translated word and add a space after
        const newText =
          currentText.substring(0, wordStart) +
          translatedWord +
          '  ' +  // Add a space after the translated word
          currentText.substring(wordEnd).trimStart();  // Remove any leading space to avoid double spaces

        // Update textarea value and React state
        setText(newText);

        // Calculate new cursor position
        const lengthDifference = translatedWord.length - word.length;
        let newCursorPos;

        if (currentCursorPos <= wordEnd) {
          // Cursor was before or at the end of the translated word
          if (currentCursorPos <= wordStart) {
            newCursorPos = currentCursorPos; // Before the word, no change
          } else {
            newCursorPos = wordEnd + lengthDifference; // At the end of translated word
          }
        } else {
          // Cursor was after the word
          newCursorPos = currentCursorPos + lengthDifference;
        }

        // Restore cursor position after React update
        setTimeout(() => {
          if (textarea) {
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            textarea.focus();
          }
        }, 10);

        lastTranslatedWordRef.current = translatedWord;
      } catch (err) {
        console.error("Translation error:", err);
        setTranslationError("Translation failed. Please try again.");
        setTimeout(() => setTranslationError(""), 3000);
        // Reset translation state on error
        lastTranslatedWordRef.current = "";
      } finally {
        isTranslatingRef.current = false;
      }
    },
    [selectedLanguage, isTranslationEnabled, currentUser]
  );

  // Find completed word when space is pressed
  const findCompletedWord = (text, cursorPos) => {
    // Look backwards from cursor position to find the completed word
    if (cursorPos > 0 && text[cursorPos - 1] === " ") {
      let wordEnd = cursorPos - 1; // Position of space
      let wordStart = wordEnd;

      // Find start of word (go backwards until space or beginning)
      while (wordStart > 0 && text[wordStart - 1] !== " ") {
        wordStart--;
      }

      const word = text.substring(wordStart, wordEnd);
      return {
        word: word.trim(),
        start: wordStart,
        end: wordEnd,
      };
    }
    return null;
  };

  // Find current word being typed (for auto-translation)
  const findCurrentWord = (text, cursorPos) => {
    let start = cursorPos;
    let end = cursorPos;

    // Find word boundaries
    while (start > 0 && text[start - 1] !== " ") {
      start--;
    }
    while (end < text.length && text[end] !== " ") {
      end++;
    }

    const word = text.substring(start, end);
    return {
      word: word.trim(),
      start: start,
      end: end,
    };
  };

  // Filter languages based on search term
  const filterLanguages = useCallback((searchTerm) => {
    if (!searchTerm) {
      setFilteredLanguages(languages);
      return;
    }
    const searchLower = searchTerm.toLowerCase();
    const filtered = languages.filter(lang => 
      lang.name.toLowerCase().includes(searchLower) || 
      lang.code.toLowerCase().includes(searchLower)
    );
    setFilteredLanguages(filtered);
  }, [languages]);

  // Debounced search handler
  const handleLanguageSearch = useCallback((e) => {
    const searchTerm = e.target.value;
    setLanguageSearch(searchTerm);
    
    if (searchDebounceRef.current) {
      clearTimeout(searchDebounceRef.current);
    }
    
    searchDebounceRef.current = setTimeout(() => {
      filterLanguages(searchTerm);
    }, 300);
  }, [filterLanguages]);

  const handleLanguageSelect = (languageCode) => {
    setSelectedLanguage(languageCode);
    setIsTranslationEnabled(!!languageCode);
    setShowLanguageDropdown(false);
    setLanguageSearch('');
    setFilteredLanguages(languages);

    // Reset translation references
    lastTranslatedWordRef.current = "";
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  };

  const toggleLanguageDropdown = async () => {
    const willShow = !showLanguageDropdown;
    
    if (willShow) {
      try {
        setLoadingLanguages(true);
        const langs = await fetchLanguages();
        if (langs && langs.length > 0) {
          setFilteredLanguages(langs);
        }
      } catch (error) {
        console.error('Error loading languages:', error);
        // Set default languages in case of error
        const defaultLangs = [
          { code: "en", name: "English" },
          { code: "hi", name: "Hindi" },
          { code: "es", name: "Spanish" }
        ];
        setLanguages(defaultLangs);
        setFilteredLanguages(defaultLangs);
      } finally {
        setLoadingLanguages(false);
      }
    } else {
      setLanguageSearch('');
    }
    setShowLanguageDropdown(willShow);
  };

  // Handle space key press for instant translation
  const handleTranslationKeyDown = useCallback((e) => {
    // Only process space key and ensure we're not in the middle of translation
    if (e.key === ' ' && !isTranslatingRef.current && isTranslationEnabled) {
      const textarea = e.target;

      // Clear any pending auto-translation
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Use requestAnimationFrame to ensure space is processed first
      requestAnimationFrame(() => {
        const text = textarea.value;
        const cursorPos = textarea.selectionStart;

        const completedWord = findCompletedWord(text, cursorPos);
        // Translate the word regardless of its length
        if (completedWord && completedWord.word) {
          translateWordSilently(
            completedWord.word,
            completedWord.start,
            completedWord.end,
            cursorPos
          );
        }
      });
    }
  }, [translateWordSilently, isTranslationEnabled, findCompletedWord]);

  // Handle input for auto-translation (debounced)
  const handleTranslationInputChange = useCallback((currentText) => {
    if (!isTranslationEnabled || !textareaRef.current) return;

    // Don't translate if user is backspacing or deleting
    const isDeleting = currentText.length < (text?.length || 0);
    if (isDeleting) {
      // Reset last translated word if user is editing
      lastTranslatedWordRef.current = '';
      return;
    }

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, [isTranslationEnabled, translateWordSilently, findCurrentWord, text?.length]);

  // Effect to handle language change
  useEffect(() => {
    if (!isTranslationEnabled || !textareaRef.current || !currentUser?.parent_id || !currentUser?.parent_token) return;

    // Reset references
    lastTranslatedWordRef.current = '';
  }, [selectedLanguage, isTranslationEnabled, currentUser]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={styles.inputContainer}>
      {selectedUserDetails.isBlock === 1 ? <BlockCard selectedMobileNumber={selectedMobileNumber} selectedUserDetails={selectedUserDetails} /> :
        <>
          {reply && <ReplyPreview reply={reply} onClose={() => setReply(null)} />}
          <header className="d-flex justify-content-start align-items-center w-100">
            <button
              onClick={() => handleTabClick("Note")}
              className={`btn ${styles.button} ms-2 ${activeTab === "Note" ? styles.activeButton : ""
                }`}
            >
              Notes {notes.length > 0 ? `(${notes.length})` : ""}
            </button>
            <button
              onClick={() => handleTabClick("quickReplies")}
              className={`btn ${styles.button} ms-2 ${activeTab === "quickReplies" ? styles.activeButton : ""
                }`}
            >
              Quick Replies
            </button>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("*")}
            >
              <FaBold />
            </div>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("_")}
            >
              <FaItalic />
            </div>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("~")}
            >
              <FaStrikethrough />
            </div>

            {/* Language Translation Dropdown */}
            <div className="dropup mx-1 d-flex align-items-center" style={{ position: 'relative' }}>
              <button
                className={`btn ${isTranslationEnabled ? 'btn-primary' : 'text-secondary'} dropdown-toggle d-flex align-items-center`}
                type="button"
                onClick={toggleLanguageDropdown}
                style={{ 
                  fontSize: '0.875rem', 
                  padding: '0.25rem 0.5rem',
                  borderTopRightRadius: 0,
                  borderBottomRightRadius: 0
                }}
              >
                <FaLanguage 
                  className="me-1" 
                  style={{ color: isTranslationEnabled ? undefined : 'black' }} 
                />
                <span style={{ color: isTranslationEnabled ? undefined : 'black' }}>
                  {selectedLanguage ?
                    languages.find(lang => lang.code === selectedLanguage)?.name || 'Language'
                    : 'Language'
                  }
                </span>
              </button>
              <div 
                className={`btn ${isTranslationEnabled ? 'btn-primary' : 'text-secondary'}`}
                style={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                  borderLeft: '1px solid rgba(255, 255, 255, 0.2)',
                  padding: '0 0.3rem',
                  cursor: 'pointer',
                  position: 'relative'
                }}
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
                onClick={() => setShowTooltip(!showTooltip)}
              >
                <FaInfoCircle style={{ fontSize: '0.8rem' }} />
                {showTooltip && (
                  <div 
                    style={{
                      position: 'absolute',
                      bottom: '100%',
                      right: 0,
                      backgroundColor: '#333',
                      color: 'white',
                      padding: '0.5rem',
                      borderRadius: '4px',
                      fontSize: '0.8rem',
                      width: '200px',
                      zIndex: 1000,
                      marginBottom: '5px'
                    }}
                  >
                    Press space after each word for instant translation (even after last word).
                  </div>
                )}
              </div>



              {showLanguageDropdown && (
                <div className="dropdown-menu show" style={{
                  position: 'absolute',
                  zIndex: 1060,
                  minWidth: '200px',
                  maxHeight: '300px',
                  overflowY: 'auto',
                  bottom: '100%',
                  top: 'auto',
                  marginBottom: '0.125rem',
                  pointerEvents: 'auto'
                }}>
                  <div className="p-2">
                    <input
                      type="text"
                      className="form-control form-control-sm mb-2"
                      placeholder="Search languages..."
                      value={languageSearch}
                      onChange={handleLanguageSearch}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                  <div className="dropdown-header">
                    <small className="text-muted">Select language for translation</small>
                  </div>
                  <button
                    className={`dropdown-item ${!selectedLanguage ? 'active' : ''}`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleLanguageSelect('');
                    }}
                  >
                    <i className="fas fa-times me-2"></i> Disable Translation
                  </button>
                  <div className="dropdown-divider"></div>

                  <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                    {loadingLanguages ? (
                      <div className="dropdown-item-text text-center">
                        <small>Loading languages...</small>
                      </div>
                    ) : filteredLanguages.length > 0 ? (
                      filteredLanguages.map((language) => (
                        <button
                          key={language.code}
                          className={`dropdown-item d-flex align-items-center ${selectedLanguage === language.code ? 'active' : ''}`}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleLanguageSelect(language.code);
                          }}
                        >
                          {language.code === 'en' && <i className="fas fa-check me-2"></i>}
                          {language.name} {language.code !== 'en' && <span className="text-muted ms-1">({language.code})</span>}
                        </button>
                      ))
                    ) : (
                      <div className="dropdown-item-text text-center">
                        <small>No languages found</small>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Overlay to close dropdown */}
              {showLanguageDropdown && (
                <div
                  className="position-fixed top-0 start-0 w-100 h-100"
                  style={{ zIndex: 1050 }}
                  onClick={() => setShowLanguageDropdown(false)}
                />
              )}
            </div>
          </header>



          {/* Suggestion Section */}
          {filteredSentences.length ? (
            <div className={styles.suggestionsContainer}>
              <ul className={styles.suggestionsList}>
                {filteredSentences.map((sentence, index) => (
                  <li
                    key={index}
                    className={styles.suggestionPill}
                    onClick={() => handleSuggestionClick(sentence)}
                  >
                    {sentence}
                  </li>
                ))}
              </ul>
              <div
                className="d-none d-md-flex justify-content-center align-items-end w-100"
                style={{ fontSize: ".7rem", color: "grey" }}
              >
                <div>Press Tab to add text</div>
              </div>
            </div>
          ) : null}

          <div className={styles.textArea}>
            <Input
              showPreview={showPreview} setShowPreview={setShowPreview}
              textareaRef={textareaRef}
              handleSuggestionClick={handleSuggestionClick}
              saveSentences={saveSentences}
              splitParagraphIntoSentences={splitParagraphIntoSentences}
              filteredSentences={filteredSentences}
              selectedMobile={data.selectedMobile}
              convData={data}
              handleTranslationKeyDown={handleTranslationKeyDown}
              handleTranslationInputChange={handleTranslationInputChange}
            />
          </div>

          {/* Translation Error Display */}
          {translationError && (
            <div className="alert alert-warning alert-dismissible fade show mt-2" role="alert">
              <small>{translationError}</small>
              <button
                type="button"
                className="btn-close"
                onClick={() => setTranslationError('')}
                aria-label="Close"
              ></button>
            </div>
          )}

        </>
      }
    </div>
  );
};

export default Inputbar;
