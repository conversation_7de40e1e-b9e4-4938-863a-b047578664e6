import React, { useContext, useEffect, useState } from "react";
import Navbar from "../components/Navbar";
import LeftMenu from "../components/LeftMenu";
// import { RadioGroup, FormControlLabel, Radio } from "@mui/material";
import Select, { components } from "react-select";
import { AuthContext } from "../context/AuthContext";
import { BASE_URL2, BASE_URL3 } from "../api/api";
import axios from "axios";
import { toast } from "react-toastify";
import styles from './setting.module.css'
import LabelCard from "../components/Labels/LabelCard";
import AutoReplyRules from "../components/AutoReply/AutoReplyRules";
import FaqSettings from "../components/faq/Faqsettings";
import DeleteModal from "../components/DeleteModal/DeleteModal";
const Setting = () => {
  const [selectedHs, setSelectedHs] = useState("");
  const [selectedOption, setSelectedOption] = useState("team");
  const [agentList, setAgentList] = useState([]);
  const [agents, setAgents] = useState([]);
  const [templateList, setTemplateList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const { currentUser } = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState("Hunt Strategy");
  const [label, setLabel] = useState({
    name: "",
    description: "",
    color_code: "",
    list_action: "",
  });
  const [labels, setLabels] = useState([]);
  const [tagStatus, setTagStatus] = useState(false);
  const [waitingTime, setWaitingTime] = useState("");
  const [maxChat, setMaxChat] = useState('');
  const [fbStrategy, setFbStrategy] = useState('');
  const [isSticky, setIsSticky] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState(null);
  const [assignments, setAssignments] = useState([{ agent: "", template: "" }]);
  const [manualAssignments, setManualAssignments] = useState([
    // { contactId: '123', contactName: 'John Doe', agent: '45' }
  ]);
  const [contactLists, setContactLists] = useState([
    { value: 'list1', label: 'Contact List 1' },
    { value: 'list2', label: 'Contact List 2' },
  ]); // Fetched from backend
  const [keywordAssignments, setKeywordAssignments] = useState([
    { keywords: [], agentIds: [] }
  ]);
  const [firstMessageAssignments, setFirstMessageAssignments] = useState([
    { message: "", agentIds: [] }
  ]);

  const handleFirstMessageAssignmentChange = (index, field, value) => {
    setFirstMessageAssignments(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        [field]: value
      };
      return updated;
    });
  };

  const addFirstMessageAssignment = () => {
    setFirstMessageAssignments(prev => [...prev, { message: "", agentIds: [] }]);
  };

  const removeFirstMessageAssignment = (index) => {
    setFirstMessageAssignments(prev => prev.filter((_, i) => i !== index));
  };



  const handleKeywordAssignmentChange = (index, field, value) => {
    setKeywordAssignments(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        [field]: value
      };
      return updated;
    });
  };

  const addKeywordAssignment = () => {
    setKeywordAssignments(prev => [...prev, { keywords: [], agentIds: [] }]);
  };
  const removeKeywordAssignment = (index) => {
    setKeywordAssignments(prev => prev.filter((_, i) => i !== index));
  };


  const handleAssignmentChange = (index, field, value) => {
    const updated = [...assignments];
    updated[index][field] = value;
    setAssignments(updated);
  };

  const addAssignment = () => {
    setAssignments([...assignments, { agent: "", template: "" }]);
  };

  const removeAssignment = (index) => {
    setAssignments(assignments.filter((_, i) => i !== index));
  };
  const handleManualAssignmentChange = (index, field, value) => {
    const updated = [...manualAssignments];
    updated[index][field] = value;
    setManualAssignments(updated);
  };

  const addManualAssignment = () => {
    setManualAssignments([...manualAssignments, { contactListId: "", agentId: "" }]);
  };

  const removeManualAssignment = (index) => {
    const updated = manualAssignments.filter((_, i) => i !== index);
    setManualAssignments(updated);
  };


  const handleAddLabel = async () => {
    if (label.name.trim() === "" || label.description.trim() === "") return;

    const prevLabels = [...labels];
    const body = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "create",
      name: label.name,
      list_type: "wp_chat",
      description: label.description,
      color_code: label.color_code,
      list_action: label.list_action,

    }
    setLabels([...labels, label]);
    try {
      const response = await axios.post(`${BASE_URL3}/contact-list.php`, body)
      if (response.data.success) {
        toast.success("Label added successfully")
      } else {
        toast.error(response.data.data)
      }

    } catch (error) {
      toast.error("Unable to add Label, please try again later");
      setLabels([...prevLabels]);

    }
    finally {
      setLabel({
        name: "",
        description: "",
        color_code: "",
        list_action: ""
      }); // Clear the input field

    }

  }

  const handleRemoveLabel = async () => {
    if (!selectedLabel) return;
    const prevLabels = [...labels];
    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "delete_label",
      list_id: selectedLabel.id,

    }
    const updatedLabels = labels.filter((item) => item.id !== selectedLabel.id);
    setLabels(updatedLabels);
    try {
      const { data } = await axios.post(`${BASE_URL2}/contact_list`, payload)
      if (data.success) {
        toast.success("Label deleted successfully")
      }

    } catch (error) {
      toast.error("Unable to delete Label, please try again later");
      setLabels([...prevLabels]);

    }
    finally {
      setShowModal(false);
    }

  };

  const handleCancel = () => {
    setShowModal(false);

  };


  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };

  useEffect(() => {
    if (currentUser.parent_id) {
      fetchAgent(selectedOption);
    }
  }, [currentUser, selectedOption]);
  const fetchAgent = async (selectType) => {
    setIsLoading(true);
    const forAgentdata = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "retrieve",
      agent_type: selectType,
    };
    try {
      const { data } = await axios.post(
        `${BASE_URL2}/whatsapp_agent`,
        forAgentdata
      );

      if (data.success === true) {
        const newAgentList = data.data.map((item) => ({
          value: item.id,
          label: item.name,
        }));
        setAgentList(newAgentList);
        setIsLoading(false);
      } else {
        setAgentList([]);
        setIsLoading(false);
      }
    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.")

      } else {
        console.error("Error fetching labels:", error);
      }
    }
  };
  useEffect(() => {
    if (!currentUser.parent_id) {
      return;
    }
    const retriveHuntST = async () => {
      const dataforsetting = {
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "retrieve_hunt_strategy",
        user_type: currentUser.user_type,
        brand_number: currentUser.brand_number,
        channel: "whatsapp",
      };
      try {
        const { data } = await axios.post(
          `${BASE_URL2}/whatsapp_setting`,
          dataforsetting
        );
        if (data.success === true) {

          setSelectedHs(data.data.hunt_strategy);
          setSelectedTeam(data.data.teams)
          if (data.data.fallback_time > 0) {
            setWaitingTime(data.data.fallback_time)
          }
          setFbStrategy(data.data.fallback_strategy)
          setMaxChat(data.data.max_chat)
          data.data.tag_status === 1 ? setTagStatus(true) : setTagStatus(false)
          data.data.is_sticky === 1 ? setIsSticky(true) : setIsSticky(false)
        }
      } catch (error) {
        if (error.response && error.response.status === 429) {
          toast.error("Too many requests. Please try again later.")

        } else {
          console.error("Error fetching labels:", error);
        }
      }
    };
    retriveHuntST();
  }, [currentUser]);

  useEffect(() => {
    if (currentUser.user_type !== "admin") return;
    const fetchLabels = async () => {
      const body = {
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "wp_list_retrieve_all",
      };

      try {
        const response = await axios.post(`${BASE_URL2}/contact_list`, body);
        if (response.data.success) {
          setLabels(response.data.data); // Store labels if fetch is successful

        }
      } catch (error) {
        if (error.response && error.response.status === 429) {
          toast.error("Too many requests. Please try again later.")

        } else {
          console.error("Error fetching labels:", error);
        }
      }
    };

    fetchLabels();
  }, [currentUser]);



  useEffect(() => {
    fetchAgents();
    fetchTemplate();
  }, [currentUser]);

  const handleHuntStrategyChange = (event) => {
    setSelectedHs(event.target.value);
  };

  // const handleRadioChange = (event) => {
  //   setSelectedOption(event.target.value);
  // };

  const handleTeamSelect = (e) => {
    setSelectedTeam(e);

  };
  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      cursor: "pointer",
    }),
  };
  const Option = (props) => {
    return (
      <div>
        <components.Option {...props}>
          <input
            type="checkbox"
            checked={props.isSelected}
            onChange={() => null}
          />{" "}
          <label>{props.label}</label>
        </components.Option>
      </div>
    );
  };
  const handleUpdate = async (e) => {
    e.preventDefault();

    let agentList;
    // if (selectedOption === "agent") {
    //   console.log(selectedAgent);
    //   agentList = selectedAgent.map((list) => list.value);
    // }

    const dataforsetting = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "hunt_strategy",
      user_type: currentUser.user_type,
      brand_number: currentUser.brand_number,
      agent_type: selectedOption,
      channel: "whatsapp",
      hunt_strategy: selectedHs,
      agent_id: selectedTeam.value,
      tag_status: tagStatus ? "1" : "0",
      max_chat: maxChat,
      fallback_time: waitingTime,
      fallback_strategy: fbStrategy,
      is_sticky: isSticky ? "1" : "0"
    };

    try {
      const { data } = await axios.post(
        `${BASE_URL2}/whatsapp_setting`,
        dataforsetting
      );
      if (data.success === true) {
        toast.success(data.message);
      }
    } catch (error) {
      console.log(error.message);
      toast.error(error.message);
    }
  };

  const handleTag = (e) => {
    const value = e.target.checked;
    setTagStatus(value ? true : false);
  };

  const handleSticky = () => {
    setIsSticky(prevState => !prevState)
  }

  const handleDeleteClick = (label) => {
    setShowModal(true);
    setSelectedLabel(label);
  }

  const fetchAgents = async () => {
    if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
    const payload = {
      user_id: currentUser.parent_id,
      method: "retrieve_agent",
      token: currentUser.parent_token,
      user_type: currentUser.user_type,
      agent_id: currentUser.user_id,
    }

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);

      if (data.success) {
        const agentList = data.data.filter((item) => item.agent_type === "agent")
        const formattedAgentList = agentList.map((item) => ({
          value: item.id,
          label: item.name
        }))

        setAgents([...formattedAgentList]);
      }

    } catch (error) {
      console.error("Error fetching agents:", error);

    }

  }

  const fetchTemplate = async () => {
    const datafortemplate = {
      token: currentUser.parent_token,
      user_id: currentUser.parent_id,
      method: "retrieve",
    };

    try {
      const { data } = await axios.post(
        `${BASE_URL3}/whatsapp_template.php`,
        datafortemplate
      );

      if (data.success === true) {
        const formattedTemplates = data.data.map(template => ({
          value: template.id,
          label: template.temp_name,
        }));
        setTemplateList(formattedTemplates);
      }
    } catch (error) {
      console.log(error.message);
      toast.error(error.message);
    }
  };


  return (
    <div>
      <div className="layout-wrapper d-lg-flex">
        <LeftMenu />
        <div className="w-100">
          <Navbar />
          <main className={`d-flex flex-column flex-lg-row justify-content-lg-between align-items-center align-items-lg-start p-3 mt-3 ${styles.settingsMain}`}>
            <div
              className={`${styles.chatSettingsCard} d-flex flex-column justify-content-center justify-content-lg-start align-items-center bg-white `}
            >
              <div className="mb-2">
                <h4>Settings</h4>
              </div>
              <div className={`${styles.settingsOptions} d-flex`}>
                <div
                  className={`${styles.option} ${activeTab === "Hunt Strategy" ? styles.active : ""
                    }`}
                  onClick={() => handleTabClick("Hunt Strategy")}
                >
                  {/* <span className={styles.icon}>⚙️</span> */}
                  <span className={styles.label}>Agent</span>
                </div>
                {currentUser.user_type === "admin" && <div
                  className={`${styles.option} ${activeTab === "Label" ? styles.active : ""
                    }`}
                  onClick={() => handleTabClick("Label")}
                >
                  {/* <span className={styles.icon}>👤</span> */}
                  <span className={styles.label}>Label</span>
                </div>}

                {/* <div className={`${styles.option} ${activeTab === "Auto Reply" ? styles.active : ""
                  }`}
                  onClick={() => handleTabClick("Auto Reply")} >
                  <span className={styles.label}>Auto Reply</span>
                </div>*/}
                <div className={`${styles.option} ${activeTab === "faq" ? styles.active : ""
                  }`}
                  onClick={() => handleTabClick("faq")} >
                  <span className={styles.label}>FAQs</span>
                </div>
              </div>


            </div>

            <div className="w-100 d-flex justify-content-center align-items-start" style={{ height: "85vh", overflowY: "auto" }}>
              {activeTab === "Hunt Strategy" && <div className={`${styles.card} bg-white`}>
                <h4 className={`${styles.header}`}>Agent</h4>
                <div className="d-flex justify-content-center align-items-center">
                  <form onSubmit={handleUpdate} style={{ width: "100%" }}>
                    <div className="mb-4 d-flex justify-content-between align-items-center">
                      <div className="d-flex ml-3">
                        <label>Keyword Priority</label>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={tagStatus}
                            onChange={handleTag}
                          />
                          <span className="slider round"></span>
                        </label>
                      </div>
                    </div>
                    <div className="col-md-12 mx-auto mb-4">
                      <label className="mb-2">
                        Hunt Strategy
                        <span style={{ color: "red" }}>*</span>
                      </label>
                      <div className="mb-2">
                        <select
                          className="form-control"
                          value={selectedHs}
                          onChange={handleHuntStrategyChange}
                          required
                        >
                          <option value="">
                            Select Hunt Strategy
                          </option>
                          <option value="random">Random</option>
                          <option value="sequence">Sequence</option>
                          <option value="parallel">Parallel</option>
                          {/* <option value="template">Template</option>
                          <option value="keyword">Keyword</option>
                          <option value="FirstMessage">First Message</option> */}
                        </select>
                      </div>
                    </div>
                    {selectedHs === "template" && (
                      <div className="mb-3">
                        <label className="mb-2">
                          Assign Agents <span style={{ color: "red" }}>*</span>
                        </label>
                        {assignments.map((assignment, index) => {
                          const selectedTemplates = assignments
                            .filter((_, i) => i !== index)
                            .map(a => Number(a.template));


                          const availableTemplates = templateList.filter(
                            item => item.value === Number(assignment.template) || !selectedTemplates.includes(item.value)
                          );

                          return (
                            <div className="d-flex align-items-center mb-2" key={index}>
                              <select
                                className="form-control me-2"
                                value={assignment.template}
                                onChange={(e) => handleAssignmentChange(index, "template", e.target.value)}
                                required
                              >
                                <option value="">Select Template</option>
                                {availableTemplates.map((item) =>
                                  <option key={item.value} value={item.value}>{item.label}</option>
                                )}
                              </select>

                              <select
                                className="form-control me-2"
                                value={assignment.agent}
                                onChange={(e) => handleAssignmentChange(index, "agent", e.target.value)}
                                required
                              >
                                <option value="">Select Agent</option>
                                {agents.map((item) =>
                                  <option key={item.value} value={item.value}>{item.label}</option>
                                )}
                              </select>

                              <button type="button" className="btn btn-danger btn-sm" onClick={() => removeAssignment(index)}>×</button>
                            </div>
                          );
                        })}

                        <button type="button" className="btn btn-primary btn-sm" onClick={addAssignment}>+ Add</button>
                      </div>
                    )}

                    {selectedHs === "keyword" && (
                      <div className="mb-4">
                        <label className="mb-2">Keyword Assignments <span style={{ color: "red" }}>*</span></label>

                        {keywordAssignments.map((assignment, index) => (
                          <div key={index} className="d-flex flex-column mb-3 p-3 border rounded">
                            <input
                              type="text"
                              className="form-control mb-2"
                              placeholder="Enter keywords (comma separated)"
                              value={assignment.keywords.join(", ")}
                              onChange={(e) =>
                                handleKeywordAssignmentChange(index, "keywords", e.target.value.split(",").map(k => k.trim()).filter(Boolean))
                              }
                            />

                            <Select
                              isMulti
                              className="basic-multi-select"
                              classNamePrefix="select"
                              placeholder="Select Agents"
                              options={agents}
                              value={agents.filter(agent => assignment.agentIds.includes(agent.value))}
                              onChange={(selectedOptions) =>
                                handleKeywordAssignmentChange(
                                  index,
                                  "agentIds",
                                  selectedOptions.map(opt => opt.value)
                                )
                              }
                            />

                            <button
                              type="button"
                              className="btn btn-outline-danger btn-sm mt-2 align-self-end"
                              onClick={() => removeKeywordAssignment(index)}
                            >
                              × Remove
                            </button>
                          </div>
                        ))}

                        <button
                          type="button"
                          className="btn btn-outline-primary btn-sm ml-2"
                          onClick={addKeywordAssignment}
                        >
                          + Add Keyword Assignment
                        </button>
                      </div>
                    )}
                    {selectedHs === "FirstMessage" && (
                      <div className="mb-4">
                        <label className="mb-2">
                          Assign Agents by First Message <span style={{ color: "red" }}>*</span>
                        </label>

                        {firstMessageAssignments.map((assignment, index) => (
                          <div key={index} className="d-flex align-items-start mb-3 flex-wrap gap-2">
                            <input
                              type="text"
                              className="form-control me-2"
                              placeholder="Enter first message"
                              value={assignment.message}
                              onChange={(e) =>
                                handleFirstMessageAssignmentChange(index, "message", e.target.value)
                              }
                              style={{ minWidth: "200px" }}
                            />
                            <div className="d-flex w-100 justify-content-between align-items-center">
                              <Select
                                isMulti
                                options={agents}
                                value={agents.filter(a => assignment.agentIds.includes(a.value))}
                                onChange={(selectedOptions) => {
                                  const values = selectedOptions.map(o => o.value);
                                  handleFirstMessageAssignmentChange(index, "agentIds", values);
                                }}
                                className="basic-multi-select w-75"
                                classNamePrefix="select"
                              />

                              <button
                                type="button"
                                className="btn btn-danger btn-sm"
                                onClick={() => removeFirstMessageAssignment(index)}
                              >
                                × Remove
                              </button>

                            </div>


                          </div>
                        ))}

                        <button
                          type="button"
                          className="btn btn-primary btn-sm"
                          onClick={addFirstMessageAssignment}
                        >
                          + Add
                        </button>
                      </div>
                    )}


                    {/* <div className="mb-4">
                      <label className="mb-2 fw-bold">Contact List → Agent Mapping</label>

                      {manualAssignments.map((assignment, index) => {
                        const selectedListIds = manualAssignments
                          .filter((_, i) => i !== index)
                          .map(a => a.contactListId);

                        const availableContactLists = contactLists.filter(
                          (list) => list.value === assignment.contactListId || !selectedListIds.includes(list.value)
                        );

                        return (
                          <div key={index} className="d-flex align-items-center gap-2 mb-2">
                           
                            <select
                              className="form-control"
                              value={assignment.contactListId}
                              onChange={(e) => handleManualAssignmentChange(index, "contactListId", e.target.value)}
                              required
                            >
                              <option value="">Select Contact List</option>
                              {availableContactLists.map((list) => (
                                <option key={list.value} value={list.value}>
                                  {list.label}
                                </option>
                              ))}
                            </select>

                            
                            <select
                              className="form-control"
                              value={assignment.agentId}
                              onChange={(e) => handleManualAssignmentChange(index, "agentId", e.target.value)}
                              required
                            >
                              <option value="">Select Agent</option>
                              {agents.map((agent) => (
                                <option key={agent.value} value={agent.value}>
                                  {agent.label}
                                </option>
                              ))}
                            </select>

                            <button
                              type="button"
                              className="btn btn-outline-danger btn-sm"
                              onClick={() => removeManualAssignment(index)}
                              aria-label="Remove Assignment"
                            >
                              ×
                            </button>
                          </div>
                        );
                      })}

                     
                      <button
                        type="button"
                        className="btn btn-primary btn-sm mt-2 ml-2"
                        onClick={addManualAssignment}
                      >
                        + Add Assignment
                      </button>
                    </div> */}

                    <div className="mb-4 d-flex justify-content-between align-items-center">
                      <div className="d-flex ml-3">
                        <label>Sticky Agent</label>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={isSticky}
                            onChange={handleSticky}
                          />
                          <span className="slider round"></span>
                        </label>
                      </div>
                    </div>
                    {selectedHs === "parallel" && <div className="col-md-12 mx-auto mb-4">
                      <label className="mb-2">
                        Fallback Strategy
                        <span style={{ color: "red" }}>*</span>
                      </label>
                      <div className="mb-2">
                        <select
                          className="form-control"
                          value={fbStrategy}
                          onChange={(e) => setFbStrategy(e.target.value)}
                          required
                        >
                          <option value="">
                            Select Fallback Strategy
                          </option>
                          <option value="random">Random</option>
                          <option value="sequence">Sequence</option>
                        </select>
                      </div>
                    </div>}

                    <div className="form-group col-md-12 mb-4">
                      {agentList.length > 0 && (<>
                        <label className="mb-2">
                          {selectedOption === "agent"
                            ? "Agents"
                            : selectedOption === "team"
                              ? "Select Team"
                              : "Managers"}
                        </label>

                        <div className="mt-1">
                          <Select
                            placeholder={`Select ${selectedOption}`}
                            onChange={handleTeamSelect}
                            options={agentList}
                            isLoading={isLoading}
                            value={selectedTeam}
                            styles={customStyles}
                            classNamePrefix="select"
                            hideSelectedOptions={false}
                            required
                          />
                        </div>
                      </>
                      )}
                    </div>

                    <div className="col-md-12 mx-auto mb-4">

                      <label>Agent Reassign Waiting Time (minutes) </label>


                      <div style={{ fontSize: ".8rem" }} className="mb-2">
                        Note<span className="text-danger word-break">*</span> : After this time the chat will be reassigned to another agent
                      </div>

                      <input
                        type="number"
                        className="form-control"
                        placeholder="Set waiting time (minutes)"
                        value={waitingTime}
                        onChange={(e) => setWaitingTime(e.target.value)}
                        min="1"
                        step="1"
                        required
                      />
                    </div>
                    <div className="mb-4">
                      <label htmlFor="" className="mb-2">Maximum chats</label>
                      <input
                        type="number"
                        className="form-control"
                        placeholder="Enter maximum number chats that can be assigned to the agent at a time"
                        min="1"
                        step="1"
                        value={maxChat}
                        onChange={(e) => setMaxChat(e.target.value)}
                        required
                      />
                    </div>



                    <div
                      className="form-group col-md-2 w-100"
                    >
                      <button
                        type="submit"
                        className="btn btn-primary w-100"
                      >
                        Update
                      </button>
                    </div>

                  </form>
                </div>
              </div>}
              {activeTab === "Label" &&
                <div className={`${styles.labelCard} bg-white shadow p-3`}>

                  <h4 className={`${styles.header}`}>Add Label</h4>
                  <div className={`${styles.labelList} mb-4`}>
                    {labels.map((label, index) => (
                      <div key={index} className="position-relative d-inline-block">
                        <LabelCard labelData={label} />
                        <button
                          className="position-absolute bottom-50  end-0 translate-middle bg-white border rounded-circle text-muted  hover-opacity-100 p-1"

                          onClick={() => handleDeleteClick(label)}
                          style={{ width: "20px", height: "20px", fontSize: "12px", lineHeight: "1" }}
                        >
                          ✖
                        </button>
                      </div>
                    ))}
                  </div>
                  <div className={`${styles.inputGroup} d-flex`}>
                    <input
                      type="text"
                      value={label.name}
                      onChange={(e) => setLabel({ ...label, name: e.target.value })}
                      className={`${styles.input} form-control mb-3`}
                      placeholder="Enter a label text"
                      required
                    />
                    <input
                      type="text"
                      value={label.description}
                      onChange={(e) => setLabel({ ...label, description: e.target.value })}
                      className={`${styles.input} form-control mb-3`}
                      placeholder="Enter label description"
                      required={true}
                    />
                    {/* <input
                      type="text"
                      value={label.action}
                      onChange={(e) => setLabel({ ...label, action: e.target.value })}
                      className={`${styles.input} form-control mb-3`}
                      placeholder="Enter label action"
                      required={true}
                    /> */}


                    <div className="d-flex justify-content-start align-items-center">

                      <label htmlFor="labelColor" className="me-3 fw-semibold">Select label color</label>
                      <input
                        type="color"
                        id="labelColor"
                        value={label.color_code || "#28a745"}
                        onChange={(e) => setLabel({ ...label, color_code: e.target.value })}
                        className={`${styles.colorPicker} form-control form-control-color mb-3`}
                        title="Choose label color"
                      />

                    </div>


                    <button
                      className={`${styles.addButton} btn btn-primary`}
                      onClick={handleAddLabel}
                    >
                      Add
                    </button>
                  </div>


                </div>
              }
              {
                activeTab === "Auto Reply" && <AutoReplyRules />
              }
              {
                activeTab === "faq" && <FaqSettings />
              }

            </div>

            <DeleteModal
              show={showModal}
              onConfirm={handleRemoveLabel}
              onCancel={handleCancel}
            />




          </main>
        </div>
      </div>
    </div>
  );
};

export default Setting;
