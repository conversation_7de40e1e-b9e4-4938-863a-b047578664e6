import React, { useState } from 'react';
import DatePicker from "react-datepicker";
import Select from 'react-select';


const FilterSortBar = ({ agents, labels, onFilterChange, fromDate, toDate, loading, handleDateChange }) => {
    const [filters, setFilters] = useState({ sort: "recent", search: "", agent: "", label: "" });


    const updateFilter = (key, value) => {
        const newFilters = { ...filters, [key]: value };
        setFilters(newFilters);
        onFilterChange?.(newFilters);
    };

    const resetFilters = () => {
        setFilters({ sort: "recent", search: "", agent: "", label: "" });
        onFilterChange?.({ sort: "recent", search: "", agent: "", label: "" });
    };

    const labelOptions = labels.map(label => ({
        value: label.id,
        label: label.name
    }));


    return (
        <div className="p-3 bg-light w-100 rounded shadow mb-4">
            <div className="d-flex flex-nowrap overflow-auto gap-3" style={{ whiteSpace: 'nowrap', scrollbarWidth: 'none' }}>

                <div className="flex-shrink-0 d-flex align-items-start" style={{ minWidth: '200px', flex: 1 }}>
                    <DatePicker
                        selectsRange
                        startDate={fromDate}
                        endDate={toDate}
                        onChange={(update) => handleDateChange(update)}
                        maxDate={fromDate instanceof Date ? new Date(fromDate.getFullYear(), fromDate.getMonth() + 1, fromDate.getDate()) : new Date()}
                        placeholderText="Select a date range"
                        className="form-control"
                        disabled={loading}
                    />
                    <span className='text-danger ml-2 '>*</span>
                </div>


                {/* Search */}
                <div className="flex-shrink-0" style={{ minWidth: '200px', flex: 1 }}>
                    <input
                        type="text"
                        value={filters.search || ""}
                        className="form-control"
                        placeholder="Search by name, email, or phone"
                        onChange={(e) => updateFilter('search', e.target.value)}
                    />
                </div>

                {/* Agent Filter */}
                <div className="flex-shrink-0" style={{ minWidth: '150px', flex: 1 }}>
                    <select
                        className="form-select"
                        value={filters.agent || ""}
                        onChange={(e) => updateFilter('agent', e.target.value)}>
                        <option value="">All Agents</option>
                        {agents.map(agent => (
                            <option key={agent.id} value={agent.name}>{agent.name}</option>
                        ))}
                    </select>
                </div>

                {/* Label Filter */}
                <div className="flex-shrink-0" style={{ minWidth: '150px', flex: 1 }}>
                    <select
                        value={filters.label || ""}
                        className="form-select"
                        onChange={(e) => updateFilter('label', e.target.value)}>
                        <option value="">All Labels</option>
                        {labels.length > 0 && labels.map(label => (
                            <option key={label.id} value={label.id}>{label.name}</option>
                        ))}
                    </select>
                </div>

                {/* Sort */}
                <div  
                    className="flex-shrink-0"
                    style={{ minWidth: '160px', flex: 1 }}>
                    <select 
                        value={filters.sort || "recent"}
                        className="form-select" onChange={(e) => updateFilter('sort', e.target.value)}>
                        <option value="recent">Newest First</option>
                        <option value="oldest">Oldest First</option>
                        <option value="nameAsc">Name A-Z</option>
                        <option value="nameDesc">Name Z-A</option>
                        <option value="activity">Last Activity</option>
                    </select>
                </div>

                {/* Unread Checkbox */}
                {/* <div className="flex-shrink-0 d-flex align-items-center" style={{ flex: 1 }}>
                    <div className="form-check form-switch d-flex align-items-center">
                        <input
                            className="form-check-input"
                            type="checkbox"
                            id="unreadOnly"
                            onChange={(e) => updateFilter('unreadOnly', e.target.checked)}
                        />
                        <label className="form-check-label ms-2" htmlFor="unreadOnly">
                            Unread only
                        </label>
                    </div>
                </div> */}

                {/* Reset Button */}
                <div className="flex-shrink-0" style={{ flex: 1 }}>
                    <button className="btn btn-secondary btn-outline-secondary" onClick={resetFilters}>Reset</button>
                </div>
            </div>
        </div>

    );
};

export default FilterSortBar;
