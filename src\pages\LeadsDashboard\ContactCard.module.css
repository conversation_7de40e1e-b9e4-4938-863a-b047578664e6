.contactCard {
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: white;
  transition: box-shadow 0.2s ease, max-height 0.3s ease;
  min-height: 70px;
}

.contactCard:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contactCard .card-body {
  padding: 12px;
}

.contactCard h6 {
  font-weight: 600;
  font-size: 14px;
}

.unread {
  font-weight: 700;
  color: #212529;
}

.contactCard p {
  font-size: 13px;
  margin-bottom: 4px;
  color: #555;
}

.labelBadge {
  background-color: #607d8b;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 12px;
}

.expandSection {
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.dropdownArrow {
  position: absolute;
  top: 8px;
  right: 12px;
  font-size: 14px;
  cursor: pointer;
  color: #333;
  transition: transform 0.2s;
  padding: 0 .5rem ;
}

.activityArrow{
  position: absolute;
  top: -30px;
  right:-50px;
  font-size: 14px;
  cursor: pointer;
  color: #333;
  padding: .5rem;
}

.contactCard:hover .dropdownArrow {
  opacity: 1;
}

.dragHandle {
  position: absolute;
  top: 4px;
  left: 6px;
  font-size: 18px;
  color: #888;
  user-select: none;
  z-index: 10;
}

.dragHandle:active {
  cursor: grabbing;
}

.threeDotsHandle {
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  cursor: move;
  color: #333;
  font-weight: bold;
  transition: transform 0.2s;
  z-index: 10; /* Ensure it stays above other elements */
}

/* .threeDotsHandle:active {
  cursor: grabbing;
} */

.threeDots {
  font-size: 18px;
  color: #333;
}

.contactCard:hover .threeDotsHandle {
  opacity: 1;
}