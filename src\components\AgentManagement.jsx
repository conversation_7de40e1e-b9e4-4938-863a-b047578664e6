import React, { useContext, useEffect, useState } from "react";
import "../assets/css/agent.css";
import dayjs from "dayjs";
import { BASE_URL } from "../api/api";
import { AuthContext } from "../context/AuthContext";
import { toast } from "react-toastify";
import { Link } from "react-router-dom";
import {
  DataGrid,
  GridToolbarContainer,
  GridToolbarExport,
  GridToolbarFilterButton,
  GridToolbarQuickFilter,
} from "@mui/x-data-grid";
import { Box, Button, Fade, Modal, Switch } from "@mui/material";
import Backdrop from "@mui/material/Backdrop";
import Select from "react-select";
import axios from "axios";
import { BASE_URL2 } from "../api/api";
import { ChatState } from "../context/AllProviders";
import { useNavigate } from "react-router-dom";
import { FiExternalLink } from "react-icons/fi";
import AgentBreakModal from "./AgentBreakModal/AgentBreakModal";
const AgentManagement = () => {
  const [agentList, setAgentList] = useState([]);
  const [selectedAgentType, setSelectedAgentType] = useState([]);
  const [updateForm, setUpdateForm] = useState(false);
  const [updateValue, setupdateValue] = useState({});
  const [name, setName] = useState("");
  const [updateCheckBox, setUpdateCheckBox] = useState({});
  const [id, setId] = useState(null);
  const [selectedBtn, setSelectedBtn] = useState("agent");
  const [selectedRows, setSelectedRows] = useState([]);
  const [open, setOpen] = useState(false);
  const [selectedAssign, setSelectedAssign] = useState();
  const [listToTransfer, setListToTransfer] = useState();
  const [transferTo, setTransferTo] = useState();
  const [deletePopup, setDeletePopup] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedAgentForBreak, setSelectedAgentForBreak] = useState(null);
  const [isBreakModalOpen, setBreakModalOpen] = useState(false);
  const [isExpiryClose, setIsExpiryClose] = useState(false);
  const [checkedMap, setCheckedMap] = useState({});
  const [userBalance, setUserBalance] = useState(0);
  const [renewPopup, setRenewPopup] = useState(false);
  const [agentCharge, setAgentCharge] = useState(0);
  const [renewID, setRenewID] = useState("")
  const { currentUser } = useContext(AuthContext);
  const { socket } = ChatState();
  const permission = currentUser.parent_id
    ? JSON.parse(currentUser?.permission)
    : null;

  const navigate = useNavigate();

  const buttonHandler = (event) => {
    const checked = event.target.checked;

    setupdateValue(prev => ({
      ...prev,
      auto_renew_status: checked ? 1 : 0
    }));
  };


  //socket
  useEffect(() => {
    Balance();

    if (!socket) return;
    const handleOnlineAgent = (data) => {
      if (data?.user_type !== "admin") {
        setAgentList((prevAgentList) => {
          const index = prevAgentList.findIndex(
            (selectedItem) => selectedItem.id === data.user_id
          );

          if (index !== -1) {
            const updatedItems = [...prevAgentList];
            updatedItems[index] = {
              ...updatedItems[index],
              online: 1,
            };
            return updatedItems;
          }
          return prevAgentList;
        });
      }
    };

    const handleOfflineAgent = (data) => {
      if (data?.user_type !== "admin") {
        setAgentList((prevAgentList) => {
          const index = prevAgentList.findIndex(
            (selectedItem) => selectedItem.id === data.user_id
          );

          if (index !== -1) {
            const updatedItems = [...prevAgentList];
            updatedItems[index] = {
              ...updatedItems[index],
              online: 0,
              last_seen_datetime: new Date(),
            };
            return updatedItems;
          }
          return prevAgentList;
        });
      }
    };

    socket.on("online agent", handleOnlineAgent);
    socket.on("offline agent", handleOfflineAgent);

    return () => {
      socket.off("online agent", handleOnlineAgent);
      socket.off("offline agent", handleOfflineAgent);
      // socket.disconnect();
    };
  }, [currentUser, socket]);

  const Balance = async () => {
    let paramData = {
      user_id: currentUser.user_id,
      method: "retrieve_user_balance_with_agent_price",
      token: currentUser.token,
      user_type: currentUser.user_type
    }
    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent_setting`, paramData);
      if (data.success) {
        setUserBalance(data?.data?.user_balance);
        setAgentCharge(data?.data?.agent_billing_dtl?.per_agent_price)
      }
      else {
        setUserBalance(0);
        setAgentCharge(0);
      }
    } catch (error) {
      setUserBalance(0);
      setAgentCharge(0);
    }
  }

  useEffect(() => {
    expiryDateHandler();
  }, [])

  const expiryDateHandler = () => {
    const today = new Date();
    const expiryStr = "13-07-2025";  // DD-MM-YYYY format
    const [day, month, year] = expiryStr.split('-');
    const expiryDate = new Date(`${year}-${month}-${day}`);

    // Calculate the difference in days
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 4 && diffDays >= 0) {
      setIsExpiryClose(true);
    } else {
      setIsExpiryClose(false);
    }
  };





  const handleCheckboxChange = (event) => {
    const { name, checked } = event.target;
    setUpdateCheckBox((prevState) => ({
      ...prevState,
      [name]: checked ? 1 : 0,
    }));
  };

  useEffect(() => {
    if (currentUser.parent_id) {
      showAgent();
    }
  }, [currentUser]);

  const showAgent = async () => {
    setLoading(true);

    const data = {
      user_id: currentUser.parent_id,
      method: "retrieve_agent",
      token: currentUser.parent_token,
      user_type: currentUser.user_type,
      agent_id: currentUser.user_id,
    };

    try {
      const response = await fetch(`${BASE_URL2}/whatsapp_agent`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json; charset=UTF-8",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        const agents = result.data;

        const initialCheckedMap = {};
        const today = dayjs(); // Use dayjs instead of Date

        const processedAgents = agents.map((agent) => {
          initialCheckedMap[agent.id] = agent.availability === 0;

          let isExpiringSoon = false;

          if (agent.exp_date) {
            // Parse using dayjs with expected format
            const expiryDate = dayjs(agent.exp_date, "DD-MM-YYYY", true); // strict parsing

            if (expiryDate.isValid()) {
              const diffDays = expiryDate.diff(today, "day");
              // console.log(`Agent: ${agent.name}, Days till expiry: ${diffDays}`);

              if (diffDays <= 4 && diffDays >= 0) {
                isExpiringSoon = true;
              }
            } else {
              console.warn(`Invalid expiry date for agent ${agent.name}:`, agent.exp_date);
            }
          }

          return {
            ...agent,
            isExpiringSoon,
          };
        });

        setCheckedMap(initialCheckedMap);
        setAgentList(processedAgents); // updates the UI table
      } else {
        console.log("Failed to fetch agent data:", result.message || response.statusText);
      }
    } catch (error) {
      console.error("Error occurred while fetching agent data:", error);
    }

    setLoading(false);
  };


  const handleDeletePopup = (id) => {
    if (permission.delete === 0) {
      return toast.info("Does Not Have Permission to Delete");
    }
    setDeletePopup(true);
    setId(id);
  };

  const DeleteAgent = async () => {
    const data = {
      user_id: currentUser.parent_id,
      method: "delete",
      token: currentUser.parent_token,
      id: id,
    };


    try {
      const response = await fetch(`${BASE_URL}/agent.php`, {
        method: "POST",
        headers: {
          "Content-type": "application/json; charset=UTF-8",
        },
        body: JSON.stringify(data),
      });
      const result = await response.json();
      if (result.success) {
        showAgent();
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error occurred while deleting data:", error);
      toast.error(error.message);
    }
  };

  const toggleUpdate = async (id) => {
    if (permission.update === 0) {
      return toast.info("Does Not Have Permission to Update");
    }

    const data = {
      user_id: currentUser.parent_id,
      method: "retrieveid",
      token: currentUser.parent_token,
      id: id,
    };

    try {
      const response = await fetch(`${BASE_URL}/agent.php`, {
        method: "POST",
        headers: {
          "Content-type": "application/json; charset=UTF-8",
        },
        body: JSON.stringify(data),
      });
      const result = await response.json();
      if (result.success) {
        setupdateValue(result.data[0]);
        setName(result.data[0].name);
        setUpdateForm(true);
        setId(id);
        const newData = result?.data[0]?.permission;
        const permission = JSON.parse(newData);
        setUpdateCheckBox(permission);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error occurred while retrieving data:", error);
      toast.error(error.message);
    }
  };

  const RenewAgentPopup = (id) => {
    setRenewPopup(true);
    setRenewID(id)
  }

  const RenewAgent = async () => {
    let paramData = {
      user_id: currentUser.user_id,
      method: "renew_agent",
      token: currentUser.token,
      user_type: currentUser.user_type,
      agent_id: renewID
    }
    try {
      let { data } = await axios.post(`${BASE_URL2}/whatsapp_agent_setting`, paramData)
      if (data.success) {
        setRenewPopup(false);
        setRenewID("");
        showAgent();
      }
    } catch (error) {
      console.log(error)
    }
  }

  const formatToLocalDate = (dateString) => {
  const date = new Date(dateString);

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short'
  }).format(date);
};

  const handleChange = (e) => {
    setupdateValue({ ...updateValue, [e.target.name]: e.target.value });
  };

  const isValid = () => {
    if (!updateValue.name) {
      return false;
    }
    if (!updateValue.mobile || updateValue.mobile.length !== 10) {
      return false;
    }
    return true;
  };

  const updateData = async (event) => {
    event.preventDefault();
    if (isValid()) {
      const data = {
        user_id: currentUser.parent_id,
        method: "update",
        token: currentUser.parent_token,
        id: id,
        permission: JSON.stringify(updateCheckBox),
        name: updateValue.name,
        agent_type: updateValue.agent_type,
        mobile: updateValue.mobile,
        email: updateValue.email,
        auto_renew: updateValue.auto_renew_status
      };
      try {
        const response = await fetch(`${BASE_URL}/agent.php`, {
          method: "POST",
          headers: {
            "Content-type": "application/json; charset=UTF-8",
          },
          body: JSON.stringify(data),
        });
        const result = await response.json();

        if (result.success) {
          showAgent();
          toast.success(result.message);
          setUpdateForm(false);
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        console.error("Error occurred while updating data:", error);
        toast.error(error.message);
      }
    }
  };

  const handleTransfer = async () => {
    const transfertoAgent = agentList.filter(
      (list) => list.id === transferTo.value
    );
    const datafortransfer = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: `${selectedBtn === "agent" ? "agent_transfer" : "manager_transfer"
        }`,
      transfer_to: transfertoAgent,
      user_type: currentUser.user_type,
      agent_list: selectedRows,
    };
    try {
      const { data } = await axios.post(
        `${BASE_URL2}/whatsapp_agent`,
        datafortransfer
      );
      if (data.success === true) {
        toast.success(data.message);
        setTransferTo("");
        setListToTransfer("");
        setSelectedAssign("");
        setSelectedRows("");
        setOpen(false);
        window.location.reload();
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  useEffect(() => {
    const filterAgent = agentList.filter((list) => {
      return list.agent_type === "agent";
    });
    setSelectedAgentType(filterAgent);
  }, [agentList]);
  const AgentBtn = () => {
    setSelectedBtn("agent");
    const filterAgent = agentList.filter((list) => {
      return list.agent_type === "agent";
    });
    setSelectedAgentType(filterAgent);
  };
  const ManagerBtn = () => {
    setSelectedBtn("manager");
    const filterAgent = agentList.filter((list) => {
      return list.agent_type === "manager";
    });
    setSelectedAgentType(filterAgent);
    setSelectedAssign({ value: "team", label: "Team" });
    const filter_Agent = agentList
      .filter((list) => list.agent_type === "team")
      .map((list) => ({
        value: list.id,
        label: list.name,
      }));

    setListToTransfer(filter_Agent);
  };
  const TeamBtn = () => {
    setSelectedBtn("team");
    const filterAgent = agentList.filter((list) => {
      return list.agent_type === "team";
    });
    setSelectedAgentType(filterAgent);
  };

  const handleShowMobile = async (agentId, status) => {
    const newStatus = status === "1" ? 1 : 0;

    // **Optimistic UI Update**
    setAgentList((prevState) =>
      prevState.map((agent) =>
        agent.id === agentId ? { ...agent, number_visibility: newStatus } : agent
      )
    );

    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "number_visibility",
      agent_id: agentId,
      status: status,
    };

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);

      if (data.success) {
        toast.success("Updated Successfully");
      } else {
        throw new Error("API request failed"); // Force catch block execution
      }
    } catch (error) {
      toast.error("Something went wrong, please try again later");

      // **Revert state on failure**
      setAgentList((prevState) =>
        prevState.map((agent) =>
          agent.id === agentId ? { ...agent, number_visibility: newStatus === 1 ? 0 : 1 } : agent
        )
      );
    }
  };

  const handleNavigate = (agentId, agentName) => {
    navigate(`/agent-management/agent/setting/?id=${agentId}&name=${agentName}`)
  }

  const handleEndAgentBreak = async agentId => {
    if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
    const payload = {
      user_id: currentUser.user_id,
      token: currentUser.token,
      method: "delete_agent_break_by_parent",
      user_type: currentUser.user_type,
      agent_id: agentId

    }

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent_setting`, payload);
      if (data.success) {
        toast.info("Break ended")
      }

    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.")

      } else {
        console.error(error);
        toast.error("Something went wrong, please try again later")
      }

    }
  }

  const handleBreakModalClose = (confirm, agentId) => {
    setBreakModalOpen(false);
    if (!confirm) {
      setCheckedMap((prev) => ({ ...prev, [agentId]: false }));
    }

  }

  const BreakToggleCell = ({ agentId }) => {
    const checked = checkedMap.hasOwnProperty(agentId) ? checkedMap[agentId] : false;

    const handleChange = (event) => {
      const isOn = event.target.checked;

      if (isOn) {
        setCheckedMap((prev) => ({ ...prev, [agentId]: true }));
        setSelectedAgentForBreak(agentId)
        setBreakModalOpen(true);

      } else {
        setCheckedMap((prev) => ({ ...prev, [agentId]: false }));
        handleEndAgentBreak(agentId)
        setSelectedAgentForBreak(null)

      }

    };
    return (

      <Switch
        checked={checked}
        onChange={handleChange}
        color="success"
        inputProps={{ "aria-label": "Agent break toggle" }}
      />

    );
  };


  const columnsAgent = [
    {
      field: "online",
      headerName: "Active Status",
      width: 170,
      sortable: true,
      renderCell: (params) => {
        const isOnline = params.row.online === 1;
        const lastSeen = params.row.last_seen_datetime;

        return isOnline ? (
          <>
            <div
              style={{
                display: "inline-block",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                backgroundColor: "green",
                marginRight: "8px",
              }}
            />
            <span>Online</span>
          </>
        ) : (
          <span style={{ color: params.row.isExpiringSoon ? "red" : "black" }}>{dayjs(lastSeen).format("DD/MM/YYYY h:mm A")}</span>
        );
      },
    },
    {
      field: "name",
      headerName: "Name",
      width: 170,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return (
          <div className="d-flex align-items-center gap-1" style={{ color }}>
            <span>{params.row.name}</span>
            <Link to={`/agent-details/${params.row.id}`}>
              <FiExternalLink size={16} style={{ marginLeft: "5px" }} />
            </Link>
          </div>
        );
      },
    },
    {
      field: "email",
      headerName: "Email",
      width: 170,
      editable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.email}</span>;
      },
    },
    {
      field: "mobile",
      headerName: "Mobile",
      width: 150,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.mobile}</span>;
      },
    },
    {
      field: "manager_name",
      headerName: "Manager",
      width: 160,
      hide: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.manager_name}</span>;
      },
    },
    {
      field: "team_name",
      headerName: "Team",
      width: 150,
      hide: selectedBtn === "team",
      editable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.team_name}</span>;
      },
    },
    {
      field: "update_first_password",
      headerName: "Verified?",
      description: "Whether the user updated their first password",
      sortable: false,
      width: 160,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        const isVerified = params.row.update_first_password === 1;
        return <p style={{ color }}>{isVerified ? "Yes" : "No"}</p>;
      },
    },
    {
      field: "is_active",
      headerName: "Account Status",
      sortable: false,
      width: 160,
      renderCell: (params) => {
        const isActive = params.row.is_active === 1;
        return isActive ? (
          <span className="badge badge-success" style={{ background: "green" }}>
            Active
          </span>
        ) : (
          <span className="badge badge-danger" style={{ background: "red" }}>
            Inactive
          </span>
        );
      },
    },
    {
      field: "break",
      headerName: "Break",
      description: "Toggle to start or end agent break.",
      sortable: false,
      width: 160,
      renderCell: (params) => {
        return <BreakToggleCell agentId={params.row.id} />;
      },
    },
    {
      field: "Expiry",
      headerName: "Expiry Date",
      width: 160,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{formatToLocalDate(params.row.exp_date)}</span>;
      },
    },
    {
      field: "",
      headerName: "Action",
      description: "Actions for each agent",
      sortable: false,
      width: 140,
      renderCell: (params) => {
        return (
          <div>
            <span
              className="mx-2"
              data-bs-toggle="tooltip"
              data-bs-title="Update"
              onClick={() => toggleUpdate(params.row.id)}
              style={{
                cursor: "pointer",
                color: permission.update === 1 ? "blue" : "grey",
              }}
            >
              <i className="bx bx-edit"></i>
            </span>
            <span
              className="mx-2"
              data-bs-toggle="tooltip"
              data-bs-title="Delete"
              onClick={() => handleDeletePopup(params.row.id)}
              style={{
                cursor: "pointer",
                color: permission.delete === 1 ? "red" : "grey",
              }}
            >
              <i className="bx bx-trash"></i>
            </span>
            {params.row.auto_renew_status === 0 && (
              <span
                style={{ cursor: "pointer", padding: "8px" }}
                className="badge bg-info ml-2"
                onClick={() => RenewAgentPopup(params.row.id)}
              >
                Renew
              </span>
            )}
          </div>
        );
      },
    },
  ];
  const columnsManager = [
    {
      field: "online",
      headerName: "Active Status",
      width: 170,
      sortable: true,
      renderCell: (params) => {
        const isOnline = params.row.online === 1;
        const lastSeen = params.row.last_seen_datetime;

        return isOnline ? (
          <>
            <div
              style={{
                display: "inline-block",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                backgroundColor: "green",
                marginRight: "8px",
              }}
            />
            <span>Online</span>
          </>
        ) : (
          <span style={{ color: params.row.isExpiringSoon ? "red" : "black" }}>{dayjs(lastSeen).format("DD/MM/YYYY h:mm A")}</span>
        );
      },
    },
    {
      field: "name",
      headerName: "Name",
      width: 150,
      editable: true,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.name}</span>;
      },
    },
    {
      field: "email",
      headerName: "Email",
      width: 190,
      editable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.email}</span>;
      },
    },
    {
      field: "mobile",
      headerName: "Mobile",
      width: 160,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.mobile}</span>;
      },
    },
    {
      field: "team_name",
      headerName: "Team",
      hide: selectedBtn === "team",
      editable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.team_name}</span>;
      },
    },
    {
      field: "Expiry",
      headerName: "Expiry Date",
      width: 160,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{formatToLocalDate(params.row.exp_date)}</span>;
      },
    },
    {
      field: "",
      headerName: "Action",
      sortable: false,
      width: 160,
      renderCell: (params) => (
        <div>
          <span
            className="mx-2"
            data-bs-toggle="tooltip"
            data-bs-title="Update"
            onClick={() => toggleUpdate(params.row.id)}
            style={{
              cursor: "pointer",
              color: permission.update === 1 ? "blue" : "grey",
            }}
          >
            <i className="bx bx-edit"></i>
          </span>
          <span
            className="mx-2"
            data-bs-toggle="tooltip"
            data-bs-title="Delete"
            onClick={() => handleDeletePopup(params.row.id)}
            style={{
              cursor: "pointer",
              color: permission.update === 1 ? "red" : "grey",
            }}
          >
            <i className="bx bx-trash"></i>
          </span>
          {params.row.auto_renew_status === 0 && (
            <span
              style={{ cursor: "pointer", padding: "8px" }}
              className="badge bg-info ml-2"
              onClick={() => RenewAgentPopup(params.row.id)}
            >
              Renew
            </span>
          )}
        </div>
      ),
    },
  ];

  const columnsTeam = [
    {
      field: "online",
      headerName: "Active Status",
      width: 170,
      sortable: true,
      renderCell: (params) => {
        const isOnline = params.row.online === 1;
        const lastSeen = params.row.last_seen_datetime;

        return isOnline ? (
          <>
            <div
              style={{
                display: "inline-block",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                backgroundColor: "green",
                marginRight: "8px",
              }}
            />
            <span>Online</span>
          </>
        ) : (
          <span style={{ color: params.row.isExpiringSoon ? "red" : "black" }}>{dayjs(lastSeen).format("DD/MM/YYYY h:mm A")}</span>
        );
      },
    },
    {
      field: "name",
      headerName: "Name",
      width: 150,
      editable: true,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        if (currentUser.user_type === "manager" && selectedBtn === "agent") {
          return (
            <div className="d-flex align-items-center gap-1" style={{ color }}>
              <span>{params.row.name}</span>
              <Link to={`/agent-details/${params.row.id}`}>
                <FiExternalLink size={16} style={{ marginLeft: "5px" }} />
              </Link>
            </div>
          );
        }

        return <span style={{ color }}>{params.row.name}</span>;
      },
    },
    {
      field: "email",
      headerName: "Email",
      width: 190,
      editable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.email}</span>;
      },
    },
    {
      field: "mobile",
      headerName: "Mobile",
      width: 160,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{params.row.mobile}</span>;
      },
    },
    {
      field: "is_active",
      headerName: "Account Status",
      sortable: false,
      width: 160,
      renderCell: (params) => {
        const isActive = params.row.is_active === 1;

        return isActive ? (
          <span className="badge badge-success" style={{ background: "green" }}>
            Active
          </span>
        ) : (
          <span className="badge badge-danger" style={{ background: "red" }}>
            Inactive
          </span>
        );
      },
    },
    ...(currentUser.user_type === "manager"
      ? [
        {
          field: "break",
          headerName: "Break",
          description: "Toggle to start or end agent break.",
          sortable: false,
          width: 160,
          renderCell: (params) => (
            <BreakToggleCell agentId={params.row.id} />
          ),
        },
      ]
      : []),
    {
      field: "Expiry",
      headerName: "Expiry Date",
      width: 160,
      sortable: true,
      renderCell: (params) => {
        const color = params.row.isExpiringSoon ? "red" : "black";
        return <span style={{ color }}>{formatToLocalDate(params.row.exp_date)}</span>;
      },
    },
    {
      field: "",
      headerName: "Action",
      sortable: false,
      width: 160,
      renderCell: (params) => (
        <div>
          <span
            className="mx-2"
            data-bs-toggle="tooltip"
            data-bs-title="Update"
            onClick={() => toggleUpdate(params.row.id)}
            style={{
              cursor: "pointer",
              color: permission.update === 1 ? "blue" : "grey",
            }}
          >
            <i className="bx bx-edit"></i>
          </span>
          <span
            className="mx-2"
            data-bs-toggle="tooltip"
            data-bs-title="Delete"
            onClick={() => handleDeletePopup(params.row.id)}
            style={{
              cursor: "pointer",
              color: permission.delete === 1 ? "red" : "grey",
            }}
          >
            <i className="bx bx-trash"></i>
          </span>
          {params.row.auto_renew_status === 0 && (
            <span
              style={{ cursor: "pointer", padding: "8px" }}
              className="badge bg-info ml-2"
              onClick={() => RenewAgentPopup(params.row.id)}
            >
              Renew
            </span>
          )}
        </div>
      ),
    },
  ];


  const CustomToolbar = () => {
    return (
      <Box
        sx={{
          p: 1,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {/* <GridToolbar /> */}
        <GridToolbarContainer>
          <GridToolbarFilterButton />
          <GridToolbarExport />
        </GridToolbarContainer>

        {selectedRows.length > 0 && selectedBtn !== "team" && (
          <Button
            variant="contained"
            color="primary"
            onClick={() => setOpen(true)}
          >
            {selectedBtn === "agent" ? "Assign Manager/Team" : "Assign Team"}
          </Button>
        )}
        <GridToolbarQuickFilter debounceMs={500} />
      </Box>
    );
  };

  const handleClose = () => {
    setTransferTo("");
    setListToTransfer("");
    setSelectedAssign("");
    setSelectedRows("");
    setOpen(false);
  };
  const modelStyle = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: 400,
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
    p: 1,
  };
  const handleSelectAssign = (selected) => {
    setSelectedAssign(selected);
    const filterAgent = agentList
      .filter((list) => list.agent_type === selected.value)
      .map((list) => ({
        value: list.id,
        label: list.name,
      }));

    setListToTransfer(filterAgent);
  };
  const CustomBackdrop = (props) => (
    <Backdrop
      {...props}
      open={props.open}
      onClick={(e) => e.stopPropagation()}
    />
  );

  return (
    <>
      <Modal
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        open={open}
        onClose={handleClose}
        closeAfterTransition
        BackdropComponent={CustomBackdrop}
        BackdropProps={{
          timeout: 500,
        }}
      >
        <Fade in={open}>
          <Box sx={modelStyle}>
            <div className="d-flex flex-column">
              <div>
                <button
                  style={{
                    float: "right",
                    cursor: "pointer",
                    padding: "3px",
                    background: "transparent",
                    border: "none",
                    fontSize: "1.5rem",
                  }}
                  onClick={handleClose}
                >
                  <i className="bx bx-x"></i>
                </button>
              </div>
              <div>
                {selectedBtn === "agent" && (
                  <Select
                    placeholder="Transfer to"
                    onChange={handleSelectAssign}
                    options={[
                      { value: "manager", label: "Manager" },
                      { value: "team", label: "Team" },
                    ]}
                  />
                )}
                {selectedAssign && (
                  <>
                    <div className="my-4">
                      <Select
                        placeholder={`Select ${selectedAssign.value}`}
                        onChange={(selected) => setTransferTo(selected)}
                        options={listToTransfer}
                      />
                    </div>
                    <div style={{ float: "right" }}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleTransfer}
                      >
                        Transfer
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </Box>
        </Fade>
      </Modal>

      <div className="d-flex flex-column container-fluid" style={{ height: "90vh" }}>
        {/* <div className="my-4">
          <Link to="/agent-management/create-agent" className="btn btn-success" style={{float:"right"}}>Create</Link>
        </div> */}
        <div className="container-fluid h-75" style={{ marginTop: "2rem" }}>

          <div className={`d-flex ${currentUser.user_type === "manager" &&
            currentUser?.permission &&
            JSON.parse(currentUser.permission).add === 1
            ? "justify-content-end"
            : "justify-content-between"} align-items-center`}>

            {currentUser.user_type !== "manager" && (<div className=" d-flex align-items-center justify-content-center flex-wrap ">
              <button
                style={{ height: "30px", width: "5rem", marginBottom: "1rem" }}
                className={`mx-1 btn-border-none rounded-pill  ${selectedBtn === "agent" ? "active-btn" : ""
                  }`}
                onClick={AgentBtn}
              >
                Agent
              </button>
              <button
                style={{ height: "30px", width: "6rem", marginBottom: "1rem" }}
                className={`mx-1 btn-border-none rounded-pill ${selectedBtn === "manager" ? "active-btn" : ""
                  }`}
                onClick={ManagerBtn}
              >
                Manager
              </button>
              {currentUser.user_type !== "team" && (
                <button
                  style={{ height: "30px", width: "5rem", marginBottom: "1rem" }}
                  className={`mx-1 btn-border-none rounded-pill ${selectedBtn === "team" ? "active-btn" : ""
                    }`}
                  onClick={TeamBtn}
                >
                  Team
                </button>

              )}
            </div>)}

            {(currentUser.user_type === "admin" ||
              (currentUser.user_type === "manager" &&
                currentUser?.permission !== undefined &&
                JSON.parse(currentUser.permission).add === 1)) && (
                <div className="mb-3">
                  <button className="btn btn-primary">
                    <Link to="/agent-management/create-agent" style={{ color: "white" }}>
                      Add Agent
                    </Link>
                  </button>
                </div>
              )}

          </div>

          <div className="card">
            <Box sx={{ width: "100%" }}>
              <DataGrid
                rows={selectedAgentType}
                columns={
                  selectedBtn === "agent" && currentUser.user_type !== "manager"
                    ? columnsAgent
                    : selectedBtn === "manager"
                      ? columnsManager
                      : columnsTeam
                }
                loading={loading}
                initialState={{
                  pagination: {
                    paginationModel: {
                      pageSize: 5,
                    },
                  },
                }}
                disableRowSelectionOnClick
                pageSizeOptions={[5]}
                checkboxSelection={
                  selectedBtn === "team" || currentUser.user_type === "manager"
                    ? false
                    : selectedBtn === "manager" &&
                      currentUser.user_type === "team"
                      ? false
                      : true
                }
                rowSelectionModel={selectedRows}
                onRowSelectionModelChange={(newRowSelectionModel) => {
                  setSelectedRows(newRowSelectionModel);
                }}
                slots={{ toolbar: CustomToolbar }}
                slotProps={{
                  toolbar: {
                    showQuickFilter: true,
                  },
                }}
              />
            </Box>
          </div>
          {updateForm && (
            <div className="popup-agent">
              <div className="assign-popup-content-agent">
                <div style={{ float: "right", cursor: "pointer" }}>
                  <i
                    className="bx bx-x float-right" style={{ fontSize: "26px" }}
                    onClick={() => setUpdateForm(false)}
                  ></i>
                </div>
                <form>
                  <div className="form-group col-md-12">
                    <h5 className=" popupHeader">Edit {name} </h5>
                  </div>

                  <div className="overflowblocks">
                    <div className="row">
                      <div className="col-md-6">
                        <label htmlFor="agent_type" className="form-label">
                          User Type
                        </label>
                        <select
                          value={updateValue.agent_type}
                          name="agent_type"
                          onChange={handleChange}
                          className="form-control"
                          style={{ cursor: "pointer" }}
                          disabled={
                            currentUser.user_type === "manager" ? true : false
                          }
                        >
                          <option value="agent">Agent</option>
                          {currentUser.user_type === "admin" ||
                            currentUser.user_type === "team" ? (
                            <option value="manager">Manager</option>
                          ) : null}
                          {currentUser.user_type === "admin" && (
                            <option value="team">Team</option>
                          )}
                        </select>
                      </div>
                      <div className="col-md-6">
                        <label className="agent-label" htmlFor="name">
                          Name
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          name="name"
                          value={updateValue.name}
                          onChange={handleChange}
                          placeholder="Enter Name"
                        />
                      </div>
                    </div>
                    {updateValue.agent_type !== "agent" && (
                      <div className="row mt-2">
                        <div className="col-md-12">
                          <input
                            className="ml-2"
                            style={{ cursor: "pointer" }}
                            type="checkbox"
                            id="checkboxadd"
                            name="add"
                            checked={updateCheckBox.add}
                            onChange={handleCheckboxChange}
                          />
                          <label
                            className="mx-2"
                            htmlFor="checkboxadd"
                            style={{ cursor: "pointer" }}
                          >
                            Add
                          </label>

                          <input
                            className="ml-2"
                            type="checkbox"
                            style={{ cursor: "pointer" }}
                            id="checkboxupdate"
                            name="update"
                            checked={updateCheckBox.update}
                            onChange={handleCheckboxChange}
                          />
                          <label
                            className="mx-2"
                            htmlFor="checkboxupdate"
                            style={{ cursor: "pointer" }}
                          >
                            Update
                          </label>

                          <input
                            className="ml-2"
                            style={{ cursor: "pointer" }}
                            type="checkbox"
                            id="checkboxdel"
                            name="delete"
                            checked={updateCheckBox.delete}
                            onChange={handleCheckboxChange}
                          />
                          <label
                            className="mx-2"
                            htmlFor="checkboxdel"
                            style={{ cursor: "pointer" }}
                          >
                            Delete
                          </label>
                        </div>
                      </div>
                    )}
                    <div className="row mt-2" style={{ marginTop: "-10px" }}>
                      <div className="col-md-6">
                        <label className="agent-label" htmlFor="email">
                          User Name
                        </label>
                        <input
                          type="email"
                          className="form-control"
                          name="email"
                          value={updateValue.email}
                          onChange={handleChange}
                          placeholder="Enter Email"
                          disabled
                        />
                        <p style={{ fontSize: "10px" }}>
                          Note: Email cannot be changed
                        </p>
                      </div>
                      <div className="col-md-6">
                        <label className="agent-label" htmlFor="mobile">
                          Mobile
                        </label>
                        <input
                          type="number"
                          className="form-control"
                          name="mobile"
                          value={updateValue.mobile}
                          onChange={handleChange}
                          placeholder="Enter Mobile"
                          disabled
                        />
                        <p style={{ fontSize: "10px" }}>
                          Note: Mobile cannot be changed
                        </p>
                      </div>
                      {/* <div className="form-group col-md-6">
                            <label className='agent-label' for="name">Status</label>
                            <select name='is_active' value={updateValue.is_active} onChange={handleChange} className="form-control">
                              <option value=''>Select Status</option>
                              <option value='1'>Active</option>
                              <option value='0'>InActive</option>
                            </select>

                          </div> */}
                    </div>

                    <div className="row mt-2 align-items-center" style={{ marginTop: "-10px" }}>
                      {/* Left side: Toggle */}
                      <div className="col-md-6 d-flex align-items-center">
                        <p className="mt-2"><strong>Agent Renew</strong></p>
                        <label className="switch mb-0">
                          <input
                            type="checkbox"
                            checked={updateValue.auto_renew_status === 1 ? true : false}
                            onChange={buttonHandler}
                          />
                          <span
                            className="slider round"
                            style={{ backgroundColor: updateValue.auto_renew_status === 1 ? "green" : "red" }}
                          ></span>
                        </label>
                      </div>

                      {/* Right side: Balance and Agent Charge */}
                      <div className="col-md-6 d-flex justify-content-end align-items-center">
                        {/* <span className="me-3"><b>Balance:</b> {Math.ceil(userBalance)}</span> */}
                        <span><b>Agent Charge:</b> {agentCharge}</span>
                      </div>
                    </div>

                    {/* <div className="form-row">
                        <div className="form-group col-md-6">
                          <label className='agent-label' for="password">Password</label>
                          <input type="password" className="form-control" name='password' value={updateValue.password} onChange={handleChange} placeholder="Enter Password" />
                        </div>
                        <div className="form-group col-md-6">
                          <label className='agent-label' for="cpassword">Confirm Password</label>
                          <input type="password" className="form-control" name='cpassword' value={updateValue.cpassword} onChange={handleChange} placeholder="Enter Confirm Password" />
                        </div>
                      </div> */}
                  </div>
                  <button
                    style={{ float: "right" }}
                    className="btn btn-primary mt-4"
                    onClick={(e) => updateData(e)}
                  >
                    Update
                  </button>
                </form>
              </div>
            </div>
          )}
          {deletePopup && (
            <div className="popup-agent">
              <div className="assign-popup-content-agent">
                <div style={{ float: "right", cursor: "pointer" }}>
                  <i
                    className="bx bx-x float-right" style={{ fontSize: "26px" }}
                    onClick={() => setDeletePopup(false)}
                  ></i>
                </div>
                <form>
                  <div className="overflowblocks">
                    <div className="row mt-4">
                      <div className="col-md-12">
                        <h4>Are You Sure Want to Delete ?</h4>
                      </div>
                    </div>
                  </div>

                  <button
                    style={{ float: "right", marginLeft: "10px" }}
                    className="btn btn-danger mt-4"
                    onClick={() => DeleteAgent()}
                  >
                    Delete
                  </button>

                  <button
                    style={{ float: "right" }}
                    className="btn btn-secondary mt-4"
                    onClick={() => setDeletePopup(false)}
                  >
                    Cancel
                  </button>
                </form>
              </div>
            </div>
          )}
          {renewPopup && (
            <div className="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-50" style={{ zIndex: 1050 }}>
              <div className="bg-white p-4 rounded shadow" style={{ width: '90%', maxWidth: '700px' }}>
                <h5 className="mb-4 text-center fw-bold text-primary">Agent Renew</h5>

                <div className="mb-4">
                  <div className="mb-3 d-flex justify-content-between align-items-center">
                    <span className="text-muted" style={{ fontSize: '18px' }}>
                      Balance to be deducted from your account: <strong>{agentCharge} / month</strong>
                    </span>
                  </div>

                  <div className="text-muted mb-3" style={{ fontSize: '16px' }}>
                    Enable <strong className="text-dark">Agent Renew</strong> to ensure uninterrupted service and avoid manual renewals —
                    <span className="text-success"> stay hassle-free!</span>
                  </div>

                </div>

                <div className="d-flex justify-content-end">
                  <button className="btn btn-outline-secondary me-2" onClick={() => setRenewPopup(false)}>
                    Close
                  </button>
                  <button className="btn btn-success" onClick={RenewAgent}>
                    Renew
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      {isBreakModalOpen && <AgentBreakModal
        onClose={() => handleBreakModalClose(false, selectedAgentForBreak)}
        onConfirm={() => handleBreakModalClose(true, selectedAgentForBreak)}
        currentUser={currentUser}
        agentId={selectedAgentForBreak} />}
    </>
  );
};

export default AgentManagement;
