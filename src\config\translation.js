// authkey-chat-new/src/config/translation.js
export const TRANSLATION_CONFIG = {
  // Use their existing API endpoint
  BASE_URL: 'https://napi.authkey.io/api',
  
  // Single endpoint for both operations
  ENDPOINTS: {
    GOOGLE: '/google'
  },
  
  // Translation settings
  SETTINGS: {
    DEBOUNCE_DELAY: 1500,
    AUTO_TRANSLATE: true,
    INSTANT_TRANSLATE_ON_SPACE: true
  }
};

// Helper function to get full URL
export const getTranslationUrl = (endpoint) => {
  return `${TRANSLATION_CONFIG.BASE_URL}${endpoint}`;
};