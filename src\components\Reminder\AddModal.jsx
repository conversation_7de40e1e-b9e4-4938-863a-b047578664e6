
import React, { useState } from "react";
import styles from './addModal.module.css'
const AddModal = ({ show, onClose, children }) => {
    if (!show) return null; // If `show` is false, return null to hide the modal

    return (
        <div className={styles.modalBackdrop}>
            <div className={styles.modalContainer}>
                <div className={styles.modalHeader}>
                    <button className={styles.closeButton} onClick={onClose}>X</button>
                </div>
                <div className={styles.modalBody}>
                    {children}
                </div>
                <div className={styles.modalFooter}>
                    <button className={styles.cancelButton} onClick={onClose}>Cancel</button>
                    <button className={styles.saveButton}>Save</button>
                </div>
            </div>
        </div>
    );
};

export default AddModal