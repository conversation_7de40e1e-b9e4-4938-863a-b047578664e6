import React from 'react';
import styles from './LabelSelector.module.css';

const LabelSelector = ({ labels, selected, onChange }) => {

  const handleCheckboxChange = (label) => {
    const isSelected = selected.some(l => l.id === label.id);
  
    if (isSelected) {
      const updatedSelected = selected.filter(l => l.id !== label.id);
      onChange(updatedSelected);
      // Update localStorage with new selected labels
      localStorage.setItem('visibleLabels', JSON.stringify(updatedSelected));
    } else {
      const updatedSelected = [...selected, label];
      onChange(updatedSelected);
      // Update localStorage with new selected labels
      localStorage.setItem('visibleLabels', JSON.stringify(updatedSelected));
    }
  };
  


  return (
    <div className={`dropdown ${styles.labelSelector}`}>
      <button className="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
        Filter Labels
      </button>
      <ul className="dropdown-menu p-2 pb-0 pe-0" style={{ minWidth: '200px' }}>
        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
          {labels.map(label => (
            <li key={label.id}>
              <div className={`form-check form-switch mb-2 ${styles.checkboxContainer}`}>
                <input
                  className="form-check-input"
                  type="checkbox"
                  role="switch"
                  checked={selected.some(l => l.id === label.id)}
                  onChange={() => handleCheckboxChange(label)}
                  id={`label-${label.id}`}
                />

                <label className="form-check-label ms-2" htmlFor={`label-${label.id}`}>
                  {label.name}
                </label>
              </div>
            </li>
          ))}
        </div>
      </ul>
    </div>
  );
};

export default LabelSelector;