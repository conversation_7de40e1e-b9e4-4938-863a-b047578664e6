
import Home from "./pages/Home";
import Login from "./pages/Login";
import AgentManagementPage from "./pages/AgentManagementPage";
import { BrowserRouter, Routes, Route, Navigate, useLocation } from "react-router-dom";
import { useContext, useEffect } from "react";
import { AuthContext } from "./context/AuthContext";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import VerifyAccount from "./components/VerifyAccount";
import CreateAgentPage from "./pages/CreateAgentPage";
import AgentReportPage from "./pages/AgentReportPage";
import Setting from "./pages/Setting";
import Customers from "./pages/Customers/Customers";
import BroadcastReport from "./components/BroadcastReport/BroadcastReport";
import Report from "./pages/Report/Report";
import AgentReport from "./components/AgentReport/AgentReport";
import { setCookie, getCookie } from "./utils/Utils";
import AgentDetailPage from "./pages/AgentDetails/AgentDetailPage";
import AgentSettings from "./pages/AgentSettings/AgentSettings";
import CustomerReport from "./components/CustomerReport/CustomerReport";
import CustomerView from "./pages/Customers/CustomerView";
import LeadsDashboard from "./pages/LeadsDashboard/LeadsDashboard";
import AgentBreakStatusCard from "./components/AgentBreakModal/AgentBreakStatusCard";
import CreateNewPipeline from "./pages/LeadsDashboard/CreateNewPipeline";
function App() {
  const { currentUser } = useContext(AuthContext);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const userData = params.get("user");

    if (userData) {
      try {
        const parsedUser = JSON.parse(decodeURIComponent(userData));

        setCookie("user", JSON.stringify(parsedUser), 1);
        window.location.replace("/dashboard");

      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }
  }, []);
  useEffect(() => {
    // Remove query parameters on app refresh
    window.history.replaceState(null, '', window.location.pathname);
  }, []);

  //Sync sentences with the DB every hour
  // useEffect(() => {
  //   const syncToDatabase = async () => {

  //     if (!currentUser?.parent_id) return;
  //     // Fetch sentences from the database
  //     const fetchBody = {
  //       user_id: currentUser.parent_id,
  //       token: currentUser.parent_token,
  //       method: "retrieve",
  //     }
  //     axios.post(`${BASE_URL2}/suggestion`, fetchBody)
  //       .then((response) => {
  //         const sentencesFromDB = response?.data?.data[0]?.suggestion || []; // Handle missing data
  //         // Fetch sentences from localStorage
  //         const savedSentences = JSON.parse(localStorage.getItem('sentences')) || [];
  //         // Merge sentences and filter out duplicates
  //         const allSentences = [...new Set([...sentencesFromDB, ...savedSentences])];
  //         // post all sentence back to db
  //         if (allSentences.length > 0) {
  //           const body = {
  //             user_id: currentUser.parent_id,
  //             token: currentUser.parent_token,
  //             method: "insert",
  //             suggestion: allSentences
  //           };
  //           axios.post(`${BASE_URL2}/suggestion`, body)
  //             .then(() => {
  //               // console.log("Sentences synced successfully with the database.");
  //             })
  //             .catch((error) => {
  //               console.error("Error inserting sentences into the database:", error);
  //             });

  //         }
  //         // Save the merged sentences back to localStorage
  //         localStorage.setItem('sentences', JSON.stringify(allSentences));

  //       })
  //       .catch((error) => {
  //         console.error('Error syncing sentences with the database:', error);
  //       })
  //   };

  //   // Initial sync when the component mounts
  //   syncToDatabase();

  //   // Set up an interval for hourly sync
  //   const interval = setInterval(syncToDatabase, 3600000); // 1 hour

  //   // Cleanup interval on unmount
  //   return () => clearInterval(interval);
  // }, [currentUser]);

  useEffect(() => {
    localStorage.removeItem('sentences')
  }, [])


  const domainName = window.location.hostname;
  const ProtectedRoute = ({ children }) => {
    const location = useLocation();
    const userCookie = getCookie("user");
    const userInfo = userCookie ? JSON.parse(userCookie) : null;
       
    if (!userInfo) {
      return <Navigate to="/" state={{ from: location }} replace />;
    }
    return children;
  };

  const AdminRoute = ({ children }) => {
    const location = useLocation();
    const userCookie = getCookie("user");
    const userInfo = userCookie ? JSON.parse(userCookie) : null;
    if (!userInfo || userInfo.data?.user_type === "agent") {
      return <Navigate to="/" state={{ from: location }} replace />;
    }
    return children;
  };

  useEffect(() => {
    document.title = domainName;
  }, [domainName])

  return (
    <BrowserRouter>
      <Routes>
        <Route
          path="/home"
          element={
            <ProtectedRoute>
              <Home />
            </ProtectedRoute>
          }
        />
        <Route
          path="/agent-management/agent"
          element={
            <AdminRoute>
              <AgentManagementPage />
            </AdminRoute>
          }
        />
        <Route
          path="/agent-management/agent/setting"
          element={
            <AdminRoute>
              <AgentSettings />
            </AdminRoute>
          }
        />
        <Route
          path="/agent-management/create-agent"
          element={
            <AdminRoute>
              <CreateAgentPage />
            </AdminRoute>
          }
        />

        <Route
          path="/agent-management/setting"
          element={
            <AdminRoute>
              <Setting />
            </AdminRoute>
          }
        />
        <Route
          path="/broadcast"
          element={
            <ProtectedRoute>
              <Customers />
            </ProtectedRoute>
          }
        />
        <Route
          path="/agent-details/:agentId"
          element={
            <ProtectedRoute>
              <AgentDetailPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/dashboard"
          element={
            <AdminRoute>
              <AgentReportPage />
            </AdminRoute>
          }
        />

        <Route
          path="/report"
          element={
            <ProtectedRoute>
              <Report />
            </ProtectedRoute>
          }
        >
          <Route
            path="broadcast-report"
            element={
              <ProtectedRoute>
                <BroadcastReport />
              </ProtectedRoute>
            }
          />
          <Route
            path="agent-report"
            element={
              <ProtectedRoute>
                <AgentReport />
              </ProtectedRoute>
            }
          />
          <Route
            path="customer-report"
            element={
              <ProtectedRoute>
                <CustomerReport />
              </ProtectedRoute>
            }
          />


        </Route>
        <Route
          path="/customer-details/:encodedMobile"
          element={
            <ProtectedRoute>
              <CustomerView />
            </ProtectedRoute>
          }
        />
        <Route
          path="/leads-dashboard"
          element={
            <ProtectedRoute>
              <LeadsDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/create-pipeline"
          element={
            <ProtectedRoute>
              <CreateNewPipeline />
            </ProtectedRoute>
          }
        />

        <Route path="/" element={<Login />} />
        <Route path="/verify-account" element={<VerifyAccount />} />
      </Routes>

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
      <AgentBreakStatusCard currentUser={currentUser} />
    </BrowserRouter>
  );
}

export default App;
