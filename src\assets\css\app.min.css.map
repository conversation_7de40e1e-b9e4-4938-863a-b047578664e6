{"version": 3, "sources": ["custom/fonts/_fonts.scss", "_theme-light.scss", "_theme-dark.scss", "custom/components/_waves.scss", "app.css", "custom/components/_avatar.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_accordion.scss", "custom/components/_modals.scss", "custom/components/_forms.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_emoji-picker.scss", "custom/structure/_general.scss", "custom/structure/_sidemenu.scss", "custom/pages/_chat-leftsidebar.scss", "custom/pages/_user-chat.scss", "custom/pages/_user-profile-details.scss", "custom/pages/_authentication.scss"], "names": [], "mappings": "AAIQ,gGAMR,WACI,YAAA,0BACA,IAAA,oCACA,IAAA,2BAAA,CAAA,qCAAA,eACA,YAAA,IAGJ,WACI,YAAA,0BACA,IAAA,sCACA,IAAA,6BAAA,CAAA,uCAAA,eACA,YAAA,IAEJ,WACI,YAAA,0BACA,IAAA,qCACA,IAAA,4BAAA,CAAA,sCAAA,eACA,YAAA,IAEJ,WACI,YAAA,0BACA,IAAA,uCACA,IAAA,8BAAA,CAAA,wCAAA,eACA,YAAA,IAEJ,WACI,YAAA,0BACA,IAAA,mCACA,IAAA,0BAAA,CAAA,oCAAA,eACA,YAAA,ICrCJ,MAIE,gBAAA,QACA,6BAAA,QACA,oCAAA,QACA,iCAAA,QACA,oBAAA,QAIC,eAAA,sBAGD,kBAAA,KACA,mBAAA,MAGA,qBAAA,QACA,qBAAA,QAKA,WAAA,QACA,eAAA,GAAA,CAAA,GAAA,CAAA,IACA,UAAA,QACA,cAAA,EAAA,CAAA,EAAA,CAAA,GACA,gBAAA,QAGA,gBAAA,QACA,sBAAA,QAGA,kBAAA,QAGA,iBAAA,KACA,2BAAA,QACA,yBAAA,QACA,+BAAA,QACA,4BAAA,QACA,2BAAA,IAGA,aAAA,KACA,iBAAA,KACA,uBAAA,QACA,oBAAA,MACA,qBAAA,KACA,qBAAA,EAAA,IAAA,IAAA,QAGA,cAAA,KACA,sBAAA,KACA,gCAAA,QAIA,gCAAA,QACA,6BAAA,QAGA,mCAAA,QACA,kBAAA,KACA,yBAAA,QACA,gCAAA,qBAGE,iBAAA,QAGA,mBAAA,QAGA,wBAAA,QACA,aAAA,QACA,gBAAA,QACA,oBAAA,EAAA,CAAA,EAAA,CAAA,GAGF,iBAAA,QAGA,4BAAA,0BACA,wBAAA,mBACA,+BAAA,oBAGA,yBAAA,QAGA,gBAAA,KAGA,yBAAA,QAGA,cAAA,KACA,oCAAA,QACA,kBAAA,QACA,wBAAA,QACA,wBAAA,QACA,uBAAA,QAGA,6BAAA,QACA,0BAAA,QAMA,wBAAA,uBClHF,wBA2BM,cAAA,QAAA,cAAA,QAAA,cAAA,QAAA,cAAA,QAAA,cAAA,QAAA,cAAA,QAAA,cAAA,QAAA,cAAA,QAAA,cAAA,QAKJ,gBAAA,QACA,6BAAA,QACA,oCAAA,QACA,iCAAA,QACA,oBAAA,QAGA,eAAA,kBAGA,kBAAA,MACA,mBAAA,KAGE,qBAAA,qBACA,qBAAA,QAGF,qBAAA,QAIA,eAAA,QACA,uBAAA,QACA,8BAAA,KAGA,gCAAA,KACA,uBAAA,KAGA,qCAAA,gCACA,4BAAA,QACA,oCAAA,wCACA,2CAAA,KACA,wCAAA,0BAGA,mBAAA,QAGA,mBAAA,QAIA,WAAA,QACA,eAAA,EAAA,CAAA,EAAA,CAAA,GACA,UAAA,QACA,cAAA,GAAA,CAAA,GAAA,CAAA,IACA,gBAAA,QAGA,gBAAA,QACA,sBAAA,QAGA,kBAAA,QAGA,iBAAA,QACA,2BAAA,QACA,yBAAA,QACA,+BAAA,QACA,4BAAA,QACA,2BAAA,IAGA,aAAA,QACA,iBAAA,QACA,uBAAA,QACA,oBAAA,KACA,qBAAA,MACA,qBAAA,EAAA,IAAA,IAAA,QAGA,cAAA,QACA,sBAAA,QACA,gCAAA,QAGA,gCAAA,QACA,6BAAA,QAGA,mCAAA,KACA,kBAAA,YACA,yBAAA,QACA,gCAAA,QAGA,iBAAA,QAGA,mBAAA,QAGE,wBAAA,QACA,aAAA,QACA,gBAAA,QACA,oBAAA,EAAA,CAAA,EAAA,CAAA,GAGF,iBAAA,QAGA,4BAAA,uBACA,wBAAA,yBACA,+BAAA,0BAGA,yBAAA,QAGA,gBAAA,QAGA,yBAAA,QAGA,cAAA,QACA,oCAAA,QACA,kBAAA,QACA,wBAAA,QACA,wBAAA,QACA,uBAAA,QAGA,6BAAA,QACA,0BAAA,QAGA,wBAAA,QCrKF;;;;;;AAOC,cACG,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,cCuLF,cDrLI,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,cCuLF,oBAFA,oBACA,sBDlLI,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MAIA,wCACI,iBAAA,qBAKJ,0CACI,iBAAA,oBAIJ,0CACI,iBAAA,mBAIJ,uCACI,iBAAA,oBAIJ,0CACI,iBAAA,qBAIJ,yCACI,iBAAA,oBEjKR,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,KACA,MAAA,KAGF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,8BACA,MAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,YAAA,IACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KAIF,cACE,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAAA,UAAA,KACA,aAAA,IACA,iCACE,YAAA,KACA,OAAA,IAAA,MAAA,kBACA,cAAA,IACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,uCACE,SAAA,SACA,kBAAA,iBAAA,UAAA,iBChDN,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAKJ,WACI,YAAA,IAGJ,aACI,YAAA,IAKJ,kBACI,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QACA,WAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,wBACI,MAAA,QACA,iBAAA,QAIR,MACI,UAAA,KAEJ,MACI,UAAA,KAEJ,MACI,UAAA,MAEJ,MACI,UAAA,MAGJ,MACI,UAAA,MAGJ,YACI,SAAA,SACA,OAAA,KACA,MAAA,KACA,MAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,iBAAA,kBAGJ,gBACI,aAAA,wCAIJ,aACI,aAAA,yBAEJ,cACI,aAAA,0BAIA,mCACI,MAAA,6BAEJ,mCACI,eAAA,UAAA,gBAAA,iBAAA,OAAA,UAAA,gBAAA,iBCzHR,aACE,OAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,IAAA,IAEA,qCACI,kBAAA,YAAA,MAAA,GAAA,SAAA,UAAA,YAAA,MAAA,GAAA,SAEJ,qCACI,kBAAA,YAAA,MAAA,KAAA,SAAA,UAAA,YAAA,MAAA,KAAA,SAEJ,qCACI,kBAAA,YAAA,MAAA,IAAA,SAAA,UAAA,YAAA,MAAA,IAAA,SAEJ,qCACI,kBAAA,YAAA,MAAA,KAAA,SAAA,UAAA,YAAA,MAAA,KAAA,SAEJ,qCACI,kBAAA,YAAA,MAAA,GAAA,SAAA,UAAA,YAAA,MAAA,GAAA,SAIN,MACI,QAAA,aACA,MAAA,IACA,OAAA,KACA,cAAA,KACA,iBAAA,8BAGJ,+BACI,GACE,OAAA,KAEF,IACE,OAAA,KAEF,KACE,OAAA,MARN,uBACI,GACE,OAAA,KAEF,IACE,OAAA,KAEF,KACE,OAAA,MCpCE,8BACI,WAAA,MAKJ,sCACI,UAAA,KAII,uDACI,QAAA,SAQhB,wBACI,YAAA,KACA,aAAA,EAQA,gDACI,iBAAA,gRCjCR,oCACI,iBAAA,8BACA,OAAA,EAAA,KAAA,KAAA,KCEP,wCAAA,yCAAA,sCAAA,sCACE,WAAA,KADF,+BAAA,gCAAA,6BAAA,6BACE,WAAA,KADF,mCAAA,oCAAA,iCAAA,iCACE,WAAA,KADF,oCAAA,qCAAA,kCAAA,kCACE,WAAA,KNynBH,0BACA,2BM3nBC,wBNynBD,wBMxnBG,WAAA,KAKH,YN2nBA,kBACA,kBMznBE,OAAA,QACA,cAAA,EAGF,0BACE,iBAAA,8BACA,aAAA,8BCxBF,iBACE,SAAA,SACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,cAAA,KAAA,UAAA,KACA,iBAAA,MAAA,cAAA,MAAA,gBAAA,WACA,mBAAA,MAAA,cAAA,WACA,kBAAA,MAAA,eAAA,MAAA,YAAA,WAGF,mBACE,SAAA,OACA,MAAA,QACA,OAAA,QACA,UAAA,QACA,WAAA,QAGF,gBACE,UAAA,QACA,SAAA,SACA,SAAA,OACA,QAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,eACA,OAAA,eACA,QAAA,EAGF,kBACE,UAAA,kBACA,mBAAA,kBAAA,WAAA,kBACA,OAAA,eACA,SAAA,SACA,IAAA,EACA,KAAA,YACA,OAAA,EACA,MAAA,YACA,QAAA,EACA,OAAA,EACA,2BAAA,MAGF,2BACE,UAAA,QACA,mBAAA,qBAAA,WAAA,qBACA,SAAA,SACA,QAAA,MACA,OAAA,KACA,MAAA,KACA,WAAA,QACA,SAAA,KACA,UAAA,KACA,WAAA,KACA,gBAAA,KACA,QAAA,YAGF,8CPqqBA,6COnqBE,QAAA,KPwqBF,yBOrqBA,0BAEE,QAAA,IACA,QAAA,MAGF,uBACE,WAAA,KACA,UAAA,KACA,MAAA,KACA,eAAA,KAGF,wCACE,mBAAA,kBAAA,WAAA,kBACA,OAAA,KACA,MAAA,KACA,UAAA,IACA,SAAA,SACA,MAAA,KACA,WAAA,IACA,SAAA,OACA,QAAA,GACA,QAAA,EACA,OAAA,EACA,eAAA,KACA,iBAAA,QAAA,kBAAA,QAAA,UAAA,QACA,kBAAA,EAAA,YAAA,EACA,wBAAA,EAAA,WAAA,EAGF,gCACE,mBAAA,QAAA,WAAA,QACA,QAAA,MACA,QAAA,EACA,SAAA,SACA,IAAA,EACA,KAAA,EACA,OAAA,MACA,MAAA,MACA,WAAA,IACA,UAAA,IACA,SAAA,OACA,eAAA,KACA,QAAA,GAGF,iBACE,QAAA,EACA,SAAA,SACA,MAAA,EACA,OAAA,EACA,eAAA,KACA,SAAA,OAGF,uDACE,eAAA,KACA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,oBAAA,KAGF,qDACE,eAAA,IAGF,qBACE,SAAA,SACA,MAAA,IACA,MAAA,IACA,WAAA,KAGF,4BACE,SAAA,SACA,QAAA,GACA,WAAA,QACA,cAAA,IACA,KAAA,EACA,MAAA,EACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAGF,8CAEE,QAAA,GACA,mBAAA,QAAA,GAAA,OAAA,WAAA,QAAA,GAAA,OAGF,oCACE,IAAA,EACA,MAAA,KAGF,gEACE,IAAA,IACA,OAAA,IAGF,sCACE,KAAA,EACA,OAAA,KAGF,kEACE,OAAA,KACA,KAAA,IACA,MAAA,IAGF,2DACE,MAAA,KACA,KAAA,EACA,IAAA,IACA,OAAA,IACA,WAAA,EACA,UAAA,KACA,MAAA,KAIF,mEACE,MAAA,KACA,KAAA,EAGF,yBACE,UAAA,IACA,SAAA,MACA,QAAA,EACA,WAAA,OACA,OAAA,MACA,MAAA,MACA,WAAA,OACA,WAAA,OAGF,0BACE,SAAA,MACA,KAAA,EACA,WAAA,OACA,WAAA,OACA,gBAAA,KAGF,eACE,OAAA,KCjNF,iBACI,iBAAA,4BACA,MAAA,gBACA,mBAAA,EAAA,IAAA,IAAA,6BAAA,WAAA,EAAA,IAAA,IAAA,6BACA,IAAA,eACA,OAAA,KACA,mBACI,YAAA,oCACA,MAAA,kBAEJ,4BAVJ,iBAWQ,KAAA,eACA,IAAA,eACA,OAAA,MAGJ,kDACI,MAAA,yBAGJ,yCACI,OAAA,eACA,+CACI,iBAAA,6BACA,MAAA,+BACA,OAAA,IAAA,MAAA,uBACA,QAAA,MAAA,eACA,UAAA,mBACA,0EACI,MAAA,4CADJ,iEACI,MAAA,4CADJ,qEACI,MAAA,4CADJ,sEACI,MAAA,4CADJ,4DACI,MAAA,4CAIR,6CACI,KAAA,+BACA,MAAA,KACA,IAAA,KAIR,6CACI,iBAAA,0BACA,uDACI,iBAAA,+BAGA,qDACI,iBAAA,+BAOhB,+BACI,iBAAA,yCAGJ,+BACI,iBAAA,0BAIJ,gCACI,KAAA,+BAIJ,yBACI,iBAAA,4BCxEJ,OACI,YAAA,KCEJ,WACI,UAAA,KACA,UAAA,KACA,OAAA,MACA,WAAA,MACA,iBAAA,qBACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,QAAA,EACA,aAAA,IAAA,MAAA,qBACA,QAAA,EAAA,IAEA,4BAXJ,WAYQ,SAAA,MACA,OAAA,EACA,OAAA,KACA,UAAA,KACA,WAAA,KACA,WAAA,IAAA,MAAA,sBAGJ,6BACI,WAAA,OAEA,iCACI,KAAA,8BAGJ,4BAPJ,6BAQQ,QAAA,MAGJ,mCACI,YAAA,KAGJ,wCACI,QAAA,MAGJ,yCACI,QAAA,KAKZ,qBACI,OAAA,KACA,4BAFJ,qBAGQ,MAAA,KAEA,8BACI,QAAA,MAKZ,eACI,OAAA,KACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,4BAHJ,eAIQ,iBAAA,kBAAA,cAAA,kBAAA,gBAAA,wBACA,mBAAA,WAAA,sBAAA,OAAA,mBAAA,IAAA,eAAA,KAEJ,yBACI,OAAA,IAAA,EACA,QAAA,MACA,MAAA,KAEA,4BALJ,yBAMQ,wBAAA,EAAA,WAAA,EACA,iBAAA,EAAA,kBAAA,EAAA,UAAA,EACA,OAAA,IAAA,GAEJ,mCACI,WAAA,OACA,UAAA,KACA,MAAA,kCACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,cAAA,EACA,QAAA,EACA,SAAA,SAEA,qCACI,eAAA,OAGJ,2CACI,SAAA,SACA,QAAA,GACA,OAAA,KACA,MAAA,IACA,MAAA,KACA,IAAA,KACA,4BAPJ,2CAQQ,MAAA,KACA,OAAA,IACA,MAAA,KACA,IAAA,KACA,KAAA,IACA,kBAAA,iBAAA,UAAA,kBAIR,4BAhCJ,mCAiCQ,UAAA,KACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,OAAA,EAAA,MAGJ,0CACI,iBAAA,YACA,MAAA,8BAEA,kDACI,iBAAA,8BAKZ,wCACI,MAAA,8BAIR,6BACI,OAAA,KACA,MAAA,KACA,iBAAA,mBACA,QAAA,IAIR,YACI,QAAA,KAIA,wCACI,QAAA,aAGJ,uCACI,QAAA,KAUgB,0FACI,QAAA,QAQxB,wCACI,QAAA,aAGJ,uCACI,QAAA,KCzKR,kBACI,OAAA,mBACA,SAAA,SACA,iBAAA,yBACA,mBAAA,EAAA,IAAA,IAAA,mBAAA,WAAA,EAAA,IAAA,IAAA,mBAEA,yBANJ,kBAOQ,UAAA,MACA,UAAA,MACA,OAAA,OAGJ,mCACI,iBAAA,QACA,QAAA,IACA,cAAA,IACA,WAAA,OACA,WAAA,KACA,QAAA,MAEA,kDACI,SAAA,SACA,IAAA,EACA,KAAA,EACA,MAAA,EAMR,+BACI,OAAA,EAGJ,kCACI,UAAA,KACA,SAAA,SACA,MAAA,KACA,IAAA,EAIR,gBACI,WAAA,oBAEA,4BAHJ,gBAIQ,OAAA,qBAIR,iBACI,OAAA,oBACA,4BAFJ,iBAGQ,OAAA,qBAKR,WACI,OAAA,EAIQ,uBACI,iBAAA,8BACA,MAAA,KAEA,yCACI,MAAA,eACA,kDACI,iBAAA,gCAIR,8BACI,iBAAA,gCACA,MAAA,eAGJ,mCACI,iBAAA,gCAKZ,gBACI,QAAA,MACA,QAAA,IAAA,KACA,MAAA,mBACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,UAAA,KAGJ,iCACI,UAAA,KAGJ,+BACI,YAAA,IAIA,wCACI,MAAA,8BACA,YAAA,IAEA,6CACI,iBAAA,8BAKZ,8BACI,SAAA,SACA,QAAA,aACA,MAAA,KACA,KAAA,KACA,IAAA,KAEA,qCACI,YAAA,KACA,YAAA,IACA,UAAA,KAMhB,eACI,SAAA,SAEA,4BACI,MAAA,KACA,OAAA,KACA,iBAAA,QACA,cAAA,IACA,OAAA,IAAA,MAAA,kBACA,SAAA,SACA,MAAA,EACA,KAAA,KACA,OAAA,EAIA,mCACI,iBAAA,QAKJ,iCACI,iBAAA,QAQR,iBACI,OAAA,QACA,QAAA,IAAA,KAIR,oBACI,QAAA,IAAA,KACA,MAAA,8BACA,YAAA,IACA,SAAA,SACA,UAAA,KACA,0BACI,QAAA,GACA,OAAA,IACA,SAAA,SACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBACA,KAAA,KACA,MAAA,EACA,iBAAA,uBAMR,oBAAA,gBACI,WAAA,mBAEA,4BAHJ,oBAAA,gBAIQ,OAAA,qBAEJ,uBAAA,mBACI,SAAA,SACA,QAAA,KAAA,KACA,cAAA,IAAA,MAAA,uBAEA,kCAAA,8BACI,cAAA,EASZ,cACI,SAAA,SACA,QAAA,aAEA,kCACI,SAAA,SACA,MAAA,EACA,KAAA,KACA,OAAA,EACA,OAAA,QAGJ,gCACI,cAAA,MAAA,WAAA,MAGJ,sCACI,QAAA,KAMA,iGACI,MAAA,KAGR,4BACI,QAAA,EACA,8CACI,QAAA,KAGJ,8CACI,SAAA,SAIA,gFACI,QAAA,QACA,YAAA,mBACA,SAAA,SACA,IAAA,IACA,KAAA,IACA,kBAAA,iBAAA,iBAAA,UAAA,iBAAA,iBACA,MAAA,8BACA,UAAA,KACA,QAAA,EAIA,iGACI,MAAA,8BAOZ,8CACI,iBAAA,sBAMZ,WACI,OAAA,QACA,OAAA,IAAA,MAAA,mBChRJ,WACI,WAAA,yCACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,SAAA,SACA,iBAAA,kBAEA,8BACI,QAAA,GACA,SAAA,SACA,KAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,iBAAA,YACA,QAAA,GAGJ,4BAjBJ,WAkBQ,SAAA,MACA,KAAA,EACA,IAAA,EACA,MAAA,KACA,OAAA,KACA,WAAA,OACA,kBAAA,iBAAA,UAAA,iBACA,QAAA,GAEA,0BACI,WAAA,QACA,kBAAA,cAAA,UAAA,eAIR,yBACI,SAAA,SAIA,gDACI,QAAA,KAGA,yBADJ,wCAEQ,QAAA,sBAAA,QAAA,sBAAA,QAAA,gBAMhB,sBACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,MAAA,KACA,OAAA,MACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,SAAA,SACA,4BAPJ,sBAQQ,QAAA,MAIR,qBACI,SAAA,SACA,OAAA,EACA,KAAA,IACA,kBAAA,iBAAA,UAAA,iBAGJ,kBACI,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,QAAA,EACA,iBAAA,sBACA,cAAA,IAAA,MAAA,uBACA,wBAAA,UAAA,gBAAA,UACA,4BATJ,kBAUQ,SAAA,MACA,iBAAA,sBAGJ,mCACI,SAAA,SACA,OAAA,MACA,KAAA,EACA,MAAA,EACA,cAAA,EAGI,6DACI,MAAA,QACA,UAAA,KACA,QAAA,IAAA,KACA,aAAA,IAAA,MAAA,qBACA,YAAA,OACA,yEACI,aAAA,IAGR,iEACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,WAAA,KAEA,oFACI,mBAAA,KAGJ,6FACI,MAAA,KAGJ,+FACI,OAAA,IAGJ,0FACI,iBAAA,kBACA,cAAA,KACA,OAAA,IAAA,MAAA,YAGJ,0FACI,cAAA,KAKZ,8CACI,QAAA,KAAA,KAKZ,gBACC,mBAAA,EAAA,EAAA,EAAA,IAAA,kBAAA,WAAA,EAAA,EAAA,EAAA,IAAA,kBAIG,wBACI,OAAA,KACA,MAAA,KACA,YAAA,KACA,mBAAA,KAAA,WAAA,KACA,QAAA,EACA,UAAA,KACA,MAAA,mBAEJ,4BAVJ,eAWQ,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,iBAAA,IAAA,cAAA,IAAA,gBAAA,UAIR,oBACI,QAAA,KAAA,KACA,UAAA,KACA,cAAA,IACA,WAAA,KACA,cAAA,IACA,iBAAA,+BACA,YAAA,IAAA,MAAA,8BAEA,uCACI,MAAA,8BACA,UAAA,KAIR,mBACI,OAAA,mBACA,4BAFJ,mBAGQ,OAAA,mBACA,cAAA,MAGJ,8CACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OAEA,iEACI,WAAA,KAIR,2CACI,WAAA,KACA,YAAA,KACA,cAAA,EACA,8CACI,QAAA,YAAA,QAAA,YAAA,QAAA,KAMA,sDACI,cAAA,EAQJ,uDACI,QAAA,KAGR,gDACI,WAAA,OAIA,sDACI,WAAA,QAKZ,gCACI,OAAA,EAAA,KAAA,EAAA,EAEA,oCACI,MAAA,KACA,OAAA,KACA,cAAA,IAIR,mCACI,SAAA,SACA,WAAA,OACA,cAAA,KACA,WAAA,KACA,MAAA,KAEA,0CACI,iBAAA,KACA,SAAA,SACA,UAAA,KACA,QAAA,EACA,QAAA,IAAA,KACA,cAAA,IAGJ,0CACI,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,IACA,KAAA,EACA,MAAA,EACA,iBAAA,+BACA,IAAA,KAGJ,0CACI,UAAA,KAIR,sCACI,cAAA,KACA,QAAA,mBAAA,QAAA,mBAAA,QAAA,YACA,SAAA,SACA,kBAAA,IAAA,eAAA,IAAA,YAAA,SACA,UAAA,IAEA,4BAPJ,sCAQQ,UAAA,KAGJ,kDACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAGJ,qDACI,UAAA,WACA,WAAA,WACA,MAAA,0BAKJ,0DACI,QAAA,KAAA,KACA,iBAAA,0BACA,SAAA,SACA,cAAA,IACA,mBAAA,EAAA,IAAA,IAAA,mBAAA,WAAA,EAAA,IAAA,IAAA,mBAGI,4BACI,+FACI,QAAA,KAGJ,oGACI,QAAA,OAMhB,yDACI,YAAA,IACA,UAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAAA,UAAA,KACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,IAAA,IAIA,iEACI,UAAA,KACA,QAAA,IACA,MAAA,QAEA,4BALJ,iEAMQ,QAAA,MAMZ,iDACI,UAAA,KACA,WAAA,IACA,WAAA,MAGJ,mDACI,cAAA,MACA,SAAA,SACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAAA,UAAA,KACA,IAAA,IAEA,qEACI,SAAA,SAGJ,uDACI,UAAA,MAGJ,qEACI,SAAA,SACA,MAAA,KACA,KAAA,KACA,OAAA,KAGI,0EACI,UAAA,KACA,MAAA,KACA,QAAA,aACA,YAAA,KACA,MAAA,KACA,OAAA,KACA,cAAA,IACA,iBAAA,kBACA,WAAA,OAOpB,0BACI,iBAAA,IAAA,cAAA,IAAA,gBAAA,SAEA,uCACI,0BAAA,EAAA,eAAA,EAAA,MAAA,EACA,aAAA,EACA,YAAA,KAGJ,qCACI,WAAA,KACA,MAAA,QAGJ,6CACI,WAAA,MAEA,yDACI,iBAAA,IAAA,cAAA,IAAA,gBAAA,SAEA,6EACI,0BAAA,EAAA,eAAA,EAAA,MAAA,EACA,iBAAA,gCAEA,WAAA,MACA,mBAAA,KAAA,WAAA,KAEA,iGACI,iBAAA,qBACA,aAAA,8BACA,MAAA,QAEA,oHACI,MAAA,8BAMhB,gEACI,iBAAA,IAAA,cAAA,IAAA,gBAAA,SAEA,oFACI,0BAAA,EAAA,eAAA,EAAA,MAAA,EAGJ,sEACI,0BAAA,EAAA,eAAA,EAAA,MAAA,EAGJ,sEACI,0BAAA,EAAA,eAAA,EAAA,MAAA,EAIR,uDACI,0BAAA,EAAA,eAAA,EAAA,MAAA,EAIR,+BACI,iBAAA,QAMR,+BACI,WAAA,MACA,SAAA,OACA,4BAHJ,+BAIQ,WAAA,OAKZ,mBACI,MAAA,KACA,OAAA,KACA,cAAA,MAAA,WAAA,MAGJ,kBACI,iBAAA,8BACA,MAAA,KACA,cAAA,IACA,YAAA,EAGJ,oBACI,iBAAA,oBACA,WAAA,IAAA,MAAA,uBACA,wBAAA,UAAA,gBAAA,UACA,SAAA,SACA,QAAA,EAEA,4BAPJ,oBAQQ,SAAA,MACA,KAAA,EACA,MAAA,EACA,OAAA,EACA,QAAA,GAGJ,yCACI,SAAA,SACA,OAAA,KACA,KAAA,EACA,MAAA,EACA,WAAA,IAAA,MAAA,uBACA,SAAA,OAEA,4BARJ,yCASQ,OAAA,MAIR,yCACI,QAAA,KACA,SAAA,SACA,IAAA,MACA,KAAA,KACA,UAAA,KACA,MAAA,QAGJ,0BACI,QAAA,MAGJ,mCACI,QAAA,EAIR,aACQ,iBAAA,kBACA,WAAA,IAAA,MAAA,uBACA,QAAA,KAAA,KAEA,mBACI,cAAA,EACA,aAAA,wCAKZ,aAAA,WACI,SAAA,SACA,KAAA,EACA,MAAA,EACA,WAAA,IAAA,MAAA,uBACA,SAAA,OACA,QAAA,EACA,OAAA,EACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,4BAVJ,aAAA,WAWQ,OAAA,OAGJ,kBAAA,gBACI,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEA,4BAJJ,kBAAA,gBAKQ,kBAAA,kBAAA,UAAA,mBAOJ,qCACI,OAAA,IAAA,EACA,8CACI,iBAAA,+BAOhB,kBACI,QAAA,YAAA,QAAA,YAAA,QAAA,KAEI,wCACI,mBAAA,KAAA,WAAA,KACA,QAAA,EACA,UAAA,KACA,MAAA,KACA,OAAA,KACA,iDACI,MAAA,mBAiBf,qBACC,QAAA,aACA,MAAA,IACA,OAAA,IACA,cAAA,IACA,aAAA,KACA,WAAA,QACM,kBAAA,KAAA,KAAA,OAAA,SAAA,UAAA,KAAA,KAAA,OAAA,SACA,QAAA,GAEN,kCACC,wBAAA,MAAA,gBAAA,MAGD,kCACC,wBAAA,KAAA,gBAAA,KAKH,wBACC,GAAA,KAAA,IACC,kBAAA,QAAA,UAAA,QAGD,IACC,kBAAA,iBAAA,UAAA,kBANF,gBACC,GAAA,KAAA,IACC,kBAAA,QAAA,UAAA,QAGD,IACC,kBAAA,iBAAA,UAAA,kBAMF,4BACI,oBAAA,kBACI,iBAAA,mBCzmBR,sBACI,OAAA,MACA,iBAAA,kBACA,QAAA,KACA,UAAA,MACA,UAAA,MAEA,yBAPJ,sBAQQ,YAAA,IAAA,MAAA,wBAGJ,6BAXJ,sBAYQ,SAAA,MACA,MAAA,EACA,IAAA,EACA,QAAA,IAGJ,4BAlBJ,sBAmBQ,UAAA,MAIR,kBACI,SAAA,SAEA,mCACI,QAAA,GACA,SAAA,SACA,KAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,WAAA,8HAAA,WAAA,iFACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,OAAA,KACA,MAAA,qBACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OAGJ,6BACI,UAAA,KACA,MAAA,KAGJ,+BACI,MAAA,KACA,OAAA,MACA,cAAA,MAAA,WAAA,MAEA,yBALJ,+BAMQ,OAAA,OAIR,qDACI,QAAA,KAGJ,sCACI,OAAA,QAIR,oBACI,cAAA,MAAA,WAAA,MAGJ,mBACI,OAAA,oBAEA,4BAHJ,mBAIQ,OAAA,qBAIR,cACI,OAAA,oBAEA,4BAHJ,cAIQ,OAAA,qBAIR,mBACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,IAAA,IAEA,mCACI,SAAA,SACA,qCACI,QAAA,MACA,SAAA,SACA,cAAA,IACA,SAAA,OACA,yCACI,MAAA,KACA,OAAA,KACA,cAAA,MAAA,WAAA,MAGJ,iDACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,MAAA,KAIR,iDACI,SAAA,SACA,IAAA,EACA,MAAA,EACA,MAAA,KACA,WAAA,kBACA,MAAA,KACA,OAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,cAAA,IACA,OAAA,IAOJ,gCACI,MAAA,QACA,uCACI,QAAA,QAQZ,mCACI,QAAA,EACA,YAAA,IAEA,yCACI,aAAA,YAOZ,cACI,OAAA,oBAEA,4BAHJ,cAIQ,OAAA,qBC5JJ,iBACI,OAAA,EAAA,KAGJ,sBACI,QAAA,MAGJ,uBACI,QAAA,KAIR,SACI,iBAAA,8BACA,WAAA,MACA,gBAAA,MACA,oBAAA,OAGJ,mBACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,yBAHJ,mBAIQ,OAAA,OAGJ,4BAPJ,mBAQQ,WAAA,QAIR,6BACI,OAAA,kBACA,iBAAA,KACA,cAAA,KACA,OAAA,KAGJ,UACI,SAAA,SACA,mDAFJ,UAGQ,UAAA,MAEJ,0BALJ,UAMQ,UAAA,MAEJ,4BARJ,UASQ,QAAA,MAQA,gEACI,QAAA,QAQZ,oBACI,SAAA,SACA,0BACI,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,IACA,KAAA,EACA,MAAA,EACA,iBAAA,uBACA,IAAA,KAGJ,2BACI,QAAA,aACA,SAAA,SACA,QAAA,EACA,iBAAA,kBACA,QAAA,IAAA", "file": "app.min.css", "sourcesContent": ["//\r\n// Google font - Public Sans\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');\r\n\r\n//\r\n// Premium font - Cerebri Sans\r\n//\r\n\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-light.eot\");\r\n    src: local('Cerebri-sans Light'), url(\"../fonts/cerebrisans-light.woff\") format(\"woff\");\r\n    font-weight: 300;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-regular.eot\");\r\n    src: local('Cerebri-sans Regular'), url(\"../fonts/cerebrisans-regular.woff\") format(\"woff\");\r\n    font-weight: 400;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-medium.eot\");\r\n    src: local('Cerebri-sans Medium'), url(\"../fonts/cerebrisans-medium.woff\") format(\"woff\");\r\n    font-weight: 500;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-semibold.eot\");\r\n    src: local('Cerebri-sans Semibold'), url(\"../fonts/cerebrisans-semibold.woff\") format(\"woff\");\r\n    font-weight: 600;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-bold.eot\");\r\n    src: local('Cerebri-sans Bold'), url(\"../fonts/cerebrisans-bold.woff\") format(\"woff\");\r\n    font-weight: 700;\r\n}", "// :root CSS variables\r\n\r\n:root{\r\n\r\n  // Vertical Sidebar - Default Light\r\n\r\n  --#{$variable-prefix}sidebar-bg: #2e2e2e;\r\n  --#{$variable-prefix}sidebar-menu-item-color: #878a92;\r\n  --#{$variable-prefix}sidebar-menu-item-active-color: #4eac6d;\r\n  --#{$variable-prefix}sidebar-menu-item-active-bg: #f7f7ff;\r\n  --#{$variable-prefix}sidebar-sub-bg: #ffffff;\r\n\r\n\r\n   // footer\r\n   --#{$variable-prefix}footer-bg: rgba(255,255,255,.05); \r\n\r\n  // Display\r\n  --#{$variable-prefix}display-none: none;\r\n  --#{$variable-prefix}display-block: block;\r\n\r\n  // Chat\r\n  --#{$variable-prefix}chat-text-color:     #495057;\r\n  --#{$variable-prefix}chat-primary-bg:     #ffffff;\r\n  \r\n  // component variable\r\n\r\n  // theme-color\r\n  --#{$variable-prefix}light: #{$gray-300};\r\n  --#{$variable-prefix}light-rgb: #{to-rgb($gray-300)};\r\n  --#{$variable-prefix}dark: #{$gray-900};\r\n  --#{$variable-prefix}dark-rgb: #{to-rgb($gray-900)};\r\n  --#{$variable-prefix}text-muted: #{$gray-600};\r\n\r\n  // link\r\n  --#{$variable-prefix}link-color: #{$primary};\r\n  --#{$variable-prefix}link-hover-color: #{$primary};\r\n\r\n  // Border variable\r\n  --#{$variable-prefix}border-color:  #eaeaf1;\r\n\r\n  // dropdown\r\n  --#{$variable-prefix}dropdown-bg: #{$white};\r\n  --#{$variable-prefix}dropdown-border-color : #f6f6f9;\r\n  --#{$variable-prefix}dropdown-link-color: #{$gray-900};\r\n  --#{$variable-prefix}dropdown-link-hover-color: #{shade-color($gray-900, 5%)};\r\n  --#{$variable-prefix}dropdown-link-hover-bg: #{$gray-100};\r\n  --#{$variable-prefix}dropdown-border-width: 1px;\r\n\r\n  // card\r\n  --#{$variable-prefix}card-bg: #{$white};\r\n  --#{$variable-prefix}card-cap-bg: #{$white};\r\n  --#{$variable-prefix}card-border-color: #eff0f2;\r\n  --#{$variable-prefix}card-logo-dark: block;\r\n  --#{$variable-prefix}card-logo-light: none;\r\n  --#{$variable-prefix}card-box-shadow:   0 2px 3px #e6e8eb;\r\n\r\n  // modal\r\n  --#{$variable-prefix}modal-bg: #{$white};\r\n  --#{$variable-prefix}modal-content-bg: #{$white};\r\n  --#{$variable-prefix}modal-content-border-color: #{$gray-300};\r\n\r\n\r\n  // nav tabs\r\n  --#{$variable-prefix}nav-tabs-link-active-color: #{$gray-700};\r\n  --#{$variable-prefix}nav-tabs-link-active-bg: #{$body-bg};\r\n\r\n  // accordion\r\n  --#{$variable-prefix}accordion-button-active-color: #{shade-color($primary, 10%)};\r\n  --#{$variable-prefix}accordion-bg: #{$white};\r\n  --#{$variable-prefix}accordion-button-bg: #{$gray-200};\r\n  --#{$variable-prefix}accordion-button-active-bg :rgba(246,246,249,.7);\r\n\r\n    // table\r\n    --#{$variable-prefix}table-color: #{$body-color};\r\n\r\n    // Boxed layout \r\n    --#{$variable-prefix}boxed-body-bg:       #e9ebf0;\r\n\r\n    // body\r\n    --#{$variable-prefix}body-heading-color:  #{$gray-700};\r\n    --#{$variable-prefix}body-bg: #f2f2f2;\r\n    --#{$variable-prefix}body-color: #{$gray-700};\r\n    --#{$variable-prefix}body-color-rgb: #{to-rgb($body-color)};\r\n\r\n  // progress\r\n  --#{$variable-prefix}progress-bg: #{$gray-200};\r\n\r\n  // toast\r\n  --#{$variable-prefix}toast-background-color: #{rgba($white, .85)};\r\n  --#{$variable-prefix}toast-border-color: #{rgba($black, .1)};\r\n  --#{$variable-prefix}toast-header-border-color: #{rgba($black, .05)};\r\n\r\n  //list\r\n  --#{$variable-prefix}list-group-hover-bg: #{$gray-100};\r\n\r\n  // popover\r\n  --#{$variable-prefix}popover-bg: #{$white};\r\n\r\n  // pagination\r\n  --#{$variable-prefix}pagination-hover-bg: #{$gray-200};\r\n\r\n  // form\r\n  --#{$variable-prefix}input-bg: #{$white};\r\n  --#{$variable-prefix}input-group-addon-border-color: #{$gray-400};\r\n  --#{$variable-prefix}input-border: #{$gray-400};\r\n  --#{$variable-prefix}input-border-color: #cfd4d8;\r\n  --#{$variable-prefix}input-focus-border: #{tint-color($component-active-bg, 50%)};\r\n  --#{$variable-prefix}input-disabled-bg: #{$gray-200};\r\n\r\n  // input\r\n  --#{$variable-prefix}input-placeholder-color: #{$gray-600};\r\n  --#{$variable-prefix}input-group-addon-bg: #{$gray-200};\r\n\r\n\r\n  // Breadcrumbs\r\n\r\n  //check\r\n  --#{$variable-prefix}input-check-border: var(--#{$variable-prefix}input-border);\r\n\r\n  \r\n}\r\n", "// :root CSS dark variables\r\n\r\n\r\n[data-layout-mode=\"dark\"]{\r\n\r\n  // Color system - Dark Mode only\r\n  $gray-100: #212529;\r\n  $gray-200: #262626;\r\n  $gray-300: #2e2e2e;\r\n  $gray-400: #757575;\r\n  $gray-500: #8f9198;\r\n  $gray-600: #adb5bd;\r\n  $gray-700: #adb5bd;\r\n  $gray-800: #f9f9f9;\r\n  $gray-900: #f8f9fa;\r\n\r\n  $grays: (\r\n      \"100\": $gray-100,\r\n      \"200\": $gray-200,\r\n      \"300\": $gray-300,\r\n      \"400\": $gray-400,\r\n      \"500\": $gray-500,\r\n      \"600\": $gray-600,\r\n      \"700\": $gray-700,\r\n      \"800\": $gray-800,\r\n      \"900\": $gray-900\r\n  );\r\n\r\n  // Prefix for :root CSS variables\r\n  @each $color, $value in $grays {\r\n      --#{$variable-prefix}gray-#{$color}: #{$value};\r\n  }\r\n\r\n  // Vertical Sidebar - Default Light\r\n\r\n  --#{$variable-prefix}sidebar-bg: #2e2e2e;\r\n  --#{$variable-prefix}sidebar-menu-item-color: #878a92;\r\n  --#{$variable-prefix}sidebar-menu-item-active-color: #4eac6d;\r\n  --#{$variable-prefix}sidebar-menu-item-active-bg: #f7f7ff;\r\n  --#{$variable-prefix}sidebar-sub-bg: #262626;\r\n\r\n  // footer\r\n  --#{$variable-prefix}footer-bg: rgba(46,46,46,.5); \r\n\r\n  // Display\r\n  --#{$variable-prefix}display-none: block;\r\n  --#{$variable-prefix}display-block: none;\r\n\r\n    // Chat\r\n    --#{$variable-prefix}chat-text-color:     rgba(255,255,255,.8);\r\n    --#{$variable-prefix}chat-primary-bg:     #383838;\r\n\r\n    // card-title-desc\r\n  --#{$variable-prefix}card-title-desc: #{$gray-400};\r\n\r\n\r\n  // Horizontal nav\r\n  --#{$variable-prefix}topnav-bg: #{lighten($gray-200, 2.5%)};\r\n  --#{$variable-prefix}topnav-item-color: #{$gray-400};\r\n  --#{$variable-prefix}topnav-item-color-active: #{$white};\r\n\r\n  // twocolumn menu\r\n  --#{$variable-prefix}twocolumn-menu-iconview-bg: #{$white};\r\n  --#{$variable-prefix}twocolumn-menu-bg:  #{$white};\r\n    \r\n  // two column dark\r\n  --#{$variable-prefix}twocolumn-menu-iconview-bg-dark: var(--#{$variable-prefix}vertical-menu-bg-dark);\r\n  --#{$variable-prefix}twocolumn-menu-bg-dark:  #30363a;\r\n  --#{$variable-prefix}twocolumn-menu-item-color-dark: var(--#{$variable-prefix}vertical-menu-item-color-dark);\r\n  --#{$variable-prefix}twocolumn-menu-item-active-color-dark:  #{$white};\r\n  --#{$variable-prefix}twocolumn-menu-item-active-bg-dark:  #{rgba($white, .15)};\r\n\r\n  // boxed \r\n  --#{$variable-prefix}boxed-body-bg: #{darken($gray-100, 4%)};\r\n\r\n  // heading-color\r\n  --#{$variable-prefix}heading-color: #{$gray-700};\r\n  \r\n  // component variable\r\n\r\n  --#{$variable-prefix}light: #{$gray-300};\r\n  --#{$variable-prefix}light-rgb: #{to-rgb($gray-300)};\r\n  --#{$variable-prefix}dark: #{$gray-800};\r\n  --#{$variable-prefix}dark-rgb: #{to-rgb($gray-800)};\r\n  --#{$variable-prefix}text-muted: #{$gray-500};\r\n\r\n  // link\r\n  --#{$variable-prefix}link-color: #{$gray-700};\r\n  --#{$variable-prefix}link-hover-color: #{$gray-700};\r\n\r\n  // Border variable\r\n  --#{$variable-prefix}border-color:  #333333;\r\n\r\n  // dropdown\r\n  --#{$variable-prefix}dropdown-bg: #333333;\r\n  --#{$variable-prefix}dropdown-border-color : #383838;\r\n  --#{$variable-prefix}dropdown-link-color: #{$gray-500};\r\n  --#{$variable-prefix}dropdown-link-hover-color:  #{$gray-500};\r\n  --#{$variable-prefix}dropdown-link-hover-bg: #383838;\r\n  --#{$variable-prefix}dropdown-border-width: 1px;\r\n\r\n  // card\r\n  --#{$variable-prefix}card-bg: #262626;\r\n  --#{$variable-prefix}card-cap-bg: #{$gray-200};\r\n  --#{$variable-prefix}card-border-color: #082a3e;\r\n  --#{$variable-prefix}card-logo-dark: none;\r\n  --#{$variable-prefix}card-logo-light: block;\r\n  --#{$variable-prefix}card-box-shadow:   0 2px 3px #04121a;\r\n\r\n  // modal\r\n  --#{$variable-prefix}modal-bg: #{$gray-200};\r\n  --#{$variable-prefix}modal-content-bg: #{$gray-200};\r\n  --#{$variable-prefix}modal-content-border-color: #{$gray-300};\r\n\r\n  // nav tabs\r\n  --#{$variable-prefix}nav-tabs-link-active-color: #{$gray-900};\r\n  --#{$variable-prefix}nav-tabs-link-active-bg: #{$gray-300};\r\n\r\n    // accordion\r\n  --#{$variable-prefix}accordion-button-active-color: #{$white};\r\n  --#{$variable-prefix}accordion-bg: #{transparent};\r\n  --#{$variable-prefix}accordion-button-bg: #{$gray-300};\r\n  --#{$variable-prefix}accordion-button-active-bg :#2e2e2e;\r\n\r\n  // table\r\n  --#{$variable-prefix}table-color: #{$gray-400};\r\n\r\n  // Boxed layout \r\n  --#{$variable-prefix}boxed-body-bg:       #333847;\r\n\r\n    // body\r\n    --#{$variable-prefix}body-heading-color:  #{$gray-600};\r\n    --#{$variable-prefix}body-bg : #2e2e2e;\r\n    --#{$variable-prefix}body-color: #{$gray-500};\r\n    --#{$variable-prefix}body-color-rgb: #{to-rgb($body-color)};\r\n\r\n  // progress\r\n  --#{$variable-prefix}progress-bg: #{$gray-300};\r\n\r\n  // toast\r\n  --#{$variable-prefix}toast-background-color: #{rgba($gray-300, .85)};\r\n  --#{$variable-prefix}toast-border-color: #{rgba($white, .1)};\r\n  --#{$variable-prefix}toast-header-border-color: #{rgba($white, .05)};\r\n\r\n  //list\r\n  --#{$variable-prefix}list-group-hover-bg: #{lighten($gray-200, 2.5%)};\r\n\r\n  // popover\r\n  --#{$variable-prefix}popover-bg: #{lighten($gray-200, 1%)};\r\n\r\n  // pagination\r\n  --#{$variable-prefix}pagination-hover-bg: #{lighten($gray-200, 4%)};\r\n\r\n  //form\r\n  --#{$variable-prefix}input-bg: #333333;\r\n  --#{$variable-prefix}input-group-addon-border-color: #{$gray-300};\r\n  --#{$variable-prefix}input-border: #{$gray-300};\r\n  --#{$variable-prefix}input-border-color: #333333;\r\n  --#{$variable-prefix}input-focus-border: #{lighten($gray-300, 4%)};\r\n  --#{$variable-prefix}input-disabled-bg: #{$gray-200};\r\n\r\n  // input-group-addon\r\n  --#{$variable-prefix}input-placeholder-color: #{$gray-500};\r\n  --#{$variable-prefix}input-group-addon-bg: #{$gray-300};\r\n\r\n  //check\r\n  --#{$variable-prefix}input-check-border:  #{lighten($gray-300, 4%)};\r\n\r\n}", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "/*\nTemplate Name: Doot - Responsive Bootstrap 5 Chat App\nAuthor: Themesbrand\nVersion: 1.0.0\nWebsite: https://Themesbrand.com/\nContact: <EMAIL>\nFile: Main Css File\n*/\n@import url(\"https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap\");\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-light.eot\");\n  src: local(\"Cerebri-sans Light\"), url(\"../fonts/cerebrisans-light.woff\") format(\"woff\");\n  font-weight: 300;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-regular.eot\");\n  src: local(\"Cerebri-sans Regular\"), url(\"../fonts/cerebrisans-regular.woff\") format(\"woff\");\n  font-weight: 400;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-medium.eot\");\n  src: local(\"Cerebri-sans Medium\"), url(\"../fonts/cerebrisans-medium.woff\") format(\"woff\");\n  font-weight: 500;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-semibold.eot\");\n  src: local(\"Cerebri-sans Semibold\"), url(\"../fonts/cerebrisans-semibold.woff\") format(\"woff\");\n  font-weight: 600;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-bold.eot\");\n  src: local(\"Cerebri-sans Bold\"), url(\"../fonts/cerebrisans-bold.woff\") format(\"woff\");\n  font-weight: 700;\n}\n:root {\n  --bs-sidebar-bg: #2e2e2e;\n  --bs-sidebar-menu-item-color: #878a92;\n  --bs-sidebar-menu-item-active-color: #4eac6d;\n  --bs-sidebar-menu-item-active-bg: #f7f7ff;\n  --bs-sidebar-sub-bg: #ffffff;\n  --bs-footer-bg: rgba(255,255,255,.05);\n  --bs-display-none: none;\n  --bs-display-block: block;\n  --bs-chat-text-color: #495057;\n  --bs-chat-primary-bg: #ffffff;\n  --bs-light: #f6f6f9;\n  --bs-light-rgb: 246, 246, 249;\n  --bs-dark: #212529;\n  --bs-dark-rgb: 33, 37, 41;\n  --bs-text-muted: #797c8c;\n  --bs-link-color: #4eac6d;\n  --bs-link-hover-color: #4eac6d;\n  --bs-border-color: #eaeaf1;\n  --bs-dropdown-bg: #fff;\n  --bs-dropdown-border-color: #f6f6f9;\n  --bs-dropdown-link-color: #212529;\n  --bs-dropdown-link-hover-color: #1f2327;\n  --bs-dropdown-link-hover-bg: #f8f9fa;\n  --bs-dropdown-border-width: 1px;\n  --bs-card-bg: #fff;\n  --bs-card-cap-bg: #fff;\n  --bs-card-border-color: #eff0f2;\n  --bs-card-logo-dark: block;\n  --bs-card-logo-light: none;\n  --bs-card-box-shadow: 0 2px 3px #e6e8eb;\n  --bs-modal-bg: #fff;\n  --bs-modal-content-bg: #fff;\n  --bs-modal-content-border-color: #f6f6f9;\n  --bs-nav-tabs-link-active-color: #495057;\n  --bs-nav-tabs-link-active-bg: #fafafa;\n  --bs-accordion-button-active-color: #469b62;\n  --bs-accordion-bg: #fff;\n  --bs-accordion-button-bg: #f9f9f9;\n  --bs-accordion-button-active-bg:rgba(246,246,249,.7);\n  --bs-table-color: #495057;\n  --bs-boxed-body-bg: #e9ebf0;\n  --bs-body-heading-color: #495057;\n  --bs-body-bg: #f2f2f2;\n  --bs-body-color: #495057;\n  --bs-body-color-rgb: 73, 80, 87;\n  --bs-progress-bg: #f9f9f9;\n  --bs-toast-background-color: rgba(255, 255, 255, 0.85);\n  --bs-toast-border-color: rgba(0, 0, 0, 0.1);\n  --bs-toast-header-border-color: rgba(0, 0, 0, 0.05);\n  --bs-list-group-hover-bg: #f8f9fa;\n  --bs-popover-bg: #fff;\n  --bs-pagination-hover-bg: #f9f9f9;\n  --bs-input-bg: #fff;\n  --bs-input-group-addon-border-color: #e6ebf5;\n  --bs-input-border: #e6ebf5;\n  --bs-input-border-color: #cfd4d8;\n  --bs-input-focus-border: #a7d6b6;\n  --bs-input-disabled-bg: #f9f9f9;\n  --bs-input-placeholder-color: #797c8c;\n  --bs-input-group-addon-bg: #f9f9f9;\n  --bs-input-check-border: var(--bs-input-border);\n}\n\n[data-layout-mode=dark] {\n  --bs-gray-100: #212529;\n  --bs-gray-200: #262626;\n  --bs-gray-300: #2e2e2e;\n  --bs-gray-400: #757575;\n  --bs-gray-500: #8f9198;\n  --bs-gray-600: #adb5bd;\n  --bs-gray-700: #adb5bd;\n  --bs-gray-800: #f9f9f9;\n  --bs-gray-900: #f8f9fa;\n  --bs-sidebar-bg: #2e2e2e;\n  --bs-sidebar-menu-item-color: #878a92;\n  --bs-sidebar-menu-item-active-color: #4eac6d;\n  --bs-sidebar-menu-item-active-bg: #f7f7ff;\n  --bs-sidebar-sub-bg: #262626;\n  --bs-footer-bg: rgba(46,46,46,.5);\n  --bs-display-none: block;\n  --bs-display-block: none;\n  --bs-chat-text-color: rgba(255,255,255,.8);\n  --bs-chat-primary-bg: #383838;\n  --bs-card-title-desc: #757575;\n  --bs-topnav-bg: #2c2c2c;\n  --bs-topnav-item-color: #757575;\n  --bs-topnav-item-color-active: #fff;\n  --bs-twocolumn-menu-iconview-bg: #fff;\n  --bs-twocolumn-menu-bg: #fff;\n  --bs-twocolumn-menu-iconview-bg-dark: var(--bs-vertical-menu-bg-dark);\n  --bs-twocolumn-menu-bg-dark: #30363a;\n  --bs-twocolumn-menu-item-color-dark: var(--bs-vertical-menu-item-color-dark);\n  --bs-twocolumn-menu-item-active-color-dark: #fff;\n  --bs-twocolumn-menu-item-active-bg-dark: rgba(255, 255, 255, 0.15);\n  --bs-boxed-body-bg: #181b1e;\n  --bs-heading-color: #adb5bd;\n  --bs-light: #2e2e2e;\n  --bs-light-rgb: 46, 46, 46;\n  --bs-dark: #f9f9f9;\n  --bs-dark-rgb: 249, 249, 249;\n  --bs-text-muted: #8f9198;\n  --bs-link-color: #adb5bd;\n  --bs-link-hover-color: #adb5bd;\n  --bs-border-color: #333333;\n  --bs-dropdown-bg: #333333;\n  --bs-dropdown-border-color: #383838;\n  --bs-dropdown-link-color: #8f9198;\n  --bs-dropdown-link-hover-color: #8f9198;\n  --bs-dropdown-link-hover-bg: #383838;\n  --bs-dropdown-border-width: 1px;\n  --bs-card-bg: #262626;\n  --bs-card-cap-bg: #262626;\n  --bs-card-border-color: #082a3e;\n  --bs-card-logo-dark: none;\n  --bs-card-logo-light: block;\n  --bs-card-box-shadow: 0 2px 3px #04121a;\n  --bs-modal-bg: #262626;\n  --bs-modal-content-bg: #262626;\n  --bs-modal-content-border-color: #2e2e2e;\n  --bs-nav-tabs-link-active-color: #f8f9fa;\n  --bs-nav-tabs-link-active-bg: #2e2e2e;\n  --bs-accordion-button-active-color: #fff;\n  --bs-accordion-bg: transparent;\n  --bs-accordion-button-bg: #2e2e2e;\n  --bs-accordion-button-active-bg:#2e2e2e;\n  --bs-table-color: #757575;\n  --bs-boxed-body-bg: #333847;\n  --bs-body-heading-color: #adb5bd;\n  --bs-body-bg: #2e2e2e;\n  --bs-body-color: #8f9198;\n  --bs-body-color-rgb: 73, 80, 87;\n  --bs-progress-bg: #2e2e2e;\n  --bs-toast-background-color: rgba(46, 46, 46, 0.85);\n  --bs-toast-border-color: rgba(255, 255, 255, 0.1);\n  --bs-toast-header-border-color: rgba(255, 255, 255, 0.05);\n  --bs-list-group-hover-bg: #2c2c2c;\n  --bs-popover-bg: #292929;\n  --bs-pagination-hover-bg: #303030;\n  --bs-input-bg: #333333;\n  --bs-input-group-addon-border-color: #2e2e2e;\n  --bs-input-border: #2e2e2e;\n  --bs-input-border-color: #333333;\n  --bs-input-focus-border: #383838;\n  --bs-input-disabled-bg: #262626;\n  --bs-input-placeholder-color: #8f9198;\n  --bs-input-group-addon-bg: #2e2e2e;\n  --bs-input-check-border: #383838;\n}\n\n/*!\n * Waves v0.7.6\n * http://fian.my.id/Waves \n * \n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \n * Released under the MIT license \n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n}\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important;\n}\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\n}\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1;\n}\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em;\n}\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em;\n}\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom;\n}\n\n.waves-input-wrapper.waves-button {\n  padding: 0;\n}\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%;\n}\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms;\n}\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n}\n\n.waves-block {\n  display: block;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4);\n}\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(78, 172, 109, 0.4);\n}\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(6, 214, 160, 0.4);\n}\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(80, 165, 241, 0.4);\n}\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(255, 209, 102, 0.4);\n}\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(239, 71, 111, 0.4);\n}\n\n.avatar-xs {\n  height: 1.8rem;\n  width: 1.8rem;\n}\n\n.avatar-sm {\n  height: 2.4rem;\n  width: 2.4rem;\n}\n\n.avatar-md {\n  height: 4rem;\n  width: 4rem;\n}\n\n.avatar-lg {\n  height: 5rem;\n  width: 5rem;\n}\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: #fff;\n  display: flex;\n  font-weight: 500;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 8px;\n}\n.avatar-group .avatar-group-item {\n  margin-left: -8px;\n  border: 2px solid var(--bs-card-bg);\n  border-radius: 50%;\n  transition: all 0.2s;\n}\n.avatar-group .avatar-group-item:hover {\n  position: relative;\n  transform: translateY(-2px);\n}\n\n.font-size-10 {\n  font-size: 10px !important;\n}\n\n.font-size-11 {\n  font-size: 11px !important;\n}\n\n.font-size-12 {\n  font-size: 12px !important;\n}\n\n.font-size-13 {\n  font-size: 13px !important;\n}\n\n.font-size-14 {\n  font-size: 14px !important;\n}\n\n.font-size-15 {\n  font-size: 15px !important;\n}\n\n.font-size-16 {\n  font-size: 16px !important;\n}\n\n.font-size-17 {\n  font-size: 17px !important;\n}\n\n.font-size-18 {\n  font-size: 18px !important;\n}\n\n.font-size-20 {\n  font-size: 20px !important;\n}\n\n.font-size-22 {\n  font-size: 22px !important;\n}\n\n.font-size-24 {\n  font-size: 24px !important;\n}\n\n.fw-medium {\n  font-weight: 500;\n}\n\n.fw-semibold {\n  font-weight: 600;\n}\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 4px);\n  display: block;\n  border: 2px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd;\n  text-align: center;\n  transition: all 0.4s;\n}\n.social-list-item:hover {\n  color: #797c8c;\n  background-color: #f9f9f9;\n}\n\n.w-xs {\n  min-width: 80px;\n}\n\n.w-sm {\n  min-width: 95px;\n}\n\n.w-md {\n  min-width: 110px;\n}\n\n.w-lg {\n  min-width: 140px;\n}\n\n.w-xl {\n  min-width: 160px;\n}\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  background-color: rgba(52, 58, 64, 0.7);\n}\n\n.border-primary {\n  border-color: rgba(var(--bs-primary-rgb), 1) !important;\n}\n\n.border-dark {\n  border-color: var(--bs-dark) !important;\n}\n\n.border-light {\n  border-color: var(--bs-light) !important;\n}\n\n[data-layout-mode=dark] .text-body {\n  color: var(--bs-gray-500) !important;\n}\n[data-layout-mode=dark] .btn-close {\n  filter: invert(1) grayscale(100%) brightness(200%);\n}\n\n.loader-line {\n  height: 28px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n.loader-line .line:nth-last-child(1) {\n  animation: loadingLine 1.25s 1s infinite;\n}\n.loader-line .line:nth-last-child(2) {\n  animation: loadingLine 1.25s 0.75s infinite;\n}\n.loader-line .line:nth-last-child(3) {\n  animation: loadingLine 1.25s 0.5s infinite;\n}\n.loader-line .line:nth-last-child(4) {\n  animation: loadingLine 1.25s 0.25s infinite;\n}\n.loader-line .line:nth-last-child(5) {\n  animation: loadingLine 1.25s 0s infinite;\n}\n\n.line {\n  display: inline-block;\n  width: 4px;\n  height: 10px;\n  border-radius: 14px;\n  background-color: rgba(var(--bs-text-muted), 0.7);\n}\n\n@keyframes loadingLine {\n  0% {\n    height: 10px;\n  }\n  50% {\n    height: 28px;\n  }\n  100% {\n    height: 10px;\n  }\n}\n.custom-accordion .card + .card {\n  margin-top: 0.5rem;\n}\n.custom-accordion a i.accor-plus-icon {\n  font-size: 16px;\n}\n.custom-accordion a.collapsed i.accor-plus-icon:before {\n  content: \"\\f0142\";\n}\n\n.accordion-button:after {\n  margin-left: auto;\n  margin-right: 0;\n}\n\n[data-layout-mode=dark] .accordion-button:after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23adb5bd'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e\");\n}\n\n.modal-header-colored .modal-header {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  margin: 0 -1px -1px -1px;\n}\n\n[type=tel]::placeholder,\n[type=url]::placeholder,\n[type=email]::placeholder,\n[type=number]::placeholder {\n  text-align: left;\n  /*rtl: right*/\n}\n\n.form-check,\n.form-check-input,\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0;\n}\n\n.form-check-input:checked {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  border-color: rgba(var(--bs-primary-rgb), 1);\n}\n\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto;\n  /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: \" \";\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 7px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: \"\";\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}\n\n.fg-emoji-picker {\n  background-color: var(--bs-card-bg) !important;\n  width: 250px !important;\n  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12) !important;\n  top: auto !important;\n  bottom: 70px;\n}\n.fg-emoji-picker * {\n  font-family: var(--bs-font-sans-serif) !important;\n  color: #495057 !important;\n}\n@media (max-width: 991.98px) {\n  .fg-emoji-picker {\n    left: 14px !important;\n    top: auto !important;\n    bottom: 60px;\n  }\n}\n.fg-emoji-picker .fg-emoji-picker-container-title {\n  color: var(--bs-dark) !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search {\n  height: 40px !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search input {\n  background-color: var(--bs-input-bg) !important;\n  color: var(--bs-body-color) !important;\n  border: 1px solid var(--bs-border-color);\n  padding: 0.5rem 1rem !important;\n  font-size: 0.9375rem !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search input::placeholder {\n  color: var(--bs-input-placeholder-color) !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search svg {\n  fill: var(--bs-body-color) !important;\n  right: 11px;\n  top: 12px;\n}\n.fg-emoji-picker .fg-emoji-picker-categories {\n  background-color: var(--bs-light) !important;\n}\n.fg-emoji-picker .fg-emoji-picker-categories li.active {\n  background-color: rgba(var(--bs-primary-rgb), 0.2);\n}\n.fg-emoji-picker .fg-emoji-picker-categories a:hover {\n  background-color: rgba(var(--bs-primary-rgb), 0.2);\n}\n\n.fg-emoji-picker-grid > li:hover {\n  background-color: rgba(var(--bs-primary-rgb), 0.2) !important;\n}\n\na.fg-emoji-picker-close-button {\n  background-color: var(--bs-light) !important;\n}\n\n.fg-emoji-picker-categories svg {\n  fill: var(--bs-body-color) !important;\n}\n\n.fg-emoji-picker-grid > li {\n  background-color: var(--bs-card-bg) !important;\n}\n\n.ml-44 {\n  margin-left: 44px;\n}\n\n.side-menu {\n  min-width: 75px;\n  max-width: 75px;\n  height: 100vh;\n  min-height: 570px;\n  background-color: var(--bs-sidebar-bg);\n  display: flex;\n  z-index: 9;\n  border-right: 1px solid var(--bs-sidebar-bg);\n  padding: 0 8px;\n}\n@media (max-width: 991.98px) {\n  .side-menu {\n    position: fixed;\n    bottom: 0;\n    height: 60px;\n    min-width: 100%;\n    min-height: auto;\n    border-top: 1px solid var(--bs-sidebar-bg);\n  }\n}\n.side-menu .navbar-brand-box {\n  text-align: center;\n}\n.side-menu .navbar-brand-box svg {\n  fill: rgba(var(--bs-primary-rgb), 1);\n}\n@media (max-width: 991.98px) {\n  .side-menu .navbar-brand-box {\n    display: none;\n  }\n}\n.side-menu .navbar-brand-box .logo {\n  line-height: 70px;\n}\n.side-menu .navbar-brand-box .logo-dark {\n  display: block;\n}\n.side-menu .navbar-brand-box .logo-light {\n  display: none;\n}\n\n.sidemenu-navigation {\n  height: 100%;\n}\n@media (max-width: 991.98px) {\n  .sidemenu-navigation {\n    width: 100%;\n  }\n  .sidemenu-navigation .tooltip {\n    display: none;\n  }\n}\n\n.side-menu-nav {\n  height: 100%;\n  flex-direction: column;\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav {\n    justify-content: space-between !important;\n    flex-direction: row;\n  }\n}\n.side-menu-nav .nav-item {\n  margin: 7px 0;\n  display: block;\n  width: 100%;\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    margin: 5px 0;\n  }\n}\n.side-menu-nav .nav-item .nav-link {\n  text-align: center;\n  font-size: 28px;\n  color: var(--bs-sidebar-menu-item-color);\n  width: 100%;\n  height: 56px;\n  line-height: 56px;\n  border-radius: 0;\n  padding: 0;\n  position: relative;\n}\n.side-menu-nav .nav-item .nav-link i {\n  vertical-align: middle;\n}\n.side-menu-nav .nav-item .nav-link::before {\n  position: absolute;\n  content: \"\";\n  height: 20px;\n  width: 2px;\n  right: -8px;\n  top: 18px;\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav .nav-item .nav-link::before {\n    width: 20px;\n    height: 2px;\n    right: auto;\n    top: -5px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav .nav-item .nav-link {\n    font-size: 20px;\n    width: 48px;\n    height: 48px;\n    line-height: 48px;\n    margin: 0px auto;\n  }\n}\n.side-menu-nav .nav-item .nav-link.active {\n  background-color: transparent;\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.side-menu-nav .nav-item .nav-link.active::before {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n}\n.side-menu-nav .nav-item.show > .nav-link {\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.side-menu-nav .profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: var(--bs-gray-300);\n  padding: 3px;\n}\n\n.light-mode {\n  display: none;\n}\n\nbody[data-layout-mode=dark] .light-mode {\n  display: inline-block;\n}\nbody[data-layout-mode=dark] .dark-mode {\n  display: none;\n}\n\nbody[data-layout-mode=dark] .side-menu-nav .nav-item .nav-link.light-dark .bx-moon:before {\n  content: \"\\eb90\";\n}\nbody[data-layout-mode=dark] .light-mode {\n  display: inline-block;\n}\nbody[data-layout-mode=dark] .dark-mode {\n  display: none;\n}\n\n.chat-leftsidebar {\n  height: calc(100vh - 60px);\n  position: relative;\n  background-color: var(--bs-sidebar-sub-bg);\n  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);\n}\n@media (min-width: 992px) {\n  .chat-leftsidebar {\n    min-width: 300px;\n    max-width: 300px;\n    height: 100vh;\n  }\n}\n.chat-leftsidebar .user-status-box {\n  background-color: #f6f6f9;\n  padding: 8px;\n  border-radius: 8px;\n  text-align: center;\n  margin-top: 16px;\n  display: block;\n}\n.chat-leftsidebar .user-status-box .chat-user-img {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n}\n\n.chat-search-box .form-control {\n  border: 0;\n}\n.chat-search-box .search-icon-btn {\n  font-size: 16px;\n  position: absolute;\n  right: 13px;\n  top: 0;\n}\n\n.chat-room-list {\n  max-height: calc(100vh - 130px);\n}\n@media (max-width: 991.98px) {\n  .chat-room-list {\n    height: calc(100vh - 190px);\n  }\n}\n\n.chat-group-list {\n  height: calc(100vh - 140px);\n}\n@media (max-width: 991.98px) {\n  .chat-group-list {\n    height: calc(100vh - 198px);\n  }\n}\n\n.chat-list {\n  margin: 0;\n}\n.chat-list li.active a {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: #fff;\n}\n.chat-list li.active a span.avatar-title {\n  color: #fff !important;\n}\n.chat-list li.active a span.avatar-title.bg-light {\n  background-color: rgba(246, 246, 249, 0.25) !important;\n}\n.chat-list li.active a .badge {\n  background-color: rgba(246, 246, 249, 0.25) !important;\n  color: #fff !important;\n}\n.chat-list li.active a .bg-primary {\n  background-color: rgba(255, 255, 255, 0.25) !important;\n}\n.chat-list li a {\n  display: block;\n  padding: 5px 24px;\n  color: var(--bs-gray-700);\n  transition: all 0.4s;\n  font-size: 14px;\n}\n.chat-list li .chat-user-message {\n  font-size: 14px;\n}\n.chat-list li .unread-msg-user {\n  font-weight: 600;\n}\n.chat-list li.typing .chat-user-message {\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-weight: 500;\n}\n.chat-list li.typing .chat-user-message .dot {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n}\n.chat-list li .unread-message {\n  position: absolute;\n  display: inline-block;\n  right: 24px;\n  left: auto;\n  top: 33px;\n}\n.chat-list li .unread-message .badge {\n  line-height: 16px;\n  font-weight: 600;\n  font-size: 10px;\n}\n\n.chat-user-img {\n  position: relative;\n}\n.chat-user-img .user-status {\n  width: 10px;\n  height: 10px;\n  background-color: #adb5bd;\n  border-radius: 50%;\n  border: 2px solid var(--bs-card-bg);\n  position: absolute;\n  right: 0;\n  left: auto;\n  bottom: 0;\n}\n.chat-user-img.online .user-status {\n  background-color: #06D6A0;\n}\n.chat-user-img.away .user-status {\n  background-color: #FFD166;\n}\n\n.contact-list li {\n  cursor: pointer;\n  padding: 8px 24px;\n}\n\n.contact-list-title {\n  padding: 6px 24px;\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-weight: 500;\n  position: relative;\n  font-size: 12px;\n}\n.contact-list-title:after {\n  content: \"\";\n  height: 1px;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  left: 50px;\n  right: 0;\n  background-color: var(--bs-border-color);\n}\n\n.chat-call-list, .chat-bookmark-list {\n  max-height: calc(100vh - 68px);\n}\n@media (max-width: 991.98px) {\n  .chat-call-list, .chat-bookmark-list {\n    height: calc(100vh - 125px);\n  }\n}\n.chat-call-list li, .chat-bookmark-list li {\n  position: relative;\n  padding: 10px 24px;\n  border-bottom: 1px solid var(--bs-border-color);\n}\n.chat-call-list li:last-child, .chat-bookmark-list li:last-child {\n  border-bottom: 0;\n}\n\n.profile-user {\n  position: relative;\n  display: inline-block;\n}\n.profile-user .profile-photo-edit {\n  position: absolute;\n  right: 0;\n  left: auto;\n  bottom: 0;\n  cursor: pointer;\n}\n.profile-user .user-profile-img {\n  object-fit: cover;\n}\n.profile-user .profile-img-file-input {\n  display: none;\n}\n\n.theme-btn-list.theme-color-list .form-check .form-check-input:checked + .form-check-label::before {\n  color: #fff;\n}\n.theme-btn-list .form-check {\n  padding: 0;\n}\n.theme-btn-list .form-check .form-check-input {\n  display: none;\n}\n.theme-btn-list .form-check .form-check-label {\n  position: relative;\n}\n.theme-btn-list .form-check .form-check-input:checked + .form-check-label::before {\n  content: \"\\e9a4\";\n  font-family: boxicons !important;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translateX(-50%) translateY(-50%);\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-size: 16px;\n  z-index: 1;\n}\n.theme-btn-list .form-check .form-check-input:checked + .form-check-label.light-background::before {\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.theme-btn-list.theme-btn-list-img .theme-btn {\n  background-color: transparent !important;\n}\n\n.theme-btn {\n  cursor: pointer;\n  border: 1px solid var(--bs-gray-400);\n}\n\n.user-chat {\n  background: url(\"../images/bg-pattern/pattern-05.png\");\n  transition: all 0.4s;\n  position: relative;\n  background-color: var(--bs-body-bg);\n}\n.user-chat .user-chat-overlay {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  background-color: transparent;\n  opacity: 0.1;\n}\n@media (max-width: 991.98px) {\n  .user-chat {\n    position: fixed;\n    left: 0;\n    top: 0;\n    width: 100%;\n    height: 100%;\n    visibility: hidden;\n    transform: translateX(100%);\n    z-index: 99;\n  }\n  .user-chat.user-chat-show {\n    visibility: visible;\n    transform: translateX(0);\n  }\n}\n.user-chat .chat-content {\n  position: relative;\n}\n.user-chat.user-chat-show .chat-welcome-section {\n  display: none;\n}\n@media (min-width: 992px) {\n  .user-chat.user-chat-show .chat-content {\n    display: flex !important;\n  }\n}\n\n.chat-welcome-section {\n  display: flex;\n  width: 100%;\n  height: 100vh;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n@media (max-width: 991.98px) {\n  .chat-welcome-section {\n    display: none;\n  }\n}\n\n.copyclipboard-alert {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.user-chat-topbar {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  z-index: 1;\n  background-color: rgba(255, 255, 255, 0.05);\n  border-bottom: 1px solid var(--bs-border-color);\n  backdrop-filter: blur(7px);\n}\n@media (max-width: 991.98px) {\n  .user-chat-topbar {\n    position: fixed;\n    background-color: rgba(255, 255, 255, 0.8);\n  }\n}\n.user-chat-topbar .topbar-bookmark {\n  position: absolute;\n  bottom: -51px;\n  left: 0;\n  right: 0;\n  border-radius: 0;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-links {\n  color: #cc8f00;\n  font-size: 14px;\n  padding: 1px 16px;\n  border-right: 1px solid rgba(255, 209, 102, 0.7);\n  white-space: nowrap;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-links:first-child {\n  padding-left: 4px;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link {\n  display: flex;\n  overflow-x: auto;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar {\n  -webkit-appearance: none;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar:vertical {\n  width: 12px;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar:horizontal {\n  height: 5px;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar-thumb {\n  background-color: rgba(52, 58, 64, 0.1);\n  border-radius: 10px;\n  border: 2px solid transparent;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar-track {\n  border-radius: 10px;\n}\n.user-chat-topbar .topbar-bookmark .btn-close {\n  padding: 12px 20px;\n}\n\n.call-close-btn {\n  box-shadow: 0px 0px 0 6px var(--bs-card-bg);\n}\n\n.user-chat-nav .nav-btn {\n  height: 40px;\n  width: 40px;\n  line-height: 40px;\n  box-shadow: none;\n  padding: 0;\n  font-size: 22px;\n  color: var(--bs-gray-600);\n}\n@media (max-width: 575.98px) {\n  .user-chat-nav {\n    display: flex;\n    justify-content: flex-end;\n  }\n}\n\n.replymessage-block {\n  padding: 12px 20px;\n  font-size: 14px;\n  margin-bottom: 8px;\n  text-align: left;\n  border-radius: 4px;\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\n  border-left: 2px solid rgba(var(--bs-primary-rgb), 1);\n}\n.replymessage-block .conversation-name {\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-size: 15px;\n}\n\n.chat-conversation {\n  height: calc(100vh - 94px);\n}\n@media (max-width: 991.98px) {\n  .chat-conversation {\n    height: calc(100vh - 80px);\n    margin-bottom: 78px;\n  }\n}\n.chat-conversation .simplebar-content-wrapper {\n  display: flex;\n  flex-direction: column;\n}\n.chat-conversation .simplebar-content-wrapper .simplebar-content {\n  margin-top: auto;\n}\n.chat-conversation .chat-conversation-list {\n  margin-top: 90px;\n  padding-top: 10px;\n  margin-bottom: 0;\n}\n.chat-conversation .chat-conversation-list > li {\n  display: flex;\n}\n.chat-conversation li:last-of-type .conversation-list {\n  margin-bottom: 0;\n}\n.chat-conversation .chat-list.left .check-message-icon {\n  display: none;\n}\n.chat-conversation .chat-list .message-box-drop {\n  visibility: hidden;\n}\n.chat-conversation .chat-list:hover .message-box-drop {\n  visibility: visible;\n}\n.chat-conversation .chat-avatar {\n  margin: 0 16px 0 0;\n}\n.chat-conversation .chat-avatar img {\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n}\n.chat-conversation .chat-day-title {\n  position: relative;\n  text-align: center;\n  margin-bottom: 24px;\n  margin-top: 12px;\n  width: 100%;\n}\n.chat-conversation .chat-day-title .title {\n  background-color: #fff;\n  position: relative;\n  font-size: 13px;\n  z-index: 1;\n  padding: 6px 12px;\n  border-radius: 5px;\n}\n.chat-conversation .chat-day-title:before {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 1px;\n  left: 0;\n  right: 0;\n  background-color: rgba(var(--bs-primary-rgb), 0.2);\n  top: 10px;\n}\n.chat-conversation .chat-day-title .badge {\n  font-size: 12px;\n}\n.chat-conversation .conversation-list {\n  margin-bottom: 24px;\n  display: inline-flex;\n  position: relative;\n  align-items: flex-end;\n  max-width: 80%;\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .conversation-list {\n    max-width: 90%;\n  }\n}\n.chat-conversation .conversation-list .ctext-wrap {\n  display: flex;\n  margin-bottom: 10px;\n}\n.chat-conversation .conversation-list .ctext-content {\n  word-wrap: break-word;\n  word-break: break-word;\n  color: var(--bs-chat-text-color);\n}\n.chat-conversation .conversation-list .ctext-wrap-content {\n  padding: 12px 20px;\n  background-color: var(--bs-chat-primary-bg);\n  position: relative;\n  border-radius: 3px;\n  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .attached-file-avatar {\n    display: none;\n  }\n  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .dropdown .dropdown-toggle {\n    display: block;\n  }\n}\n.chat-conversation .conversation-list .conversation-name {\n  font-weight: 500;\n  font-size: 14px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  gap: 8px;\n}\n.chat-conversation .conversation-list .dropdown .dropdown-toggle {\n  font-size: 18px;\n  padding: 4px;\n  color: #797c8c;\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n    display: none;\n  }\n}\n.chat-conversation .conversation-list .chat-time {\n  font-size: 12px;\n  margin-top: 4px;\n  text-align: right;\n}\n.chat-conversation .conversation-list .message-img {\n  border-radius: 0.2rem;\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n.chat-conversation .conversation-list .message-img .message-img-list {\n  position: relative;\n}\n.chat-conversation .conversation-list .message-img img {\n  max-width: 150px;\n}\n.chat-conversation .conversation-list .message-img .message-img-link {\n  position: absolute;\n  right: 10px;\n  left: auto;\n  bottom: 10px;\n}\n.chat-conversation .conversation-list .message-img .message-img-link li > a {\n  font-size: 18px;\n  color: #fff;\n  display: inline-block;\n  line-height: 20px;\n  width: 26px;\n  height: 24px;\n  border-radius: 3px;\n  background-color: rgba(52, 58, 64, 0.7);\n  text-align: center;\n}\n.chat-conversation .right {\n  justify-content: flex-end;\n}\n.chat-conversation .right .chat-avatar {\n  order: 3;\n  margin-right: 0px;\n  margin-left: 16px;\n}\n.chat-conversation .right .chat-time {\n  text-align: left;\n  color: #797c8c;\n}\n.chat-conversation .right .conversation-list {\n  text-align: right;\n}\n.chat-conversation .right .conversation-list .ctext-wrap {\n  justify-content: flex-end;\n}\n.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content {\n  order: 2;\n  background-color: rgba(var(--bs-primary-rgb), 0.23);\n  text-align: right;\n  box-shadow: none;\n}\n.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block {\n  background-color: rgba(255, 255, 255, 0.5);\n  border-color: rgba(var(--bs-primary-rgb), 1);\n  color: #495057;\n}\n.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block .conversation-name {\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.chat-conversation .right .conversation-list .conversation-name {\n  justify-content: flex-end;\n}\n.chat-conversation .right .conversation-list .conversation-name .check-message-icon {\n  order: 1;\n}\n.chat-conversation .right .conversation-list .conversation-name .time {\n  order: 2;\n}\n.chat-conversation .right .conversation-list .conversation-name .name {\n  order: 3;\n}\n.chat-conversation .right .conversation-list .dropdown {\n  order: 1;\n}\n.chat-conversation .right .dot {\n  background-color: #343a40;\n}\n\n.videocallModal .modal-content {\n  min-height: 450px;\n  overflow: hidden;\n}\n@media (max-width: 575.98px) {\n  .videocallModal .modal-content {\n    min-height: 350px;\n  }\n}\n\n.videocallModal-bg {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-chat-remove {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: #fff;\n  border-radius: 3px;\n  line-height: 1;\n}\n\n.chat-input-section {\n  background-color: var(--bs-footer-bg);\n  border-top: 1px solid var(--bs-border-color);\n  backdrop-filter: blur(7px);\n  position: relative;\n  z-index: 1;\n}\n@media (max-width: 991.98px) {\n  .chat-input-section {\n    position: fixed;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 1;\n  }\n}\n.chat-input-section .chat-input-collapse {\n  position: absolute;\n  bottom: 92px;\n  left: 0;\n  right: 0;\n  border-top: 1px solid var(--bs-border-color);\n  overflow: hidden;\n}\n@media (max-width: 991.98px) {\n  .chat-input-section .chat-input-collapse {\n    bottom: 74px;\n  }\n}\n.chat-input-section .chat-input-feedback {\n  display: none;\n  position: absolute;\n  top: -18px;\n  left: 16px;\n  font-size: 12px;\n  color: #EF476F;\n}\n.chat-input-section .show {\n  display: block;\n}\n.chat-input-section .replyCollapse {\n  z-index: 1;\n}\n\n.file_Upload {\n  background-color: var(--bs-card-bg);\n  border-top: 1px solid var(--bs-border-color);\n  padding: 16px 24px;\n}\n.file_Upload .card {\n  margin-bottom: 0;\n  border-color: rgba(var(--bs-primary-rgb), 1) !important;\n}\n\n.replyCard, .file_Upload {\n  position: absolute;\n  left: 0;\n  right: 0;\n  border-top: 1px solid var(--bs-border-color);\n  overflow: hidden;\n  opacity: 0;\n  bottom: 0;\n  transition: all 0.4s;\n}\n@media (max-width: 991.98px) {\n  .replyCard, .file_Upload {\n    bottom: -12px;\n  }\n}\n.replyCard.show, .file_Upload.show {\n  transform: translateY(-92px);\n  opacity: 1;\n}\n@media (max-width: 991.98px) {\n  .replyCard.show, .file_Upload.show {\n    transform: translateY(-86px);\n  }\n}\n\n.contact-modal-list .contact-list li {\n  margin: 2px 0px;\n}\n.contact-modal-list .contact-list li.selected {\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\n}\n\n.chat-input-links {\n  display: flex;\n}\n.chat-input-links .links-list-item > .btn {\n  box-shadow: none;\n  padding: 0;\n  font-size: 22px;\n  width: 43px;\n  height: 43px;\n}\n.chat-input-links .links-list-item > .btn.btn-link {\n  color: var(--bs-gray-600);\n}\n\n.animate-typing .dot {\n  display: inline-block;\n  width: 4px;\n  height: 4px;\n  border-radius: 50%;\n  margin-right: -1px;\n  background: #343a40;\n  animation: wave 1.3s linear infinite;\n  opacity: 0.6;\n}\n.animate-typing .dot:nth-child(2) {\n  animation-delay: -1.1s;\n}\n.animate-typing .dot:nth-child(3) {\n  animation-delay: -0.9s;\n}\n\n@keyframes wave {\n  0%, 60%, 100% {\n    transform: initial;\n  }\n  30% {\n    transform: translateY(-5px);\n  }\n}\n@-moz-document url-prefix() {\n  .user-chat-topbar, .chat-input-section {\n    background-color: #f2f2f2 !important;\n  }\n}\n.user-profile-sidebar {\n  height: 100vh;\n  background-color: var(--bs-card-bg);\n  display: none;\n  min-width: 380px;\n  max-width: 380px;\n}\n@media (min-width: 992px) {\n  .user-profile-sidebar {\n    border-left: 4px solid var(--bs-border-color);\n  }\n}\n@media (max-width: 1199.98px) {\n  .user-profile-sidebar {\n    position: fixed;\n    right: 0;\n    top: 0;\n    z-index: 99;\n  }\n}\n@media (max-width: 575.98px) {\n  .user-profile-sidebar {\n    min-width: 100%;\n  }\n}\n\n.user-profile-img {\n  position: relative;\n}\n.user-profile-img .overlay-content {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 10%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.5) 100%);\n  display: flex;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  flex-direction: column;\n}\n.user-profile-img .user-name {\n  font-size: 16px;\n  color: #fff;\n}\n.user-profile-img .profile-img {\n  width: 100%;\n  height: 250px;\n  object-fit: cover;\n}\n@media (max-width: 991px) {\n  .user-profile-img .profile-img {\n    height: 160px;\n  }\n}\n.user-profile-img .profile-foreground-img-file-input {\n  display: none;\n}\n.user-profile-img .profile-photo-edit {\n  cursor: pointer;\n}\n\n.user-profile-image {\n  object-fit: cover;\n}\n\n.user-profile-desc {\n  height: calc(100vh - 285px);\n}\n@media (max-width: 991.98px) {\n  .user-profile-desc {\n    height: calc(100vh - 194px);\n  }\n}\n\n.profile-desc {\n  height: calc(100vh - 285px);\n}\n@media (max-width: 991.98px) {\n  .profile-desc {\n    height: calc(100vh - 330px);\n  }\n}\n\n.profile-media-img {\n  display: flex;\n  gap: 8px;\n}\n.profile-media-img .media-img-list {\n  position: relative;\n}\n.profile-media-img .media-img-list a {\n  display: block;\n  position: relative;\n  border-radius: 4px;\n  overflow: hidden;\n}\n.profile-media-img .media-img-list a img {\n  width: 76px;\n  height: 76px;\n  object-fit: cover;\n}\n.profile-media-img .media-img-list a .bg-overlay {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #fff;\n}\n.profile-media-img .media-img-list .image-remove {\n  position: absolute;\n  top: 0;\n  right: 0;\n  color: #fff;\n  background: rgba(52, 58, 64, 0.7);\n  width: 18px;\n  height: 18px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border-radius: 2px;\n  margin: 4px;\n}\n\n.favourite-btn.active .bx-heart {\n  color: #EF476F;\n}\n.favourite-btn.active .bx-heart:before {\n  content: \"\\ed36\";\n}\n\n.edit-input.form-control[readonly] {\n  padding: 0;\n  font-weight: 500;\n}\n.edit-input.form-control[readonly]:focus {\n  border-color: transparent;\n}\n\n.user-setting {\n  height: calc(100vh - 288px);\n}\n@media (max-width: 991.98px) {\n  .user-setting {\n    height: calc(100vh - 320px);\n  }\n}\n\n.auth-logo .logo {\n  margin: 0px auto;\n}\n.auth-logo .logo-dark {\n  display: block;\n}\n.auth-logo .logo-light {\n  display: none;\n}\n\n.auth-bg {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  min-height: 100vh;\n  background-size: cover;\n  background-position: center;\n}\n\n.auth-logo-section {\n  display: flex;\n  flex-direction: column;\n}\n@media (min-width: 992px) {\n  .auth-logo-section {\n    height: 100vh;\n  }\n}\n@media (max-width: 991.98px) {\n  .auth-logo-section {\n    text-align: center;\n  }\n}\n\n.authentication-page-content {\n  height: calc(100% - 48px);\n  background-color: #fff;\n  border-radius: 16px;\n  margin: 24px;\n}\n\n.auth-img {\n  position: relative;\n}\n@media (min-width: 992px) and (max-width: 1549.98px) {\n  .auth-img {\n    max-width: 220%;\n  }\n}\n@media (min-width: 1550px) {\n  .auth-img {\n    max-width: 200%;\n  }\n}\n@media (max-width: 991.98px) {\n  .auth-img {\n    display: none;\n  }\n}\n\n.auth-pass-inputgroup input[type=text] + .btn .ri-eye-fill:before {\n  content: \"\\ec80\";\n}\n\n.signin-other-title {\n  position: relative;\n}\n.signin-other-title:after {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 1px;\n  left: 0;\n  right: 0;\n  background-color: var(--bs-border-color);\n  top: 10px;\n}\n.signin-other-title .title {\n  display: inline-block;\n  position: relative;\n  z-index: 9;\n  background-color: var(--bs-card-bg);\n  padding: 2px 16px;\n}", "//\n// avatar.scss\n//\n\n.avatar-xs {\n  height: 1.8rem;\n  width: 1.8rem;\n}\n\n.avatar-sm {\n  height: 2.4rem;\n  width: 2.4rem;\n}\n\n.avatar-md {\n  height: 4rem;\n  width: 4rem;\n}\n\n.avatar-lg {\n  height: 5rem;\n  width: 5rem;\n}\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: $white;\n  display: flex;\n  font-weight: $font-weight-medium;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n// avatar group\n.avatar-group {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 8px;\n  .avatar-group-item {\n    margin-left: -8px;\n    border: 2px solid $card-bg;\n    border-radius: 50%;\n    transition: all 0.2s;\n    &:hover{\n      position: relative;\n      transform: translateY(-2px);\n    }\n  }\n}\n", "//\n// _helper.scss\n//\n\n.font-size-10 {\n    font-size: 10px !important;\n}\n\n.font-size-11 {\n    font-size: 11px !important;\n}\n\n.font-size-12 {\n    font-size: 12px !important;\n}\n\n.font-size-13 {\n    font-size: 13px !important;\n}\n\n.font-size-14 {\n    font-size: 14px !important;\n}\n\n.font-size-15 {\n    font-size: 15px !important;\n}\n\n.font-size-16 {\n    font-size: 16px !important;\n}\n\n.font-size-17 {\n    font-size: 17px !important;\n}\n\n.font-size-18 {\n    font-size: 18px !important;\n}\n\n.font-size-20 {\n    font-size: 20px !important;\n}\n\n.font-size-22 {\n    font-size: 22px !important;\n}\n\n.font-size-24 {\n    font-size: 24px !important;\n}\n\n// Font weight help class\n\n.fw-medium {\n    font-weight: $font-weight-medium;\n}\n\n.fw-semibold {\n    font-weight: $font-weight-semibold;\n}\n\n// Social\n\n.social-list-item {\n    height: 2rem;\n    width: 2rem;\n    line-height: calc(2rem - 4px);\n    display: block;\n    border: 2px solid $gray-500;\n    border-radius: 50%;\n    color: $gray-500;\n    text-align: center;\n    transition: all 0.4s;\n    &:hover {\n        color: $gray-600;\n        background-color: $gray-200;\n    }\n}\n\n.w-xs {\n    min-width: 80px;\n}\n.w-sm {\n    min-width: 95px;\n}\n.w-md {\n    min-width: 110px;\n}\n.w-lg {\n    min-width: 140px;\n}\n\n.w-xl {\n    min-width: 160px;\n}\n\n.bg-overlay {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    top: 0;\n    background-color: rgba($dark, 0.7);\n}\n\n.border-primary {\n    border-color: rgba(var(--bs-primary-rgb), 1) !important;\n}\n\n// border-color\n.border-dark {\n    border-color: var(--#{$variable-prefix}dark) !important;\n}\n.border-light {\n    border-color: var(--#{$variable-prefix}light) !important;\n}\n\n[data-layout-mode=\"dark\"] {\n    .text-body {\n        color: var(--#{$variable-prefix}gray-500) !important;\n    }\n    .btn-close {\n        filter: invert(1) grayscale(100%) brightness(200%);\n    }\n}\n", "// \r\n// _progress.scss\r\n// \r\n\r\n.loader-line{\r\n  height: 28px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n\r\n  .line:nth-last-child(1) {\r\n      animation: loadingLine 1.25s 1s infinite;\r\n  }\r\n  .line:nth-last-child(2) {\r\n      animation: loadingLine 1.25s 0.75s infinite;\r\n  }\r\n  .line:nth-last-child(3) {\r\n      animation: loadingLine 1.25s 0.5s infinite;\r\n  }\r\n  .line:nth-last-child(4) {\r\n      animation: loadingLine 1.25s 0.25s infinite;\r\n  }\r\n  .line:nth-last-child(5) {\r\n      animation: loadingLine 1.25s 0s infinite;\r\n  }\r\n}\r\n\r\n.line {\r\n    display: inline-block;\r\n    width: 4px;\r\n    height: 10px;\r\n    border-radius: 14px;\r\n    background-color: rgba($text-muted, 0.7);\r\n}\r\n\r\n@keyframes loadingLine {\r\n    0% {\r\n      height: 10px;\r\n    }\r\n    50% {\r\n      height: 28px;\r\n    }\r\n    100% {\r\n      height: 10px;\r\n    }\r\n}\r\n\r\n", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .card {\r\n        + .card {\r\n            margin-top: 0.5rem;\r\n        }\r\n    }\r\n\r\n    a {\r\n        i.accor-plus-icon {\r\n            font-size: 16px;\r\n        }\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0142\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.accordion-button {\r\n    &:after {\r\n        margin-left: auto/*rtl:0*/;\r\n        margin-right:0/*rtl:auto*/;\r\n    }\r\n}\r\n\r\n// theme dark\r\n\r\n[data-layout-mode=\"dark\"]{\r\n    .accordion-button {\r\n        &:after{\r\n            background-image: escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$gray-500}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\"));\r\n        }\r\n    }\r\n}", "// \r\n// _modal.scss\r\n// \r\n\r\n.modal-header-colored {\r\n    .modal-header {\r\n        background-color:  rgba(var(--bs-primary-rgb), 1);\r\n        margin: 0 -1px -1px -1px;\r\n    }\r\n}", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n[type=\"tel\"],\r\n[type=\"url\"],\r\n[type=\"email\"],\r\n[type=\"number\"] {\r\n &::placeholder{\r\n   text-align: left; /*rtl: right*/\r\n }\r\n}\r\n\r\n\r\n.form-check,\r\n.form-check-input,\r\n.form-check-label {\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-check-input:checked {\r\n  background-color: rgba(var(--bs-primary-rgb), 1);\r\n  border-color: rgba(var(--bs-primary-rgb), 1);\r\n}\r\n", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 7px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "//\r\n// emoji-picker.scss\r\n//\r\n\r\n.fg-emoji-picker{\r\n    background-color: $card-bg !important;\r\n    width: 250px !important;\r\n    box-shadow: $box-shadow !important;\r\n    top: auto !important;\r\n    bottom: 70px;\r\n    * {\r\n        font-family: $font-family-base !important;\r\n        color: $body-color !important;\r\n    }\r\n    @media (max-width:991.98px) {\r\n        left: 14px !important;\r\n        top: auto !important;\r\n        bottom: 60px;\r\n    }\r\n\r\n    .fg-emoji-picker-container-title{\r\n        color: var(--#{$variable-prefix}dark) !important;\r\n    }\r\n\r\n    .fg-emoji-picker-search{\r\n        height: 40px !important;\r\n        input{\r\n            background-color: $input-bg !important;\r\n            color: $input-color !important;\r\n            border: 1px solid $border-color;\r\n            padding: $input-btn-padding-y $input-btn-padding-x !important;\r\n            font-size: $font-size-base !important;\r\n            &::placeholder{\r\n                color: $input-placeholder-color !important;\r\n            }\r\n        }\r\n\r\n        svg{\r\n            fill: var(--#{$variable-prefix}body-color) !important;\r\n            right: 11px;\r\n            top: 12px;\r\n        }\r\n    }\r\n\r\n    .fg-emoji-picker-categories{\r\n        background-color: var(--#{$variable-prefix}light) !important;\r\n        li.active{\r\n            background-color: rgba(var(--bs-primary-rgb), 0.2);\r\n        }\r\n        a{\r\n            &:hover{\r\n                background-color: rgba(var(--bs-primary-rgb), 0.2);\r\n            }\r\n        }\r\n        \r\n    }\r\n}\r\n\r\n.fg-emoji-picker-grid > li:hover{\r\n    background-color: rgba(var(--bs-primary-rgb), 0.2) !important;\r\n}\r\n\r\na.fg-emoji-picker-close-button{\r\n    background-color: var(--#{$variable-prefix}light) !important;\r\n}\r\n\r\n\r\n.fg-emoji-picker-categories svg{\r\n    fill: var(--#{$variable-prefix}body-color) !important;\r\n}\r\n\r\n\r\n.fg-emoji-picker-grid > li{\r\n    background-color: $card-bg !important;\r\n}", "\r\n.ml-44{\r\n    margin-left: 44px;\r\n}", "//\r\n// Sidemenu\r\n//\r\n\r\n.side-menu {\r\n    min-width: 75px;\r\n    max-width: 75px;\r\n    height: 100vh;\r\n    min-height: 570px;\r\n    background-color: $sidebar-bg;\r\n    display: flex;\r\n    z-index: 9;\r\n    border-right: 1px solid $sidebar-bg;\r\n    padding: 0 8px;\r\n\r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        bottom: 0;\r\n        height: 60px;\r\n        min-width: 100%;\r\n        min-height: auto;\r\n        border-top: 1px solid $sidebar-bg;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        text-align: center;\r\n\r\n        svg {\r\n            fill: rgba(var(--bs-primary-rgb), 1);\r\n        }\r\n\r\n        @media (max-width: 991.98px) {\r\n            display: none;\r\n        }\r\n\r\n        .logo {\r\n            line-height: 70px;\r\n        }\r\n\r\n        .logo-dark {\r\n            display: $display-block;\r\n        }\r\n\r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n}\r\n\r\n.sidemenu-navigation {\r\n    height: 100%;\r\n    @media (max-width: 991.98px) {\r\n        width: 100%;\r\n\r\n        .tooltip {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n.side-menu-nav {\r\n    height: 100%;\r\n    flex-direction: column;\r\n    @media (max-width: 991.98px) {\r\n        justify-content: space-between !important;\r\n        flex-direction: row;\r\n    }\r\n    .nav-item {\r\n        margin: 7px 0;\r\n        display: block;\r\n        width: 100%;\r\n\r\n        @media (max-width: 991.98px) {\r\n            flex-basis: 0;\r\n            flex-grow: 1;\r\n            margin: 5px 0;\r\n        }\r\n        .nav-link {\r\n            text-align: center;\r\n            font-size: 28px;\r\n            color: $sidebar-menu-item-color;\r\n            width: 100%;\r\n            height: 56px;\r\n            line-height: 56px;\r\n            border-radius: 0;\r\n            padding: 0;\r\n            position: relative;\r\n\r\n            i {\r\n                vertical-align: middle;\r\n            }\r\n\r\n            &::before {\r\n                position: absolute;\r\n                content: \"\";\r\n                height: 20px;\r\n                width: 2px;\r\n                right: -8px;\r\n                top: 18px;\r\n                @media (max-width: 991.98px) {\r\n                    width: 20px;\r\n                    height: 2px;\r\n                    right: auto;\r\n                    top: -5px;\r\n                    left: 50%;\r\n                    transform: translateX(-50%);\r\n                }\r\n            }\r\n\r\n            @media (max-width: 991.98px) {\r\n                font-size: 20px;\r\n                width: 48px;\r\n                height: 48px;\r\n                line-height: 48px;\r\n                margin: 0px auto;\r\n            }\r\n\r\n            &.active {\r\n                background-color: transparent;\r\n                color: rgba(var(--bs-primary-rgb), 1);\r\n\r\n                &::before {\r\n                    background-color: rgba(var(--bs-primary-rgb), 1);\r\n                }\r\n            }\r\n        }\r\n\r\n        &.show > .nav-link {\r\n            color: rgba(var(--bs-primary-rgb), 1);\r\n        }\r\n    }\r\n\r\n    .profile-user {\r\n        height: 36px;\r\n        width: 36px;\r\n        background-color: var(--#{$variable-prefix}gray-300);\r\n        padding: 3px;\r\n    }\r\n}\r\n\r\n.light-mode {\r\n    display: none;\r\n}\r\n\r\nbody[data-layout-mode=\"dark\"] {\r\n    .light-mode {\r\n        display: inline-block;\r\n    }\r\n\r\n    .dark-mode {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout-mode=\"dark\"] {\r\n    .side-menu-nav {\r\n        .nav-item {\r\n            .nav-link {\r\n                &.light-dark {\r\n                    .bx-moon {\r\n                        &:before {\r\n                            content: \"\\eb90\";\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .light-mode {\r\n        display: inline-block;\r\n    }\r\n\r\n    .dark-mode {\r\n        display: none;\r\n    }\r\n}\r\n", "// \r\n// chat-leftsidebar.scss\r\n//\r\n\r\n\r\n.chat-leftsidebar {\r\n    height: calc(100vh - 60px);;\r\n    position: relative;\r\n    background-color: $sidebar-sub-bg;\r\n    box-shadow: $box-shadow;\r\n\r\n    @media (min-width: 992px) {\r\n        min-width: 300px;\r\n        max-width: 300px;\r\n        height: 100vh;\r\n    }\r\n\r\n    .user-status-box {\r\n        background-color: $light;\r\n        padding: 8px;\r\n        border-radius: 8px;\r\n        text-align: center;\r\n        margin-top: 16px;\r\n        display: block;\r\n\r\n        .chat-user-img {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.chat-search-box {\r\n    .form-control {\r\n        border: 0;\r\n    }\r\n\r\n    .search-icon-btn {\r\n        font-size: 16px;\r\n        position: absolute;\r\n        right: 13px;\r\n        top: 0;\r\n    }\r\n}\r\n\r\n.chat-room-list {\r\n    max-height: calc(100vh - 130px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 190px);\r\n    }\r\n}\r\n\r\n.chat-group-list {\r\n    height: calc(100vh - 140px);\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 198px);\r\n    }\r\n}\r\n\r\n\r\n.chat-list {\r\n    margin: 0;\r\n\r\n    li {\r\n        &.active {\r\n            a {\r\n                background-color: rgba(var(--bs-primary-rgb), 1);\r\n                color: $white;\r\n\r\n                span.avatar-title {\r\n                    color: $white !important;\r\n                    &.bg-light{\r\n                        background-color: rgba($light, 0.25) !important;\r\n                    }\r\n                }\r\n\r\n                .badge {\r\n                    background-color: rgba($light, 0.25) !important;\r\n                    color: $white !important;\r\n                }\r\n\r\n                .bg-primary {\r\n                    background-color: rgba($white, 0.25) !important;\r\n                }\r\n            }\r\n        }\r\n\r\n        a {\r\n            display: block;\r\n            padding: 5px 24px;\r\n            color: var(--#{$variable-prefix}gray-700);\r\n            transition: all 0.4s;\r\n            font-size: 14px;\r\n        }\r\n\r\n        .chat-user-message {\r\n            font-size: 14px;\r\n        }\r\n\r\n        .unread-msg-user {\r\n            font-weight: 600;\r\n        }\r\n\r\n        &.typing {\r\n            .chat-user-message {\r\n                color: rgba(var(--bs-primary-rgb), 1);\r\n                font-weight: $font-weight-medium;\r\n\r\n                .dot {\r\n                    background-color: rgba(var(--bs-primary-rgb), 1);\r\n                }\r\n            }\r\n        }\r\n\r\n        .unread-message {\r\n            position: absolute;\r\n            display: inline-block;  \r\n            right: 24px/*rtl:auto*/;\r\n            left: auto/*rtl:0*/;\r\n            top: 33px;\r\n\r\n            .badge {\r\n                line-height: 16px;\r\n                font-weight: $font-weight-semibold;\r\n                font-size: 10px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.chat-user-img {\r\n    position: relative;\r\n\r\n    .user-status {\r\n        width: 10px;\r\n        height: 10px;\r\n        background-color: $gray-500;\r\n        border-radius: 50%;\r\n        border: 2px solid var(--#{$variable-prefix}card-bg);\r\n        position: absolute;\r\n        right: 0;\r\n        left: auto;\r\n        bottom: 0;\r\n    }\r\n\r\n    &.online {\r\n        .user-status {\r\n            background-color: $success;\r\n        }\r\n    }\r\n\r\n    &.away {\r\n        .user-status {\r\n            background-color: $warning;\r\n        }\r\n    }\r\n}\r\n\r\n// contact list\r\n\r\n.contact-list {\r\n    li {\r\n        cursor: pointer;\r\n        padding: 8px 24px;\r\n    }\r\n}\r\n\r\n.contact-list-title{\r\n    padding: 6px 24px;\r\n    color: rgba(var(--bs-primary-rgb), 1);\r\n    font-weight: $font-weight-medium;\r\n    position: relative;\r\n    font-size: 12px;\r\n    &:after{\r\n        content: \"\";\r\n        height: 1px;\r\n        position: absolute;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        left: 50px;\r\n        right: 0;\r\n        background-color: $border-color;\r\n    }\r\n}\r\n\r\n// Calls\r\n\r\n.chat-call-list, .chat-bookmark-list{\r\n    max-height: calc(100vh - 68px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 125px);\r\n    }\r\n    li{\r\n        position: relative;\r\n        padding: 10px 24px;\r\n        border-bottom: 1px solid $border-color;\r\n\r\n        &:last-child{\r\n            border-bottom: 0;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// settings\r\n\r\n\r\n.profile-user {\r\n    position: relative;\r\n    display: inline-block;\r\n\r\n    .profile-photo-edit {\r\n        position: absolute;\r\n        right: 0/*rtl:auto*/;\r\n        left: auto/*rtl:0*/;\r\n        bottom: 0;\r\n        cursor: pointer;\r\n    }\r\n\r\n    .user-profile-img{\r\n        object-fit: cover;\r\n    }\r\n\r\n    .profile-img-file-input{\r\n        display: none;\r\n    }\r\n}\r\n\r\n.theme-btn-list{\r\n    &.theme-color-list{\r\n        .form-check .form-check-input:checked+.form-check-label::before{\r\n            color: $white;\r\n        }\r\n    }\r\n    .form-check{\r\n        padding: 0;\r\n        .form-check-input{\r\n            display: none;\r\n        }\r\n\r\n        .form-check-label{\r\n            position: relative;\r\n        }\r\n\r\n        .form-check-input:checked + .form-check-label {\r\n            &::before{\r\n                content: \"\\e9a4\";\r\n                font-family: boxicons!important;\r\n                position: absolute;\r\n                top: 50%;\r\n                left: 50%;\r\n                transform: translateX(-50%) translateY(-50%);\r\n                color: rgba(var(--bs-primary-rgb), 1);\r\n                font-size: 16px;\r\n                z-index: 1;\r\n            }\r\n\r\n            &.light-background{\r\n                &::before{\r\n                    color: rgba(var(--bs-primary-rgb), 1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.theme-btn-list-img{\r\n        .theme-btn{\r\n            background-color: transparent !important;\r\n        }\r\n    }\r\n    \r\n}\r\n\r\n.theme-btn{\r\n    cursor: pointer;\r\n    border: 1px solid var(--#{$variable-prefix}gray-400);\r\n}", "// \r\n// user chat.scss\r\n//\r\n\r\n\r\n// user chat\r\n\r\n.user-chat {\r\n    background: url(\"../images/bg-pattern/pattern-05.png\");\r\n    transition: all 0.4s;\r\n    position: relative;\r\n    background-color: var(--#{$variable-prefix}body-bg);\r\n\r\n    .user-chat-overlay{\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        bottom: 0;\r\n        right: 0;\r\n        background-color: transparent;\r\n        opacity: 0.1;\r\n    }\r\n\r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        left: 0;\r\n        top: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        visibility: hidden;\r\n        transform: translateX(100%);\r\n        z-index: 99;\r\n\r\n        &.user-chat-show{\r\n            visibility: visible;\r\n            transform: translateX(0);\r\n        }\r\n    }\r\n\r\n    .chat-content{\r\n        position: relative;\r\n    }\r\n\r\n    &.user-chat-show{\r\n        .chat-welcome-section{\r\n            display: none;\r\n        }\r\n        .chat-content{\r\n            @media (min-width: 992px) {\r\n                display: flex !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.chat-welcome-section{\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100vh;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: relative;\r\n    @media (max-width: 991.98px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.copyclipboard-alert{\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 50%;\r\n    transform:translateX(-50%);\r\n}\r\n\r\n.user-chat-topbar{\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    z-index: 1;\r\n    background-color: rgba($white,0.05);\r\n    border-bottom: 1px solid $border-color;\r\n    backdrop-filter: blur(7px);\r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        background-color: rgba($white,0.8);\r\n    }\r\n\r\n    .topbar-bookmark{\r\n        position: absolute;\r\n        bottom: -51px;\r\n        left: 0;\r\n        right: 0;\r\n        border-radius: 0;\r\n\r\n        .bookmark-tabs{\r\n            .tab-links{\r\n                color: darken($warning, 30%);\r\n                font-size: 14px;\r\n                padding: 1px 16px;\r\n                border-right: 1px solid rgba($warning, 0.7);\r\n                white-space: nowrap;\r\n                &:first-child{\r\n                    padding-left: 4px;\r\n                }\r\n            }\r\n            .tab-list-link{\r\n                display: flex;\r\n                overflow-x: auto;\r\n\r\n                &::-webkit-scrollbar {\r\n                    -webkit-appearance: none;\r\n                }\r\n                \r\n                &::-webkit-scrollbar:vertical {\r\n                    width: 12px;\r\n                }\r\n                \r\n                &::-webkit-scrollbar:horizontal {\r\n                    height: 5px;\r\n                }\r\n                \r\n                &::-webkit-scrollbar-thumb {\r\n                    background-color: rgba($dark, .1);\r\n                    border-radius: 10px;\r\n                    border: 2px solid transparent;\r\n                }\r\n                \r\n                &::-webkit-scrollbar-track {\r\n                    border-radius: 10px;  \r\n                }\r\n            }\r\n        }\r\n\r\n        .btn-close{\r\n            padding: 12px 20px;\r\n        }\r\n    }\r\n}\r\n\r\n.call-close-btn{\r\n box-shadow: 0px 0px 0 6px $card-bg;   \r\n}\r\n\r\n.user-chat-nav {\r\n    .nav-btn {\r\n        height: 40px;\r\n        width: 40px;\r\n        line-height: 40px;\r\n        box-shadow: none;\r\n        padding: 0;\r\n        font-size: 22px;\r\n        color: var(--#{$variable-prefix}gray-600);\r\n    }\r\n    @media (max-width:575.98px) {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n    }\r\n}\r\n\r\n.replymessage-block{\r\n    padding: 12px 20px;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n    text-align: left;\r\n    border-radius: 4px;\r\n    background-color: rgba(var(--bs-primary-rgb), 0.1);\r\n    border-left: 2px solid rgba(var(--bs-primary-rgb), 1);\r\n\r\n    .conversation-name{\r\n        color: rgba(var(--bs-primary-rgb), 1);\r\n        font-size: 15px;\r\n    }\r\n}\r\n\r\n.chat-conversation {\r\n    height: calc(100vh - 94px);\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 80px);\r\n        margin-bottom: 78px;\r\n    }\r\n\r\n    .simplebar-content-wrapper{\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .simplebar-content{\r\n            margin-top: auto;\r\n        }\r\n    }\r\n\r\n    .chat-conversation-list{\r\n        margin-top: 90px;\r\n        padding-top: 10px;\r\n        margin-bottom: 0;\r\n        > li{\r\n            display: flex;\r\n        }\r\n    }\r\n\r\n    li {\r\n        &:last-of-type {\r\n            .conversation-list {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    .chat-list{\r\n\r\n        &.left{\r\n            .check-message-icon{\r\n                display: none;\r\n            }\r\n        }\r\n        .message-box-drop {\r\n            visibility: hidden;\r\n        }\r\n\r\n        &:hover {\r\n            .message-box-drop {\r\n                visibility: visible;\r\n            }\r\n        }\r\n    }\r\n\r\n    .chat-avatar {\r\n        margin: 0 16px 0 0/*rtl:0 0 0 16px*/;\r\n        \r\n        img {\r\n            width: 28px;\r\n            height: 28px;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    .chat-day-title {\r\n        position: relative;\r\n        text-align: center;\r\n        margin-bottom: 24px;\r\n        margin-top: 12px;\r\n        width: 100%;\r\n\r\n        .title {\r\n            background-color: $white;\r\n            position: relative;\r\n            font-size: 13px;\r\n            z-index: 1;\r\n            padding: 6px 12px;\r\n            border-radius: 5px;\r\n        }\r\n\r\n        &:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 1px;\r\n            left: 0;\r\n            right: 0;\r\n            background-color: rgba(var(--bs-primary-rgb), 0.2);\r\n            top: 10px;\r\n        }\r\n\r\n        .badge {\r\n            font-size: 12px;\r\n        }\r\n    }\r\n\r\n    .conversation-list {\r\n        margin-bottom: 24px;\r\n        display: inline-flex;\r\n        position: relative;\r\n        align-items: flex-end;\r\n        max-width: 80%;\r\n\r\n        @media (max-width: 575.98px) {\r\n            max-width: 90%;\r\n        }\r\n\r\n        .ctext-wrap {\r\n            display: flex;\r\n            margin-bottom: 10px;\r\n        }\r\n\r\n        .ctext-content{\r\n            word-wrap: break-word;\r\n            word-break: break-word;\r\n            color: $chat-text-color;\r\n\r\n            \r\n        }\r\n\r\n        .ctext-wrap-content {\r\n            padding: 12px 20px;\r\n            background-color: $chat-primary-bg;\r\n            position: relative;\r\n            border-radius: 3px;\r\n            box-shadow: $box-shadow;\r\n\r\n            .attached-file{\r\n                @media (max-width: 575.98px) {\r\n                    .attached-file-avatar{\r\n                        display: none;\r\n                    }\r\n\r\n                    .dropdown .dropdown-toggle{\r\n                        display: block;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .conversation-name {\r\n            font-weight: $font-weight-medium;\r\n            font-size: 14px;\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            align-items: center;\r\n            gap: 8px;\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-toggle {\r\n                font-size: 18px;\r\n                padding: 4px;\r\n                color: $gray-600;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        .chat-time {\r\n            font-size: 12px;\r\n            margin-top: 4px;\r\n            text-align: right;\r\n        }\r\n\r\n        .message-img {\r\n            border-radius: .2rem;\r\n            position: relative;\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            gap: 8px;\r\n\r\n            .message-img-list {\r\n                position: relative;\r\n            }\r\n\r\n            img {\r\n                max-width: 150px;\r\n            }\r\n\r\n            .message-img-link {\r\n                position: absolute;\r\n                right: 10px/*rtl:auto*/;\r\n                left: auto/*rtl:0*/;\r\n                bottom: 10px;\r\n\r\n                li {\r\n                    >a {\r\n                        font-size: 18px;\r\n                        color: $white;\r\n                        display: inline-block;\r\n                        line-height: 20px;\r\n                        width: 26px;\r\n                        height: 24px;\r\n                        border-radius: 3px;\r\n                        background-color: rgba($dark,0.7);\r\n                        text-align: center;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .right {\r\n        justify-content: flex-end;\r\n\r\n        .chat-avatar {\r\n            order: 3;\r\n            margin-right: 0px;\r\n            margin-left: 16px;\r\n        }\r\n\r\n        .chat-time {\r\n            text-align: left;\r\n            color: $gray-600;\r\n        }\r\n\r\n        .conversation-list {\r\n            text-align: right;\r\n\r\n            .ctext-wrap {\r\n                justify-content: flex-end;\r\n\r\n                .ctext-wrap-content {\r\n                    order: 2;\r\n                    background-color: $chat-secondary-bg;\r\n                    // color: #466751;\r\n                    text-align: right;\r\n                    box-shadow: none;\r\n\r\n                    .replymessage-block{\r\n                        background-color: rgba($white,0.5);\r\n                        border-color: rgba(var(--bs-primary-rgb), 1);\r\n                        color: $body-color;\r\n        \r\n                        .conversation-name{\r\n                            color: rgba(var(--bs-primary-rgb), 1);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            .conversation-name {\r\n                justify-content: flex-end;\r\n\r\n                .check-message-icon{\r\n                    order: 1;\r\n                }\r\n\r\n                .time{\r\n                    order: 2;\r\n                }\r\n\r\n                .name{\r\n                    order: 3;\r\n                }\r\n            }\r\n\r\n            .dropdown {\r\n                order: 1;\r\n            }\r\n        }\r\n\r\n        .dot {\r\n            background-color: $dark;\r\n        }\r\n    }\r\n}\r\n\r\n.videocallModal{\r\n    .modal-content{\r\n        min-height: 450px;\r\n        overflow: hidden;\r\n        @media (max-width: 575.98px) {\r\n            min-height: 350px;\r\n        }\r\n    }\r\n}\r\n\r\n.videocallModal-bg{\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.user-chat-remove {\r\n    background-color: rgba(var(--bs-primary-rgb), 1);\r\n    color: $white;\r\n    border-radius: 3px;\r\n    line-height: 1;\r\n}\r\n\r\n.chat-input-section{\r\n    background-color: $footer-bg;\r\n    border-top: 1px solid $border-color;\r\n    backdrop-filter: blur(7px);\r\n    position: relative;\r\n    z-index: 1;\r\n    \r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        z-index: 1;\r\n    }\r\n    \r\n    .chat-input-collapse{\r\n        position: absolute;\r\n        bottom: 92px;\r\n        left: 0;\r\n        right: 0;\r\n        border-top: 1px solid $border-color;\r\n        overflow: hidden;\r\n\r\n        @media (max-width: 991.98px) {\r\n            bottom: 74px;\r\n        }\r\n    }\r\n\r\n    .chat-input-feedback {\r\n        display: none;\r\n        position: absolute;\r\n        top: -18px;\r\n        left: 16px;\r\n        font-size: 12px;\r\n        color: $danger;\r\n    }\r\n    \r\n    .show{\r\n        display: block;\r\n    }\r\n\r\n    .replyCollapse{\r\n        z-index: 1;\r\n    }\r\n}\r\n\r\n.file_Upload{\r\n        background-color:$card-bg;\r\n        border-top: 1px solid $border-color;\r\n        padding: 16px 24px;\r\n\r\n        .card{\r\n            margin-bottom: 0;\r\n            border-color: rgba(var(--bs-primary-rgb), 1) !important;\r\n        }\r\n}\r\n\r\n\r\n.replyCard, .file_Upload{\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    border-top: 1px solid $border-color;\r\n    overflow: hidden;\r\n    opacity: 0;\r\n    bottom: 0;\r\n    transition: all 0.4s;\r\n\r\n    @media (max-width: 991.98px) {\r\n        bottom: -12px;\r\n    }\r\n\r\n    &.show{\r\n        transform: translateY(-92px);\r\n        opacity: 1;\r\n\r\n        @media (max-width: 991.98px) {\r\n            transform: translateY(-86px);\r\n        }\r\n    }\r\n}\r\n\r\n.contact-modal-list{\r\n    .contact-list{\r\n        li{\r\n            margin: 2px 0px;\r\n            &.selected{\r\n                background-color: rgba(var(--bs-primary-rgb), 0.1);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.chat-input-links{\r\n    display: flex;\r\n    .links-list-item{\r\n        > .btn{\r\n            box-shadow: none;\r\n            padding: 0;\r\n            font-size: 22px;\r\n            width: 43px;\r\n            height: 43px;\r\n            &.btn-link{\r\n                color: var(--#{$variable-prefix}gray-600);\r\n            }\r\n        }\r\n        // [data-bs-toggle=\"collapse\"]{\r\n        //     &[aria-expanded=\"true\"]{\r\n        //         .bx-up-arrow-alt{\r\n        //             &:before{\r\n        //                 content: \"\\ea19\";\r\n        //             }\r\n        //         }\r\n        //     }\r\n        // }\r\n        \r\n    }\r\n}\r\n\r\n.animate-typing {\r\n\t.dot {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 4px;\r\n\t\theight: 4px;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: -1px;\r\n\t\tbackground: $dark;\r\n        animation: wave 1.3s linear infinite;\r\n        opacity: 0.6;\r\n\r\n\t\t&:nth-child(2) {\r\n\t\t\tanimation-delay: -1.1s;\r\n\t\t}\r\n\r\n\t\t&:nth-child(3) {\r\n\t\t\tanimation-delay: -0.9s;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@keyframes wave {\r\n\t0%, 60%, 100% {\r\n\t\ttransform: initial;\r\n\t}\r\n\r\n\t30% {\r\n\t\ttransform: translateY(-5px);\r\n\t}\r\n}\r\n\r\n\r\n// only for firefox browser\r\n@-moz-document url-prefix() {\r\n    .user-chat-topbar, .chat-input-section{\r\n        background-color: darken($body-bg, 3%) !important;\r\n    }\r\n}", "// \r\n//  User profile details.scss\r\n//\r\n\r\n// User profile details\r\n\r\n.user-profile-sidebar {\r\n    height: 100vh;\r\n    background-color: $card-bg;\r\n    display: none;\r\n    min-width: 380px;\r\n    max-width: 380px;\r\n\r\n    @media (min-width: 992px) {\r\n        border-left: 4px solid $border-color;\r\n    }\r\n\r\n    @media (max-width: 1199.98px) {\r\n        position: fixed;\r\n        right: 0;\r\n        top: 0;\r\n        z-index: 99;\r\n    }\r\n\r\n    @media (max-width: 575.98px) {\r\n        min-width: 100%;\r\n    }\r\n}\r\n\r\n.user-profile-img{\r\n    position: relative;\r\n    \r\n    .overlay-content{\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        bottom: 0;\r\n        right: 0;\r\n        background: linear-gradient(180deg,rgba(0,0,0,0.5) 10%, rgba(0,0,0,0) 60%,rgba(0,0,0,.5) 100%);\r\n        display: flex;\r\n        height: 100%;\r\n        color: rgba($white, 0.6);\r\n        flex-direction: column;\r\n    }\r\n\r\n    .user-name{\r\n        font-size: 16px;\r\n        color: $white;\r\n    }\r\n\r\n    .profile-img{\r\n        width: 100%;\r\n        height: 250px;\r\n        object-fit: cover;\r\n\r\n        @media (max-width:991px) {\r\n            height: 160px;\r\n        }\r\n    }\r\n\r\n    .profile-foreground-img-file-input{\r\n        display: none;\r\n    }\r\n\r\n    .profile-photo-edit{\r\n        cursor: pointer;\r\n    }\r\n}\r\n\r\n.user-profile-image{\r\n    object-fit: cover;\r\n}\r\n\r\n.user-profile-desc {\r\n    height: calc(100vh - 285px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 194px);\r\n    }\r\n}\r\n\r\n.profile-desc{\r\n    height: calc(100vh - 285px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 330px);\r\n    }\r\n}\r\n\r\n.profile-media-img{\r\n    display: flex;\r\n    gap: 8px;\r\n\r\n    .media-img-list{\r\n        position: relative;\r\n        a{\r\n            display: block;\r\n            position: relative;\r\n            border-radius: 4px;\r\n            overflow: hidden;\r\n            img{\r\n                width: 76px;\r\n                height: 76px;\r\n                object-fit: cover;\r\n            }\r\n\r\n            .bg-overlay{\r\n                display: flex;\r\n                justify-content: center;\r\n                align-items: center;\r\n                color: $white;\r\n            }\r\n        }\r\n\r\n        .image-remove{\r\n            position: absolute;\r\n            top: 0;\r\n            right: 0;\r\n            color: $white;\r\n            background: rgba($dark, 0.7);\r\n            width: 18px;\r\n            height: 18px;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            border-radius: 2px;\r\n            margin: 4px;\r\n        }\r\n    }\r\n}\r\n\r\n.favourite-btn{\r\n    &.active{\r\n        .bx-heart{\r\n            color: $danger;\r\n            &:before{\r\n                content: \"\\ed36\";\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// edit input\r\n.edit-input{\r\n    &.form-control[readonly]{\r\n        padding: 0;\r\n        font-weight: $headings-font-weight;\r\n        color: $headings-color;\r\n        &:focus{\r\n            border-color: transparent;\r\n        }\r\n    }\r\n}\r\n\r\n// setting\r\n\r\n.user-setting{\r\n    height: calc(100vh - 288px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 320px);\r\n    }\r\n}\r\n\r\n", "// \r\n// authentication.scss\r\n//\r\n\r\n.auth-logo{\r\n    .logo{\r\n        margin: 0px auto;\r\n    }\r\n\r\n    .logo-dark{\r\n        display: $display-block;\r\n    }\r\n\r\n    .logo-light{\r\n        display: $display-none;\r\n    }\r\n}\r\n\r\n.auth-bg{\r\n    background-color: rgba(var(--bs-primary-rgb), 1);\r\n    min-height: 100vh;\r\n    background-size: cover;\r\n    background-position: center;\r\n}\r\n\r\n.auth-logo-section{\r\n    display: flex;\r\n    flex-direction: column;\r\n    @media (min-width: 992px) {\r\n        height: 100vh;\r\n    }\r\n\r\n    @media (max-width: 991.98px) {\r\n        text-align: center\r\n    }\r\n}\r\n\r\n.authentication-page-content{\r\n    height: calc(100% - 48px);\r\n    background-color: $white;\r\n    border-radius: 16px;\r\n    margin: 24px;\r\n}\r\n\r\n.auth-img{\r\n    position: relative;\r\n    @media (min-width: 992px) and (max-width: 1549.98px) {\r\n        max-width: 220%;\r\n    }\r\n    @media (min-width: 1550px) {\r\n        max-width: 200%;\r\n    }\r\n    @media (max-width: 991.98px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n// auth-pass-inputgroup\r\n\r\n.auth-pass-inputgroup{\r\n    input[type=\"text\"] + .btn .ri-eye-fill{\r\n        &:before{\r\n            content: \"\\ec80\";\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// signin card title\r\n\r\n.signin-other-title{\r\n    position: relative;\r\n    &:after{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 100%;\r\n        height: 1px;\r\n        left: 0;\r\n        right: 0;\r\n        background-color: $border-color;\r\n        top: 10px;\r\n    }\r\n\r\n    .title{\r\n        display: inline-block;\r\n        position: relative;\r\n        z-index: 9;\r\n        background-color: $card-bg;\r\n        padding: 2px 16px;\r\n    }\r\n}\r\n"]}