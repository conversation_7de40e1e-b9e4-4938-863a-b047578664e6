import React from 'react';
import styles from './faqSettings.module.css';
import { useState, useContext, useEffect } from 'react';
import { AuthContext } from '../../context/AuthContext';
import DeleteModal from '../DeleteModal/DeleteModal';
import axios from 'axios';
import { BASE_URL2 } from '../../api/api';
import { CircularProgress } from '@mui/material';
import { toast } from 'react-toastify';
import { v4 as uuidv4 } from 'uuid';
const FaqSettings = () => {
    const [faqs, setFaqs] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [question, setQuestion] = useState('');
    const [loading, setLoading] = useState(false);
    const [answer, setAnswer] = useState('');
    const [category, setCategory] = useState('');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteIndex, setDeleteIndex] = useState(null);
    const [formStatus, setFormStatus] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const { currentUser } = useContext(AuthContext);
    const [tempFaqId, setTempFaqId] = useState('');

    useEffect(() => {
        const fetchFaqs = async () => {
            setLoading(true);
            const payload = {
                user_id: currentUser.parent_id,
                token: currentUser.parent_token,
                method: "retrieve_faq",

            }
            try {
                const { data } = await axios.post(`${BASE_URL2}/whatsapp_setting`, payload);
                if (data.success) {
                    setFaqs(data.data);
                } else {
                    setFaqs([]);
                }

            } catch (error) {
                console.log(error);

            }
            finally {
                setLoading(false);
            }

        }

        fetchFaqs();

    }, [currentUser])

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Create a temporary ID for optimistic rendering
        const tempId = uuidv4();
        const tempFaq = { _id: tempId, question, answer, category };

        // Optimistically update the UI
        setFaqs((prevFaqs) => [...prevFaqs, tempFaq]);
        setQuestion("");
        setAnswer("");
        setCategory("");
        setFormStatus(false);


        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "create_faq",
            faq: {
                ques: question,
                ans: answer,
                category: category,
            },
        };

        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_setting`, payload);
            if (data.success) {
                toast.success("Faq added successfully");
                setFaqs((prevFaqs) =>
                    prevFaqs.map((faq) =>
                        faq._id === tempId ? { ...faq, _id: data._id } : faq
                    )
                );
            }
        } catch (error) {
            console.log(error);
            toast.error("Something went wrong, please try again later");

            // Roll back optimistic update
            setFaqs((prevFaqs) => prevFaqs.filter((faq) => faq._id !== tempId));
        }
    };


    const confirmDelete = (id) => {
        setTempFaqId(id);
        setShowDeleteModal(true);
    };

    const handleDelete = async () => {
        // Optimistically remove the FAQ from the UI
        const originalFaqs = [...faqs]; // Save the original state for rollback
        setFaqs((prevFaqs) => prevFaqs.filter((item) => item._id !== tempFaqId));

        setTempFaqId('');
        setShowDeleteModal(false);

        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "delete_faq",
            id: tempFaqId
        }
        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_setting`, payload);
            if (data.success) {

                toast.success("Faq deleted successfully")
            } else {
                throw new Error("Failed to delete FAQ");
            }

        } catch (error) {
            console.log(error);
            toast.error("Something went wrong, please try again later");
            setFaqs(originalFaqs);

        }
    };

    const handleEdit = (faq) => {
        setEditMode(true);
        setFormStatus(true)
        setQuestion(faq.question);
        setAnswer(faq.answer);
        setCategory(faq.category);
        setTempFaqId(faq._id)

    };

    const handleUpdate = async (e) => {
        e.preventDefault();

        const originalFaqs = [...faqs];
        setFaqs((prevState) =>
            prevState.map((faq) => {
                if (faq._id === tempFaqId) {
                    return { ...faq, question, answer, category }; // Optimistically update
                }
                return faq;
            })
        );

        setQuestion('');
        setAnswer('');
        setCategory('');
        setFormStatus(false);
        setEditMode(false);
        setTempFaqId('');

        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            method: "update_faq",
            faq: {
                ques: question,
                ans: answer,
                category: category
            },
            id: tempFaqId

        }
        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_setting`, payload);
            if (data.success) {
                toast.success("Faq updated successfully");

            } else {
                throw new Error("Failed to update FAQ");
            }

        } catch (error) {
            console.log(error);
            setFaqs(originalFaqs);
            toast.error("Something went wrong, please try again later");

        }

    }

    const handleBack = () => {
        setFormStatus(false);
        setEditMode(false);
        setQuestion('');
        setAnswer('');
        setCategory('');
        setTempFaqId('');
    }


    // Filter FAQs based on the search term
    const filteredFaqs = faqs.filter((faq) => {
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        return (
            faq.question.toLowerCase().includes(lowerCaseSearchTerm) ||
            faq.answer.toLowerCase().includes(lowerCaseSearchTerm) ||
            faq.category.toLowerCase().includes(lowerCaseSearchTerm)
        );
    });

    return (
        <div className={`${styles.faqContainer}`}>

            {!formStatus ? <>
                <div className="mb-3 w-100 d-flex justify-content-between align-items-center">
                    <input
                        type="text"
                        placeholder="Search FAQs by question, answer, or category..."
                        className="form-control me-2 w-75"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />

                    <button className='btn btn-primary w-25' onClick={() => setFormStatus(true)}>Add FAQ</button>
                </div>

                <div className="mb-3 w-100" style={{ maxHeight: '100%', overflowY: 'auto' }}>
                    {loading
                        ? 
                        <div className='text-center'>
                            <CircularProgress />
                        </div>
                        : (filteredFaqs.length > 0 ? (
                            <ul className="list-group" >
                                {filteredFaqs.map((faq, index) => (
                                    <li key={index} className="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Q: {faq.question}</strong>
                                            <span className="badge bg-info ms-2">{faq.category}</span>
                                            <br />
                                            <strong>A:</strong> {faq.answer}
                                        </div>
                                        <div className="d-flex justify-content-between align-items-center">
                                            <button
                                                className="btn btn-sm btn-warning me-2"
                                                onClick={() => handleEdit(faq)}
                                            >
                                                Edit
                                            </button>
                                            <button
                                                className="btn btn-sm btn-danger"
                                                onClick={() => confirmDelete(faq._id)}
                                            >
                                                Delete
                                            </button>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        ) : (
                            faqs.length === 0 ?
                                <p className="text-center text-muted">No FAQs found.</p> :
                                <p className="text-center text-muted">No FAQs match your search.</p>
                        )
                        )
                    }
                </div>

            </>
                :

                <div className='d-flex flex-column justify-content-center align-items-end w-100'>
                    <button className="btn btn-primary mb-3" onClick={handleBack}>Back</button>

                    <div className={`card mb-0 p-3 ${styles.formCard}`}>
                        <h6 className="fw-bold">{editMode ? 'Update FAQ' : 'Add FAQ'}</h6>
                        <form onSubmit={editMode ? handleUpdate : handleSubmit}>
                            <div className="mb-2">
                                <label htmlFor="category" className="form-label">
                                    Category
                                </label>
                                <input
                                    type="text"
                                    id="category"
                                    placeholder="Enter category"
                                    className="form-control"
                                    required
                                    onChange={(e) => setCategory(e.target.value)}
                                    value={category}
                                />
                            </div>
                            <div className="mb-2">
                                <label htmlFor="question" className="form-label">
                                    Question
                                </label>
                                <textarea
                                    id="question"
                                    placeholder="Enter question"
                                    className="form-control"
                                    required
                                    onChange={(e) => setQuestion(e.target.value)}
                                    value={question}
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter" && !e.shiftKey) {
                                            e.preventDefault(); // Prevent new line
                                            e.target.form.requestSubmit(); // Trigger the form's onSubmit event
                                        }
                                    }}
                                />
                            </div>

                            <div className="mb-2">
                                <label htmlFor="answer" className="form-label">
                                    Answer
                                </label>
                                <textarea
                                    id="answer"
                                    placeholder="Enter answer"
                                    className="form-control"
                                    required
                                    onChange={(e) => setAnswer(e.target.value)}
                                    value={answer}
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter" && !e.shiftKey) {
                                            e.preventDefault(); // Prevent new line
                                            e.target.form.requestSubmit(); // Trigger the form's onSubmit event
                                        }
                                    }}
                                />
                            </div>

                            {editMode ? <button className="btn btn-primary w-100" type="submit">
                                Update
                            </button> :
                                <button className="btn btn-primary w-100" type="submit">
                                    Add
                                </button>}
                        </form>
                    </div>
                </div>
            }




            {/* Delete Confirmation Modal */}
            <DeleteModal
                show={showDeleteModal}
                onConfirm={handleDelete}
                onCancel={() => setShowDeleteModal(false)}
            />
        </div>
    );
};

export default FaqSettings;
