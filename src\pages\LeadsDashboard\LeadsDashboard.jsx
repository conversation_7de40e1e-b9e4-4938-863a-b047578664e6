import React, { useState, useEffect, useContext } from 'react';
import styles from './LeadsDashboard.module.css';
import LabelSelector from './LabelSelector';
import LabelColumn from './LabelColumn';
import LeftMenu from '../../components/LeftMenu';
import FilterSortBar from './FilterSortBar';
import { BASE_URL2 } from '../../api/api';
import { AuthContext } from '../../context/AuthContext';
import axios from 'axios';
import { CircularProgress } from '@mui/material';
import { ChatState } from '../../context/AllProviders';
import { useNavigate } from 'react-router-dom';
import { DndContext, closestCenter, DragOverlay } from '@dnd-kit/core';
import { arrayMove, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import {
    SortableContext,
    rectSortingStrategy,
} from '@dnd-kit/sortable';
import ContactCard from './ContactCard';
import { toast } from 'react-toastify';
const LeadsDashboard = () => {
    const { currentUser } = useContext(AuthContext);
    const navigate = useNavigate();
    const { pipelines, activePipelineId, setActivePipelineId } = ChatState();
    const activePipeline = pipelines?.find(p => p.id === activePipelineId) || null;

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const [visibleLabels, setVisibleLabels] = useState([]);
    const [groupBy, setGroupBy] = useState("labels");
    const [filters, setFilters] = useState({ sort: "recent", search: "", agent: "", label: "" });
    const [allContacts, setAllContacts] = useState([]); // Static source
    const [filteredContacts, setFilteredContacts] = useState([]);
    const [dateRange, setDateRange] = useState([new Date().toISOString(), new Date().toISOString()]);
    const [fromDate, toDate] = dateRange;
    const [loading, setLoading] = useState(false);
    const [labels, setLabels] = useState([]);
    const [agents, setAgents] = useState([]);

    const [activeId, setActiveId] = useState(null);



    // 🔄 Re-filter contacts whenever filters change
    useEffect(() => {
        let filtered = [...allContacts];

        if (filters.search) {
            const query = filters.search.toLowerCase();
            filtered = filtered.filter(c =>
                c.name.toLowerCase().includes(query) ||
                c.email.toLowerCase().includes(query) ||
                c.phone.includes(query)
            );
        }

        if (filters.agent) filtered = filtered.filter(c => c.agent === filters.agent);
        if (filters.status) filtered = filtered.filter(c => c.status === filters.status);
        if (filters.label) filtered = filtered.filter(c => c.labels[0]?.id == filters.label);
        if (filters.unreadOnly) filtered = filtered.filter(c => c.unread === true);

        switch (filters.sort) {
            case 'nameAsc': filtered.sort((a, b) => a.name.localeCompare(b.name)); break;
            case 'nameDesc': filtered.sort((a, b) => b.name.localeCompare(a.name)); break;
            case 'oldest': filtered.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt)); break;
            case 'recent': filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)); break;
            case 'activity': filtered.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity)); break;
        }

        setFilteredContacts(filtered);
    }, [filters, allContacts]);

    useEffect(() => {
        const fetchLabels = async () => {
            if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
            const body = {
                user_id: currentUser.parent_id,
                token: currentUser.parent_token,
                method: "wp_list_retrieve_all",
            };

            try {
                const response = await axios.post(`${BASE_URL2}/contact_list`, body);
                if (response.data.success) {
                    const unassignedLabel = {
                        id: -1, // Use a negative or unique ID to avoid conflict
                        name: "Unassigned",
                        color_code: "#6c757d", // A neutral gray color
                        description: "Contacts without any label",
                        list_action: null,
                    };
                    setLabels([unassignedLabel, ...response.data.data]); // Store labels if fetch is successful

                }
            } catch (error) {
                console.error("Error fetching labels:", error);
            }
        };

        fetchLabels();
    }, [currentUser]);

    useEffect(() => {
        if (groupBy === 'labels' && labels.length > 0) {
            const stored = localStorage.getItem('visibleLabels');
            if (stored) {
                try {
                    const parsed = JSON.parse(stored);
                    // ensure stored labels still exist in current label list
                    const filtered = labels.filter(label => parsed.some(p => p.id === label.id));
                    setVisibleLabels(filtered);
                } catch {
                    setVisibleLabels(labels); // fallback in case JSON parse fails
                }
            } else {
                setVisibleLabels(labels);
            }
        } else if (groupBy === 'agent') {
            const getRandomColor = () => {
                const letters = '0123456789ABCDEF';
                let color = '#';
                for (let i = 0; i < 6; i++) {
                    color += letters[Math.floor(Math.random() * 16)];
                }
                return color;
            };

            const agentMap = new Map();

            allContacts.forEach((c, index) => {
                if (c.agent && !agentMap.has(c.agent)) {
                    agentMap.set(c.agent, {
                        id: index + 1,
                        name: c.agent,
                        color_code: getRandomColor()
                    });
                }
            });
            const uniqueAgentObjects = Array.from(agentMap.values());
            setVisibleLabels(uniqueAgentObjects);
        } else if (groupBy === 'pipeline' && activePipeline?.stages?.length > 0) {
            const getRandomColor = () => {
                const letters = '0123456789ABCDEF';
                let color = '#';
                for (let i = 0; i < 6; i++) {
                    color += letters[Math.floor(Math.random() * 16)];
                }
                return color;
            };

            const pipelineStages = activePipeline.stages.map((stage, index) => ({
                id: stage.id,
                name: stage.name,
                color_code: getRandomColor()
            }));

            setVisibleLabels(pipelineStages);

        } else {
            setVisibleLabels([]);
        }
    }, [groupBy, allContacts, labels, activePipeline]);

    const mapApiContactToStaticFormat = (apiContact, id) => {
        const hasLabels = Array.isArray(apiContact.label) && apiContact.label.length > 0;

        const labels = hasLabels
            ? apiContact.label.map(l => ({
                id: l.id,
                name: l.name,
                color: l.color_code,
            }))
            : [{
                id: -1,
                name: "Unassigned",
                color: "#6c757d",
            }];

        return {
            id,
            name: apiContact.name || '',
            email: apiContact.email || '',
            phone: apiContact.mobile || '',
            agent: apiContact.agent_name || '',
            unread: apiContact.conv_count_user > apiContact.conv_count_agent,
            labels,
            createdAt: apiContact.update_time || new Date().toISOString(),
            lastActivity: apiContact.update_time || new Date().toISOString(),
            lastContacted: apiContact.update_time?.split('T')[0] || '',
        };
    };



    useEffect(() => {

        fetchContacts();

    }, [currentUser, fromDate, toDate])

    useEffect(() => {
        fetchAgents();

    }, [currentUser])

    const fetchContacts = async () => {
        if (!currentUser || !currentUser.token || !currentUser.user_id || !currentUser.user_type || !fromDate || !toDate) return;

        setLoading(true);

        const payload = {
            token: currentUser.token,
            user_id: currentUser.user_id,
            method: "left_menu",
            brand_number: currentUser.brand_number,
            agent_id: 0,
            type: "whatsapp",
            from_date: new Date(fromDate).toISOString().split("T")[0],
            to_date: new Date(toDate).toISOString().split("T")[0],
            page: 1,
            user_type: currentUser.user_type
        };

        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_conv`, payload);

            // if (data.success === true && Array.isArray(data.data)) {
            //     const contacts = data.data.map((item, index) =>
            //         mapApiContactToStaticFormat(item, index + 1)
            //     );

            //     setAllContacts(contacts);
            //     setFilteredContacts(contacts);
            // }
            if (data.success === true && Array.isArray(data.data)) {
                const contacts = data.data.map((item, index) => {
                    const contact = mapApiContactToStaticFormat(item, index + 1);

                    // 💡 Assign a dummy pipelineStageId (alternating for demo)
                    const dummyStages = activePipeline?.stages || [];
                    const stage = dummyStages[index % dummyStages.length];

                    return {
                        ...contact,
                        pipelineStageId: stage?.id || null
                    };
                });

                setAllContacts(contacts);
                setFilteredContacts(contacts);
            }
        } catch (err) {
            console.error("Error fetching contacts:", err);
        } finally {
            setLoading(false);
        }
    };


    const fetchAgents = async () => {
        if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
        const payload = {
            user_id: currentUser.parent_id,
            method: "retrieve_agent",
            token: currentUser.parent_token,
            user_type: currentUser.user_type,
            agent_id: currentUser.user_id,
        }

        try {
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);

            if (data.success) {
                const agents = data.data.filter((item) => item.agent_type === "agent")
                setAgents(agents);
            }

        } catch (error) {
            console.error("Error fetching agents:", error);

        }


    }

    const handleFilterChange = (newFilters) => {
        setFilters(newFilters);
    };

    const handleDateChange = (dates) => {
        if (!dates) {
            setDateRange([null, null]);
            return;
        }

        const [start, end] = dates;

        // Ensure proper start and end limits
        const adjustedStart = start ? new Date(start.setHours(0, 0, 0, 0)) : null; // Start at 00:00:00
        const adjustedEnd = end ? new Date(end.setHours(23, 59, 59, 999)) : null; // End at 23:59:59

        setDateRange([adjustedStart, adjustedEnd]);

        if (adjustedStart && adjustedEnd) {

            fetchContacts();
        }
    };

    const handleDragStart = (event) => {
        setActiveId(event.active.id);
    };


    const handleDragEnd = async (event) => {
        setActiveId(null);
        const { active, over } = event;
        if (!over || active.id === over.id) return;

        const draggedContactId = active.id;
        const overId = over.id;

        let targetLabel =
            labels.find(l => l.id === overId) ||
            (() => {
                const targetContact = filteredContacts.find(c => c.id === overId);
                return targetContact?.labels?.[0] || null;   // first label, if any
            })();

        if (!targetLabel) return;

        const draggedContact = filteredContacts.find(c => c.id === draggedContactId);
        const currentLabelId = draggedContact?.labels?.[0]?.id || null; // null if no label now
        const targetLabelId = targetLabel.id;




        if (currentLabelId === targetLabelId) return;


        const success = await changeLabel(draggedContact.phone, currentLabelId, targetLabelId);
        if (success) {
            setFilteredContacts(prevContacts => {
                return prevContacts.map(contact => {
                    if (contact.id === draggedContactId) {
                        return {
                            ...contact,
                            labels: targetLabel ? [{
                                id: targetLabel.id,
                                name: targetLabel.name,
                                color: targetLabel.color_code
                            }] : []
                        };
                    }
                    return contact;
                });
            });
        }

    };


    const changeLabel = async (mobile, currentLabelId, targetLabelId) => {
        try {
            const payload = {
                token: currentUser.token,
                user_id: currentUser.user_id,
                user_type: currentUser.user_type,
                method: targetLabelId === -1 ? "removeLabel" : "moveToLabel",
                current_list_id: currentLabelId,
                mobile,
                ...(targetLabelId !== -1 && { target_list_id: targetLabelId }),
            }
            const { data } = await axios.post(`${BASE_URL2}/v1/label`, payload);
            if (data.success) {
                toast.success(data.message)
                return true;
            } else {
                return false;
            }

        } catch (error) {
            if (error.response && error.response.status === 429) {
                toast.error("Too many requests. Please try again later.")

            } else {
                console.error(error);
                toast.error("Something went wrong, please try again later")
            }
            return false;
        }

    }


    return (
        <div className={styles.pageContainer}>
            <LeftMenu />
            <div className={`d-flex flex-column justify-content-start align-items-center ${styles.main}`}>
                <div className="d-flex justify-content-between w-100 align-items-center mb-3">
                    <h4 className="fw-bold">Leads</h4>
                    <div className='d-flex justify-content-between align-items-center'>
                        {groupBy === "pipeline" && <div className={styles.pipelineDropdownWrapper}>
                            <div className="dropdown">
                                <button
                                    className="btn btn-outline-secondary dropdown-toggle"
                                    type="button"
                                    data-bs-toggle="dropdown"
                                    aria-expanded="false"
                                >
                                    {activePipeline?.name || 'Select Pipeline'}
                                </button>

                                <ul className="dropdown-menu">
                                    {pipelines.map((pipeline) => (
                                        <li key={pipeline.id}>
                                            <button
                                                className={`dropdown-item ${pipeline.id === activePipelineId ? 'active' : ''}`}
                                                onClick={() => setActivePipelineId(pipeline.id)}
                                            >
                                                {pipeline.name}
                                            </button>
                                        </li>
                                    ))}

                                    <li><hr className="dropdown-divider" /></li>

                                    <li>
                                        <button
                                            className="dropdown-item text-primary"
                                            onClick={() => {
                                                // Navigate to /pipelines/new or handle add
                                                console.log('Navigate to create new pipeline');
                                                navigate('/create-pipeline')
                                            }}
                                        >
                                            + New pipeline
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>}

                        {groupBy === "labels" && (
                            <LabelSelector labels={labels} selected={visibleLabels} onChange={setVisibleLabels} />
                        )}
                        <select className="form-select w-auto ms-3" value={groupBy} onChange={e => setGroupBy(e.target.value)}>
                            <option value="labels">Label</option>
                            <option value="agent">Agent</option>
                            {/* <option value="pipeline">Pipeline</option> */}
                        </select>

                    </div>
                </div>

                <FilterSortBar agents={agents} labels={labels} onFilterChange={handleFilterChange} handleDateChange={handleDateChange} fromDate={fromDate} toDate={toDate} loading={loading} />

                {loading ? <CircularProgress /> :
                    <DndContext
                        // sensors={sensors}
                        // collisionDetection={closestCenter}
                        // onDragStart={handleDragStart}
                        // onDragEnd={handleDragEnd}
                        sensors={groupBy === "agent" ? undefined : sensors}
                        collisionDetection={groupBy === "agent" ? undefined : closestCenter}
                        onDragStart={groupBy === "agent" ? undefined : handleDragStart}
                        onDragEnd={groupBy === "agent" ? undefined : handleDragEnd}
                    >

                        <div className={`d-flex overflow-auto w-100 ${styles.columnsContainer}`}>
                            {visibleLabels.map((label, index) => {
                                const contacts = groupBy === "labels"
                                    ? label.name === "Unassigned"
                                        ? filteredContacts.filter(c => c.labels[0].id === -1)
                                        : filteredContacts.filter(c =>
                                            c.labels?.some(l => l.name === label.name)
                                        )
                                    : groupBy === "agent"
                                        ? filteredContacts.filter(c => c.agent === label.name)
                                        : groupBy === "pipeline"
                                            ? filteredContacts.filter(c => c.pipelineStageId === label.id)
                                            : [];

                                return (

                                    <SortableContext
                                        key={label.id}
                                        id={label.id}
                                        items={contacts.map(c => c.id)}
                                        strategy={rectSortingStrategy}
                                    >
                                        <LabelColumn
                                            key={label.name}
                                            label={label}
                                            contacts={contacts}
                                            index={index}
                                            total={visibleLabels.length}
                                            groupBy={groupBy}
                                            currentUser={currentUser}

                                        />
                                    </SortableContext>

                                );
                            })}

                        </div>
                        <DragOverlay>
                            {activeId ? (
                                <ContactCard groupBy={groupBy} currentUser={currentUser} contact={filteredContacts.find(c => c.id === activeId)} />
                            ) : null}
                        </DragOverlay>

                    </DndContext>
                }
            </div>
        </div >
    );
};

export default LeadsDashboard;
