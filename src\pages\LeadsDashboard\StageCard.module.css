.card {
    
    border-radius: .5rem;
    overflow: hidden;
    max-width: 300px;
    min-width: 250px;
    background-color: #f5f5f6 ;
    display: flex;
    flex-direction: column;
    height: 100%;
    cursor: move;
    
}
.header{
    word-wrap:break-word;
}

.header, .footer{
    padding: 0.75rem 1rem;
    font-weight: bold;
    
}



.body {
    padding: 1rem;
    flex-grow: 2;
}

.separator {
    border: 0;
    border-top: 1px solid #5f5e5e; 
    margin: 5px 0;  
  }