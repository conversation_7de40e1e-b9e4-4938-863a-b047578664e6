import React from 'react';
import styles from './LabelColumn.module.css';
import ContactCard from './ContactCard';
import { getContrastingTextColor } from '../../utils/Utils';

import { useDroppable } from '@dnd-kit/core';
const LabelColumn = ({ currentUser, label, contacts, index, total, groupBy = "labels", colorMap = {}, }) => {

  const isFirst = index === 0;
  const isLast = index === total - 1;
  const { setNodeRef } = useDroppable({
    id: label.id, // this ID will come back as `over.id`
  });
 

  return (
    <div ref={setNodeRef} className={`me-3 mb-3 ${styles.labelColumn}`}>
      <div
        className={styles.arrowHeader}
        style={{
          backgroundColor: label.color_code || '#ccc',
          clipPath: getArrowClipPath(isFirst, isLast),
          color: getContrastingTextColor(label.color_code || '#ccc')
        }}
      >
        <span>{label.name}</span>
        <span>{` (${contacts.length})`}</span>
      </div>
     
        <div className={styles.cardContainer}>
          {contacts.length === 0 ? <div className={styles.noContacts}>

            <p className="mt-2 mb-0 text-muted text-center">No contacts for selected date range</p>
          </div>
            :
            contacts.map(contact => (
              <ContactCard currentUser={currentUser} key={contact.id} contact={contact} groupBy={groupBy}/>
            ))}
        </div>
      
    </div>
  );
};

const getArrowClipPath = (isFirst, isLast) => {
  if (isFirst) {
    return `polygon(0 0, 90% 0, 100% 50%, 90% 100%, 0 100%)`; // Inward left arrow
  } else if (isLast) {
    return `polygon(0 0, 100% 0, 100% 100%, 0 100%, 0 100%, 10% 50%)`; // Outward right arrow
  } else {
    return `polygon(0 0, 90% 0, 100% 50%, 90% 100%, 0 100%, 10% 50%)`; // Both sides
  }
};

export default LabelColumn;
