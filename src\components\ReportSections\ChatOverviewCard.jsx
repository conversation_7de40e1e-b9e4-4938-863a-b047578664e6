import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ChatState } from "../../context/AllProviders";
import styles from './overviewCard.module.css'

const ChatOverviewCard = ({
  totalChats = [],
  answered = [],
  unanswered = [],
  repeated = [],
  liveChats = [],
}) => {
  const navigate = useNavigate();
  const { setAllChats } = ChatState();

  // Local states to store each progress bar width (numeric).
  // Initialize them at 0 so they can animate from 0 to final.
  const [answeredWidth, setAnsweredWidth] = useState(0);
  const [unansweredWidth, setUnansweredWidth] = useState(0);
  const [repeatedWidth, setRepeatedWidth] = useState(0);
  const [liveWidth, setLiveWidth] = useState(0);
  const [newWidth, setNewWidth] = useState(0);

  // Helper to calculate percentage
  const getPercentage = (value, total) => {
    if (!total || total === 0) return 0;
    return ((value / total) * 100).toFixed(1);
  };

  const newChats = totalChats.filter(
    (item) => !repeated.some((rep) => rep.mobile === item.mobile)
  );


  // On mount (and whenever the data arrays change),
  // set each bar's width so we can see them animate from 0 to final.
  useEffect(() => {
    const total = totalChats.length;
    setAnsweredWidth(getPercentage(answered.length, total));
    setUnansweredWidth(getPercentage(unanswered.length, total));
    setRepeatedWidth(getPercentage(repeated.length, total));
    setLiveWidth(getPercentage(liveChats.length, total));
    setNewWidth(getPercentage(total - repeated.length, total));
  }, [answered, unanswered, repeated, liveChats, totalChats]);

  const handleClick = (title, data) => {
    if (data.length === 0) return;
    const encodedTitle = btoa(title);
    setAllChats([...data]);
    navigate(`/home/<USER>
  };

  return (
    <div
      className={`card mb-0 shadow  p-4  ${styles.mainCard}`}
    >
      {/* Top: Total Chats */}
      <h5
        className={`mb-3 text-muted ${styles.overviewCard}`}
        role="button"
        onClick={() => handleClick("Total Chats", totalChats)}
      >
        <strong className="text-dark" style={{ fontSize: "1.5rem" }}>
          {totalChats.length}
        </strong>{" "}
        Total Chats
      </h5>

      {/* Answered */}
      <div
        className={`mb-3 ${styles.overviewCard} `}
        role="button"
        onClick={() => handleClick("Answered Chats", answered)}
      >
        <div className="d-flex justify-content-between mb-1">
          <span>Answered</span>
          <span>{answered.length}</span>
        </div>
        <div className="progress" style={{ height: "6px" }}>
          <div
            className="progress-bar bg-success  progress-bar-animated"
            role="progressbar"
            style={{
              // Animate width changes over 0.5s
              width: `${answeredWidth}%`,
              transition: "width 0.5s ease",
            }}
            aria-valuenow={answered.length}
            aria-valuemin="0"
            aria-valuemax={totalChats.length}
          />
        </div>
      </div>

      {/* Unanswered */}
      <div
        className={`mb-3 ${styles.overviewCard} `}
        role="button"
        onClick={() => handleClick("Unanswered Chats", unanswered)}
      >
        <div className="d-flex justify-content-between mb-1">
          <span>Unanswered</span>
          <span>{unanswered.length}</span>
        </div>
        <div className="progress" style={{ height: "6px" }}>
          <div
            className="progress-bar bg-danger  progress-bar-animated"
            role="progressbar"
            style={{
              width: `${unansweredWidth}%`,
              transition: "width 0.5s ease",
            }}
            aria-valuenow={unanswered.length}
            aria-valuemin="0"
            aria-valuemax={totalChats.length}
          />
        </div>
      </div>

      {/* New Chats */}
      <div
        className={`mb-3 ${styles.overviewCard} `}
        role="button"
        onClick={() => handleClick("New Chats", newChats )}
      >
        <div className="d-flex justify-content-between mb-1">
          <span>New</span>
          <span>{totalChats.length - repeated.length}</span>
        </div>
        <div className="progress" style={{ height: "6px" }}>
          <div
            className="progress-bar bg-dark progress-bar-animated"
            role="progressbar"
            style={{
              width: `${newWidth}%`,
              transition: "width 0.5s ease",
            }}
            aria-valuenow={totalChats.length - repeated.length}
            aria-valuemin="0"
            aria-valuemax={totalChats.length}
          />
        </div>
      </div>
      {/* Repeated */}
      <div
        className={`mb-3 ${styles.overviewCard} `}
        role="button"
        onClick={() => handleClick("Repeated Chats", repeated)}
      >
        <div className="d-flex justify-content-between mb-1">
          <span>Repeated</span>
          <span>{repeated.length}</span>
        </div>
        <div className="progress" style={{ height: "6px" }}>
          <div
            className="progress-bar bg-warning progress-bar-animated"
            role="progressbar"
            style={{
              width: `${repeatedWidth}%`,
              transition: "width 0.5s ease",
            }}
            aria-valuenow={repeated.length}
            aria-valuemin="0"
            aria-valuemax={totalChats.length}
          />
        </div>
      </div>

      {/* Live Chats */}
      <div
        className={`mb-3 ${styles.overviewCard} `}
        role="button"
        onClick={() => handleClick("Live Chats", liveChats)}
      >
        <div className="d-flex justify-content-between mb-1">
          <span>Live Chats</span>
          <span>{liveChats.length}</span>
        </div>
        <div className="progress" style={{ height: "6px" }}>
          <div
            className="progress-bar bg-info  progress-bar-animated"
            role="progressbar"
            style={{
              width: `${liveWidth}%`,
              transition: "width 0.5s ease",
            }}
            aria-valuenow={liveChats.length}
            aria-valuemin="0"
            aria-valuemax={totalChats.length}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatOverviewCard;
