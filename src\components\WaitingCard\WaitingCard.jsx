import { useEffect, useState } from "react";


const WaitingCard = ({item}) => {

    const[timer,setTimer]=useState(60);
    useEffect(()=>{

      const id=  setInterval(()=>{
            setTimer((prevState)=>prevState-1)

        },1000)

        return clearInterval(id)

    },[])
    return (
        <li
         className="d-flex justify-content-evenly align-items-center"
            // key={i}
            // className={selectedMobileNumber === item.mobile ? "active" : ""}
            // onMouseEnter={() => setLeftMenuOption(item.mobile)}
            // onMouseLeave={() => setLeftMenuOption("")}
            style={{padding:".5rem"}}
        >
            <div
                // onClick={() => handleSelect(item)}
                className="d-flex justify-content-evenly align-items-center"
            >
                <div className="chat-user-img flex-shrink-0 me-2">
                    <div style={{ width: ".7rem", height: ".7rem", position: "absolute",  borderRadius: "50%", bottom: "0", right: "0", zIndex: "1" }}></div>
                    <div className="avatar-group">
                        <div className="avatar-group-item">
                            <div className="avatar-xs">
                                {/* <!-- Default avatar icon (you can replace with the actual user avatar) --> */}
                                <svg
                                    viewBox="0 0 212 212"
                                    height="212"
                                    width="212"
                                    preserveAspectRatio="xMidYMid meet"
                                    className="xh8yej3 x5yr21d"
                                    version="1.1"
                                    x="0px"
                                    y="0px"
                                    enableBackground="new 0 0 212 212"
                                >
                                    <title>default-user</title>
                                    <path
                                        fill="#DFE5E7"
                                        className="background"
                                        d="M106.251,0.5C164.653,0.5,212,47.846,212,106.25S164.653,212,106.25,212C47.846,212,0.5,164.654,0.5,106.25 S47.846,0.5,106.251,0.5z"
                                    ></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex-grow-1 overflow-hidden">
                    <div className="d-flex align-item-center">
                        <div className={`text-truncate mb-0 font-size-17 flex flex-grow-1 ChatItemName`}>
                            {/* {item.name
                                ? isSpecialChar(item.name)
                                    ? currentUser.user_type === "admin"
                                        ? item.mobile
                                        : maskNo(item.mobile)
                                    : item.name
                                : currentUser.user_type === "admin"
                                    ? item.mobile
                                    : maskNo(item.mobile)} */}
                        </div>
                        <div className="font-size-12 text-truncate dateColor mt-1">
                            {/* {dayjs(item.created).format("YYYY-MM-DD") === formateddate
                                ? dayjs(item.created).format("h:mm A")
                                : dayjs(item.created).format("DD/MM/YYYY")} */}
                        </div>
                    </div>
                    <div className="d-flex align-items-center">
                        <div className="me-3">
                            <span style={{ color: "blue" }}>{item.name}:</span>
                        </div>
                        <div className="ms-3 d-flex justify-content-center">
                            {/* <div className="flex">
                                {item.read_status === 0 && item.read_count !== 0 && (
                                    <span className="badge badge-primary rounded-circle wgreen">
                                        {item.read_count}
                                    </span>
                                )}
                            </div> */}
                        </div>
                    </div>
                </div>
            </div>

            {/* <!-- Timer --> */}
            <div className="me-2">
                <div className="font-size-12 text-muted">
                    Waiting for Agent to Join:{" "}
                    <span className="timer-text">{timer}</span> {/* Example timer */}
                </div>
            </div>

            {/* <!-- Join Chat Button --> */}
            <div className="">
                <button
                    className="btn-primary"
                    // onClick={() => joinChat(item)} 
                    style={{border:"none",borderRadius:".2rem"}}
                    
                >
                    Join Chat
                </button>
                
            </div>
        </li>

    )
}

export default WaitingCard;