/*
Template Name: Doot - Responsive Bootstrap 5 Chat App
Author: Themesbrand
Version: 1.0.0
Website: https://Themesbrand.com/
Contact: <EMAIL>
File: Main Css File
*/
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap");
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-light.eot");
  src: local("Cerebri-sans Light"), url("../fonts/cerebrisans-light.woff") format("woff");
  font-weight: 300;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-regular.eot");
  src: local("Cerebri-sans Regular"), url("../fonts/cerebrisans-regular.woff") format("woff");
  font-weight: 400;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-medium.eot");
  src: local("Cerebri-sans Medium"), url("../fonts/cerebrisans-medium.woff") format("woff");
  font-weight: 500;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-semibold.eot");
  src: local("Cerebri-sans Semibold"), url("../fonts/cerebrisans-semibold.woff") format("woff");
  font-weight: 600;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-bold.eot");
  src: local("Cerebri-sans Bold"), url("../fonts/cerebrisans-bold.woff") format("woff");
  font-weight: 700;
}
:root {
  --bs-sidebar-bg: #2e2e2e;
  --bs-sidebar-menu-item-color: #878a92;
  --bs-sidebar-menu-item-active-color: #4eac6d;
  --bs-sidebar-menu-item-active-bg: #f7f7ff;
  --bs-sidebar-sub-bg: #ffffff;
  --bs-footer-bg: rgba(255,255,255,.05);
  --bs-display-none: none;
  --bs-display-block: block;
  --bs-chat-text-color: #495057;
  --bs-chat-primary-bg: #ffffff;
  --bs-light: #f6f6f9;
  --bs-light-rgb: 246, 246, 249;
  --bs-dark: #212529;
  --bs-dark-rgb: 33, 37, 41;
  --bs-text-muted: #797c8c;
  --bs-link-color: #4eac6d;
  --bs-link-hover-color: #4eac6d;
  --bs-border-color: #eaeaf1;
  --bs-dropdown-bg: #fff;
  --bs-dropdown-border-color: #f6f6f9;
  --bs-dropdown-link-color: #212529;
  --bs-dropdown-link-hover-color: #1f2327;
  --bs-dropdown-link-hover-bg: #f8f9fa;
  --bs-dropdown-border-width: 1px;
  --bs-card-bg: #fff;
  --bs-card-cap-bg: #fff;
  --bs-card-border-color: #eff0f2;
  --bs-card-logo-dark: block;
  --bs-card-logo-light: none;
  --bs-card-box-shadow: 0 2px 3px #e6e8eb;
  --bs-modal-bg: #fff;
  --bs-modal-content-bg: #fff;
  --bs-modal-content-border-color: #f6f6f9;
  --bs-nav-tabs-link-active-color: #495057;
  --bs-nav-tabs-link-active-bg: #fafafa;
  --bs-accordion-button-active-color: #469b62;
  --bs-accordion-bg: #fff;
  --bs-accordion-button-bg: #f9f9f9;
  --bs-accordion-button-active-bg:rgba(246,246,249,.7);
  --bs-table-color: #495057;
  --bs-boxed-body-bg: #e9ebf0;
  --bs-body-heading-color: #495057;
  --bs-body-bg: #f2f2f2;
  --bs-body-color: #495057;
  --bs-body-color-rgb: 73, 80, 87;
  --bs-progress-bg: #f9f9f9;
  --bs-toast-background-color: rgba(255, 255, 255, 0.85);
  --bs-toast-border-color: rgba(0, 0, 0, 0.1);
  --bs-toast-header-border-color: rgba(0, 0, 0, 0.05);
  --bs-list-group-hover-bg: #f8f9fa;
  --bs-popover-bg: #fff;
  --bs-pagination-hover-bg: #f9f9f9;
  --bs-input-bg: #fff;
  --bs-input-group-addon-border-color: #e6ebf5;
  --bs-input-border: #e6ebf5;
  --bs-input-border-color: #cfd4d8;
  --bs-input-focus-border: #a7d6b6;
  --bs-input-disabled-bg: #f9f9f9;
  --bs-input-placeholder-color: #797c8c;
  --bs-input-group-addon-bg: #f9f9f9;
  --bs-input-check-border: var(--bs-input-border);
}

[data-layout-mode=dark] {
  --bs-gray-100: #212529;
  --bs-gray-200: #262626;
  --bs-gray-300: #2e2e2e;
  --bs-gray-400: #757575;
  --bs-gray-500: #8f9198;
  --bs-gray-600: #adb5bd;
  --bs-gray-700: #adb5bd;
  --bs-gray-800: #f9f9f9;
  --bs-gray-900: #f8f9fa;
  --bs-sidebar-bg: #2e2e2e;
  --bs-sidebar-menu-item-color: #878a92;
  --bs-sidebar-menu-item-active-color: #4eac6d;
  --bs-sidebar-menu-item-active-bg: #f7f7ff;
  --bs-sidebar-sub-bg: #262626;
  --bs-footer-bg: rgba(46,46,46,.5);
  --bs-display-none: block;
  --bs-display-block: none;
  --bs-chat-text-color: rgba(255,255,255,.8);
  --bs-chat-primary-bg: #383838;
  --bs-card-title-desc: #757575;
  --bs-topnav-bg: #2c2c2c;
  --bs-topnav-item-color: #757575;
  --bs-topnav-item-color-active: #fff;
  --bs-twocolumn-menu-iconview-bg: #fff;
  --bs-twocolumn-menu-bg: #fff;
  --bs-twocolumn-menu-iconview-bg-dark: var(--bs-vertical-menu-bg-dark);
  --bs-twocolumn-menu-bg-dark: #30363a;
  --bs-twocolumn-menu-item-color-dark: var(--bs-vertical-menu-item-color-dark);
  --bs-twocolumn-menu-item-active-color-dark: #fff;
  --bs-twocolumn-menu-item-active-bg-dark: rgba(255, 255, 255, 0.15);
  --bs-boxed-body-bg: #181b1e;
  --bs-heading-color: #adb5bd;
  --bs-light: #2e2e2e;
  --bs-light-rgb: 46, 46, 46;
  --bs-dark: #f9f9f9;
  --bs-dark-rgb: 249, 249, 249;
  --bs-text-muted: #8f9198;
  --bs-link-color: #adb5bd;
  --bs-link-hover-color: #adb5bd;
  --bs-border-color: #333333;
  --bs-dropdown-bg: #333333;
  --bs-dropdown-border-color: #383838;
  --bs-dropdown-link-color: #8f9198;
  --bs-dropdown-link-hover-color: #8f9198;
  --bs-dropdown-link-hover-bg: #383838;
  --bs-dropdown-border-width: 1px;
  --bs-card-bg: #262626;
  --bs-card-cap-bg: #262626;
  --bs-card-border-color: #082a3e;
  --bs-card-logo-dark: none;
  --bs-card-logo-light: block;
  --bs-card-box-shadow: 0 2px 3px #04121a;
  --bs-modal-bg: #262626;
  --bs-modal-content-bg: #262626;
  --bs-modal-content-border-color: #2e2e2e;
  --bs-nav-tabs-link-active-color: #f8f9fa;
  --bs-nav-tabs-link-active-bg: #2e2e2e;
  --bs-accordion-button-active-color: #fff;
  --bs-accordion-bg: transparent;
  --bs-accordion-button-bg: #2e2e2e;
  --bs-accordion-button-active-bg:#2e2e2e;
  --bs-table-color: #757575;
  --bs-boxed-body-bg: #333847;
  --bs-body-heading-color: #adb5bd;
  --bs-body-bg: #2e2e2e;
  --bs-body-color: #8f9198;
  --bs-body-color-rgb: 73, 80, 87;
  --bs-progress-bg: #2e2e2e;
  --bs-toast-background-color: rgba(46, 46, 46, 0.85);
  --bs-toast-border-color: rgba(255, 255, 255, 0.1);
  --bs-toast-header-border-color: rgba(255, 255, 255, 0.05);
  --bs-list-group-hover-bg: #2c2c2c;
  --bs-popover-bg: #292929;
  --bs-pagination-hover-bg: #303030;
  --bs-input-bg: #333333;
  --bs-input-group-addon-border-color: #2e2e2e;
  --bs-input-border: #2e2e2e;
  --bs-input-border-color: #333333;
  --bs-input-focus-border: #383838;
  --bs-input-disabled-bg: #262626;
  --bs-input-placeholder-color: #8f9198;
  --bs-input-group-addon-bg: #2e2e2e;
  --bs-input-check-border: #383838;
}

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-right: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  -webkit-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0) translate(0, 0);
  transform: scale(0) translate(0, 0);
  pointer-events: none;
}

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2);
}

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
}

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important;
}

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
}

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1;
}

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em;
}

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em;
}

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom;
}

.waves-input-wrapper.waves-button {
  padding: 0;
}

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  right: 0;
  z-index: 1;
}

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
}

.waves-float {
  -webkit-mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  transition: all 300ms;
}

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
}

.waves-block {
  display: block;
}

.waves-effect.waves-light .waves-ripple {
  background-color: rgba(255, 255, 255, 0.4);
}

.waves-effect.waves-primary .waves-ripple {
  background-color: rgba(78, 172, 109, 0.4);
}

.waves-effect.waves-success .waves-ripple {
  background-color: rgba(6, 214, 160, 0.4);
}

.waves-effect.waves-info .waves-ripple {
  background-color: rgba(80, 165, 241, 0.4);
}

.waves-effect.waves-warning .waves-ripple {
  background-color: rgba(255, 209, 102, 0.4);
}

.waves-effect.waves-danger .waves-ripple {
  background-color: rgba(239, 71, 111, 0.4);
}

.avatar-xs {
  height: 1.8rem;
  width: 1.8rem;
}

.avatar-sm {
  height: 2.4rem;
  width: 2.4rem;
}

.avatar-md {
  height: 4rem;
  width: 4rem;
}

.avatar-lg {
  height: 5rem;
  width: 5rem;
}

.avatar-xl {
  height: 6rem;
  width: 6rem;
}

.avatar-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: rgba(var(--bs-primary-rgb), 1);
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-weight: 500;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
}

.avatar-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding-right: 8px;
}
.avatar-group .avatar-group-item {
  margin-right: -8px;
  border: 2px solid var(--bs-card-bg);
  border-radius: 50%;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}
.avatar-group .avatar-group-item:hover {
  position: relative;
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
}

.font-size-10 {
  font-size: 10px !important;
}

.font-size-11 {
  font-size: 11px !important;
}

.font-size-12 {
  font-size: 12px !important;
}

.font-size-13 {
  font-size: 13px !important;
}

.font-size-14 {
  font-size: 14px !important;
}

.font-size-15 {
  font-size: 15px !important;
}

.font-size-16 {
  font-size: 16px !important;
}

.font-size-17 {
  font-size: 17px !important;
}

.font-size-18 {
  font-size: 18px !important;
}

.font-size-20 {
  font-size: 20px !important;
}

.font-size-22 {
  font-size: 22px !important;
}

.font-size-24 {
  font-size: 24px !important;
}

.fw-medium {
  font-weight: 500;
}

.fw-semibold {
  font-weight: 600;
}

.social-list-item {
  height: 2rem;
  width: 2rem;
  line-height: calc(2rem - 4px);
  display: block;
  border: 2px solid #adb5bd;
  border-radius: 50%;
  color: #adb5bd;
  text-align: center;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}
.social-list-item:hover {
  color: #797c8c;
  background-color: #f9f9f9;
}

.w-xs {
  min-width: 80px;
}

.w-sm {
  min-width: 95px;
}

.w-md {
  min-width: 110px;
}

.w-lg {
  min-width: 140px;
}

.w-xl {
  min-width: 160px;
}

.bg-overlay {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  background-color: rgba(52, 58, 64, 0.7);
}

.border-primary {
  border-color: rgba(var(--bs-primary-rgb), 1) !important;
}

.border-dark {
  border-color: var(--bs-dark) !important;
}

.border-light {
  border-color: var(--bs-light) !important;
}

[data-layout-mode=dark] .text-body {
  color: var(--bs-gray-500) !important;
}
[data-layout-mode=dark] .btn-close {
  -webkit-filter: invert(1) grayscale(100%) brightness(200%);
          filter: invert(1) grayscale(100%) brightness(200%);
}

.loader-line {
  height: 28px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 4px;
}
.loader-line .line:nth-last-child(1) {
  -webkit-animation: loadingLine 1.25s 1s infinite;
          animation: loadingLine 1.25s 1s infinite;
}
.loader-line .line:nth-last-child(2) {
  -webkit-animation: loadingLine 1.25s 0.75s infinite;
          animation: loadingLine 1.25s 0.75s infinite;
}
.loader-line .line:nth-last-child(3) {
  -webkit-animation: loadingLine 1.25s 0.5s infinite;
          animation: loadingLine 1.25s 0.5s infinite;
}
.loader-line .line:nth-last-child(4) {
  -webkit-animation: loadingLine 1.25s 0.25s infinite;
          animation: loadingLine 1.25s 0.25s infinite;
}
.loader-line .line:nth-last-child(5) {
  -webkit-animation: loadingLine 1.25s 0s infinite;
          animation: loadingLine 1.25s 0s infinite;
}

.line {
  display: inline-block;
  width: 4px;
  height: 10px;
  border-radius: 14px;
  background-color: rgba(var(--bs-text-muted), 0.7);
}

@-webkit-keyframes loadingLine {
  0% {
    height: 10px;
  }
  50% {
    height: 28px;
  }
  100% {
    height: 10px;
  }
}

@keyframes loadingLine {
  0% {
    height: 10px;
  }
  50% {
    height: 28px;
  }
  100% {
    height: 10px;
  }
}
.custom-accordion .card + .card {
  margin-top: 0.5rem;
}
.custom-accordion a i.accor-plus-icon {
  font-size: 16px;
}
.custom-accordion a.collapsed i.accor-plus-icon:before {
  content: "\f0142";
}

.accordion-button:after {
  margin-right: auto;
  margin-left: 0;
}

[data-layout-mode=dark] .accordion-button:after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23adb5bd'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.modal-header-colored .modal-header {
  background-color: rgba(var(--bs-primary-rgb), 1);
  margin: 0 -1px -1px -1px;
}

[type=tel]::-webkit-input-placeholder, [type=url]::-webkit-input-placeholder, [type=email]::-webkit-input-placeholder, [type=number]::-webkit-input-placeholder {
  text-align: right;
}

[type=tel]::-moz-placeholder, [type=url]::-moz-placeholder, [type=email]::-moz-placeholder, [type=number]::-moz-placeholder {
  text-align: right;
}

[type=tel]:-ms-input-placeholder, [type=url]:-ms-input-placeholder, [type=email]:-ms-input-placeholder, [type=number]:-ms-input-placeholder {
  text-align: right;
}

[type=tel]::-ms-input-placeholder, [type=url]::-ms-input-placeholder, [type=email]::-ms-input-placeholder, [type=number]::-ms-input-placeholder {
  text-align: right;
}

[type=tel]::placeholder,
[type=url]::placeholder,
[type=email]::placeholder,
[type=number]::placeholder {
  text-align: right;
}

.form-check,
.form-check-input,
.form-check-label {
  cursor: pointer;
  margin-bottom: 0;
}

.form-check-input:checked {
  background-color: rgba(var(--bs-primary-rgb), 1);
  border-color: rgba(var(--bs-primary-rgb), 1);
}

[data-simplebar] {
  position: relative;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -ms-flex-line-pack: start;
      align-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit;
}

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0;
}

.simplebar-offset {
  direction: inherit !important;
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  right: 0 !important;
  bottom: 0;
  left: 0 !important;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

.simplebar-content-wrapper {
  direction: inherit;
  -webkit-box-sizing: border-box !important;
          box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%;
  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  visibility: visible;
  overflow: auto;
  /* Scroll on this element otherwise element can't have a padding applied properly */
  max-width: 100%;
  /* Not required for horizontal scroll to trigger */
  max-height: 100%;
  /* Needed for vertical scroll to trigger */
  scrollbar-width: none;
  padding: 0px !important;
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.simplebar-content:before,
.simplebar-content:after {
  content: " ";
  display: table;
}

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none;
}

.simplebar-height-auto-observer-wrapper {
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: right;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  -webkit-box-flex: inherit;
      -ms-flex-positive: inherit;
          flex-grow: inherit;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -ms-flex-preferred-size: 0;
      flex-basis: 0;
}

.simplebar-height-auto-observer {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  right: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

.simplebar-track {
  z-index: 1;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none;
}

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all;
}

.simplebar-scrollbar {
  position: absolute;
  left: 2px;
  width: 7px;
  min-height: 10px;
}

.simplebar-scrollbar:before {
  position: absolute;
  content: "";
  background: #a2adb7;
  border-radius: 7px;
  right: 0;
  left: 0;
  opacity: 0;
  -webkit-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
}

.simplebar-scrollbar.simplebar-visible:before {
  /* When hovered, remove all transitions from drag handle */
  opacity: 0.5;
  -webkit-transition: opacity 0s linear;
  transition: opacity 0s linear;
}

.simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px;
}

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px;
}

.simplebar-track.simplebar-horizontal {
  right: 0;
  height: 11px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  height: 100%;
  right: 2px;
  left: 2px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  left: auto;
  right: 0;
  top: 2px;
  height: 7px;
  min-height: 0;
  min-width: 10px;
  width: auto;
}

/* Rtl support */
[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {
  left: auto;
  right: 0;
}

.hs-dummy-scrollbar-size {
  direction: ltr;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
}

.simplebar-hide-scrollbar {
  position: fixed;
  right: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
}

.custom-scroll {
  height: 100%;
}

.fg-emoji-picker {
  background-color: var(--bs-card-bg) !important;
  width: 250px !important;
  -webkit-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12) !important;
          box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12) !important;
  top: auto !important;
  bottom: 70px;
}
.fg-emoji-picker * {
  font-family: var(--bs-font-sans-serif) !important;
  color: #495057 !important;
}
@media (max-width: 991.98px) {
  .fg-emoji-picker {
    right: 14px !important;
    top: auto !important;
    bottom: 60px;
  }
}
.fg-emoji-picker .fg-emoji-picker-container-title {
  color: var(--bs-dark) !important;
}
.fg-emoji-picker .fg-emoji-picker-search {
  height: 40px !important;
}
.fg-emoji-picker .fg-emoji-picker-search input {
  background-color: var(--bs-input-bg) !important;
  color: var(--bs-body-color) !important;
  border: 1px solid var(--bs-border-color);
  padding: 0.5rem 1rem !important;
  font-size: 0.9375rem !important;
}
.fg-emoji-picker .fg-emoji-picker-search input::-webkit-input-placeholder {
  color: var(--bs-input-placeholder-color) !important;
}
.fg-emoji-picker .fg-emoji-picker-search input::-moz-placeholder {
  color: var(--bs-input-placeholder-color) !important;
}
.fg-emoji-picker .fg-emoji-picker-search input:-ms-input-placeholder {
  color: var(--bs-input-placeholder-color) !important;
}
.fg-emoji-picker .fg-emoji-picker-search input::-ms-input-placeholder {
  color: var(--bs-input-placeholder-color) !important;
}
.fg-emoji-picker .fg-emoji-picker-search input::placeholder {
  color: var(--bs-input-placeholder-color) !important;
}
.fg-emoji-picker .fg-emoji-picker-search svg {
  fill: var(--bs-body-color) !important;
  left: 11px;
  top: 12px;
}
.fg-emoji-picker .fg-emoji-picker-categories {
  background-color: var(--bs-light) !important;
}
.fg-emoji-picker .fg-emoji-picker-categories li.active {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
}
.fg-emoji-picker .fg-emoji-picker-categories a:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
}

.fg-emoji-picker-grid > li:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.2) !important;
}

a.fg-emoji-picker-close-button {
  background-color: var(--bs-light) !important;
}

.fg-emoji-picker-categories svg {
  fill: var(--bs-body-color) !important;
}

.fg-emoji-picker-grid > li {
  background-color: var(--bs-card-bg) !important;
}

.ml-44 {
  margin-right: 44px;
}

.side-menu {
  min-width: 75px;
  max-width: 75px;
  height: 100vh;
  min-height: 570px;
  background-color: var(--bs-sidebar-bg);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  z-index: 9;
  border-left: 1px solid var(--bs-sidebar-bg);
  padding: 0 8px;
}
@media (max-width: 991.98px) {
  .side-menu {
    position: fixed;
    bottom: 0;
    height: 60px;
    min-width: 100%;
    min-height: auto;
    border-top: 1px solid var(--bs-sidebar-bg);
  }
}
.side-menu .navbar-brand-box {
  text-align: center;
}
.side-menu .navbar-brand-box svg {
  fill: rgba(var(--bs-primary-rgb), 1);
}
@media (max-width: 991.98px) {
  .side-menu .navbar-brand-box {
    display: none;
  }
}
.side-menu .navbar-brand-box .logo {
  line-height: 70px;
}
.side-menu .navbar-brand-box .logo-dark {
  display: block;
}
.side-menu .navbar-brand-box .logo-light {
  display: none;
}

.sidemenu-navigation {
  height: 100%;
}
@media (max-width: 991.98px) {
  .sidemenu-navigation {
    width: 100%;
  }
  .sidemenu-navigation .tooltip {
    display: none;
  }
}

.side-menu-nav {
  height: 100%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (max-width: 991.98px) {
  .side-menu-nav {
    -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
            justify-content: space-between !important;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.side-menu-nav .nav-item {
  margin: 7px 0;
  display: block;
  width: 100%;
}
@media (max-width: 991.98px) {
  .side-menu-nav .nav-item {
    -ms-flex-preferred-size: 0;
        flex-basis: 0;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    margin: 5px 0;
  }
}
.side-menu-nav .nav-item .nav-link {
  text-align: center;
  font-size: 28px;
  color: var(--bs-sidebar-menu-item-color);
  width: 100%;
  height: 56px;
  line-height: 56px;
  border-radius: 0;
  padding: 0;
  position: relative;
}
.side-menu-nav .nav-item .nav-link i {
  vertical-align: middle;
}
.side-menu-nav .nav-item .nav-link::before {
  position: absolute;
  content: "";
  height: 20px;
  width: 2px;
  left: -8px;
  top: 18px;
}
@media (max-width: 991.98px) {
  .side-menu-nav .nav-item .nav-link::before {
    width: 20px;
    height: 2px;
    left: auto;
    top: -5px;
    right: 50%;
    -webkit-transform: translateX(50%);
            transform: translateX(50%);
  }
}
@media (max-width: 991.98px) {
  .side-menu-nav .nav-item .nav-link {
    font-size: 20px;
    width: 48px;
    height: 48px;
    line-height: 48px;
    margin: 0px auto;
  }
}
.side-menu-nav .nav-item .nav-link.active {
  background-color: transparent;
  color: rgba(var(--bs-primary-rgb), 1);
}
.side-menu-nav .nav-item .nav-link.active::before {
  background-color: rgba(var(--bs-primary-rgb), 1);
}
.side-menu-nav .nav-item.show > .nav-link {
  color: rgba(var(--bs-primary-rgb), 1);
}
.side-menu-nav .profile-user {
  height: 36px;
  width: 36px;
  background-color: var(--bs-gray-300);
  padding: 3px;
}

.light-mode {
  display: none;
}

body[data-layout-mode=dark] .light-mode {
  display: inline-block;
}
body[data-layout-mode=dark] .dark-mode {
  display: none;
}

body[data-layout-mode=dark] .side-menu-nav .nav-item .nav-link.light-dark .bx-moon:before {
  content: "\eb90";
}
body[data-layout-mode=dark] .light-mode {
  display: inline-block;
}
body[data-layout-mode=dark] .dark-mode {
  display: none;
}

.chat-leftsidebar {
  height: calc(100vh - 60px);
  position: relative;
  background-color: var(--bs-sidebar-sub-bg);
  -webkit-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
          box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
}
@media (min-width: 992px) {
  .chat-leftsidebar {
    min-width: 300px;
    max-width: 300px;
    height: 100vh;
  }
}
.chat-leftsidebar .user-status-box {
  background-color: #f6f6f9;
  padding: 8px;
  border-radius: 8px;
  text-align: center;
  margin-top: 16px;
  display: block;
}
.chat-leftsidebar .user-status-box .chat-user-img {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.chat-search-box .form-control {
  border: 0;
}
.chat-search-box .search-icon-btn {
  font-size: 16px;
  position: absolute;
  left: 13px;
  top: 0;
}

.chat-room-list {
  max-height: calc(100vh - 130px);
}
@media (max-width: 991.98px) {
  .chat-room-list {
    height: calc(100vh - 190px);
  }
}

.chat-group-list {
  height: calc(100vh - 140px);
}
@media (max-width: 991.98px) {
  .chat-group-list {
    height: calc(100vh - 198px);
  }
}

.chat-list {
  margin: 0;
}
.chat-list li.active a {
  background-color: rgba(var(--bs-primary-rgb), 1);
  color: #fff;
}
.chat-list li.active a span.avatar-title {
  color: #fff !important;
}
.chat-list li.active a span.avatar-title.bg-light {
  background-color: rgba(246, 246, 249, 0.25) !important;
}
.chat-list li.active a .badge {
  background-color: rgba(246, 246, 249, 0.25) !important;
  color: #fff !important;
}
.chat-list li.active a .bg-primary {
  background-color: rgba(255, 255, 255, 0.25) !important;
}
.chat-list li a {
  display: block;
  padding: 5px 24px;
  color: var(--bs-gray-700);
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  font-size: 14px;
}
.chat-list li .chat-user-message {
  font-size: 14px;
}
.chat-list li .unread-msg-user {
  font-weight: 600;
}
.chat-list li.typing .chat-user-message {
  color: rgba(var(--bs-primary-rgb), 1);
  font-weight: 500;
}
.chat-list li.typing .chat-user-message .dot {
  background-color: rgba(var(--bs-primary-rgb), 1);
}
.chat-list li .unread-message {
  position: absolute;
  display: inline-block;
  left: 24px;
  right: auto;
  top: 33px;
}
.chat-list li .unread-message .badge {
  line-height: 16px;
  font-weight: 600;
  font-size: 10px;
}

.chat-user-img {
  position: relative;
}
.chat-user-img .user-status {
  width: 10px;
  height: 10px;
  background-color: #adb5bd;
  border-radius: 50%;
  border: 2px solid var(--bs-card-bg);
  position: absolute;
  left: 0;
  right: auto;
  bottom: 0;
}
.chat-user-img.online .user-status {
  background-color: #06D6A0;
}
.chat-user-img.away .user-status {
  background-color: #FFD166;
}

.contact-list li {
  cursor: pointer;
  padding: 8px 24px;
}

.contact-list-title {
  padding: 6px 24px;
  color: rgba(var(--bs-primary-rgb), 1);
  font-weight: 500;
  position: relative;
  font-size: 12px;
}
.contact-list-title:after {
  content: "";
  height: 1px;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 50px;
  left: 0;
  background-color: var(--bs-border-color);
}

.chat-call-list, .chat-bookmark-list {
  max-height: calc(100vh - 68px);
}
@media (max-width: 991.98px) {
  .chat-call-list, .chat-bookmark-list {
    height: calc(100vh - 125px);
  }
}
.chat-call-list li, .chat-bookmark-list li {
  position: relative;
  padding: 10px 24px;
  border-bottom: 1px solid var(--bs-border-color);
}
.chat-call-list li:last-child, .chat-bookmark-list li:last-child {
  border-bottom: 0;
}

.profile-user {
  position: relative;
  display: inline-block;
}
.profile-user .profile-photo-edit {
  position: absolute;
  left: 0;
  right: auto;
  bottom: 0;
  cursor: pointer;
}
.profile-user .user-profile-img {
  -o-object-fit: cover;
     object-fit: cover;
}
.profile-user .profile-img-file-input {
  display: none;
}

.theme-btn-list.theme-color-list .form-check .form-check-input:checked + .form-check-label::before {
  color: #fff;
}
.theme-btn-list .form-check {
  padding: 0;
}
.theme-btn-list .form-check .form-check-input {
  display: none;
}
.theme-btn-list .form-check .form-check-label {
  position: relative;
}
.theme-btn-list .form-check .form-check-input:checked + .form-check-label::before {
  content: "\e9a4";
  font-family: boxicons !important;
  position: absolute;
  top: 50%;
  right: 50%;
  -webkit-transform: translateX(50%) translateY(-50%);
          transform: translateX(50%) translateY(-50%);
  color: rgba(var(--bs-primary-rgb), 1);
  font-size: 16px;
  z-index: 1;
}
.theme-btn-list .form-check .form-check-input:checked + .form-check-label.light-background::before {
  color: rgba(var(--bs-primary-rgb), 1);
}
.theme-btn-list.theme-btn-list-img .theme-btn {
  background-color: transparent !important;
}

.theme-btn {
  cursor: pointer;
  border: 1px solid var(--bs-gray-400);
}

.user-chat {
  background: url("../images/bg-pattern/pattern-05.png");
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  position: relative;
  background-color: var(--bs-body-bg);
}
.user-chat .user-chat-overlay {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  background-color: transparent;
  opacity: 0.1;
}
@media (max-width: 991.98px) {
  .user-chat {
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    visibility: hidden;
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
    z-index: 99;
  }
  .user-chat.user-chat-show {
    visibility: visible;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
.user-chat .chat-content {
  position: relative;
}
.user-chat.user-chat-show .chat-welcome-section {
  display: none;
}
@media (min-width: 992px) {
  .user-chat.user-chat-show .chat-content {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }
}

.chat-welcome-section {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100vh;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: relative;
}
@media (max-width: 991.98px) {
  .chat-welcome-section {
    display: none;
  }
}

.copyclipboard-alert {
  position: absolute;
  bottom: 0;
  right: 50%;
  -webkit-transform: translateX(50%);
          transform: translateX(50%);
}

.user-chat-topbar {
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid var(--bs-border-color);
  -webkit-backdrop-filter: blur(7px);
          backdrop-filter: blur(7px);
}
@media (max-width: 991.98px) {
  .user-chat-topbar {
    position: fixed;
    background-color: rgba(255, 255, 255, 0.8);
  }
}
.user-chat-topbar .topbar-bookmark {
  position: absolute;
  bottom: -51px;
  right: 0;
  left: 0;
  border-radius: 0;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-links {
  color: #cc8f00;
  font-size: 14px;
  padding: 1px 16px;
  border-left: 1px solid rgba(255, 209, 102, 0.7);
  white-space: nowrap;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-links:first-child {
  padding-right: 4px;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow-x: auto;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar {
  -webkit-appearance: none;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar:vertical {
  width: 12px;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar:horizontal {
  height: 5px;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar-thumb {
  background-color: rgba(52, 58, 64, 0.1);
  border-radius: 10px;
  border: 2px solid transparent;
}
.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar-track {
  border-radius: 10px;
}
.user-chat-topbar .topbar-bookmark .btn-close {
  padding: 12px 20px;
}

.call-close-btn {
  -webkit-box-shadow: 0px 0px 0 6px var(--bs-card-bg);
          box-shadow: 0px 0px 0 6px var(--bs-card-bg);
}

.user-chat-nav .nav-btn {
  height: 40px;
  width: 40px;
  line-height: 40px;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
  font-size: 22px;
  color: var(--bs-gray-600);
}
@media (max-width: 575.98px) {
  .user-chat-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
        -ms-flex-pack: end;
            justify-content: flex-end;
  }
}

.replymessage-block {
  padding: 12px 20px;
  font-size: 14px;
  margin-bottom: 8px;
  text-align: right;
  border-radius: 4px;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  border-right: 2px solid rgba(var(--bs-primary-rgb), 1);
}
.replymessage-block .conversation-name {
  color: rgba(var(--bs-primary-rgb), 1);
  font-size: 15px;
}

.chat-conversation {
  height: calc(100vh - 94px);
}
@media (max-width: 991.98px) {
  .chat-conversation {
    height: calc(100vh - 80px);
    margin-bottom: 78px;
  }
}
.chat-conversation .simplebar-content-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.chat-conversation .simplebar-content-wrapper .simplebar-content {
  margin-top: auto;
}
.chat-conversation .chat-conversation-list {
  margin-top: 90px;
  padding-top: 10px;
  margin-bottom: 0;
}
.chat-conversation .chat-conversation-list > li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.chat-conversation li:last-of-type .conversation-list {
  margin-bottom: 0;
}
.chat-conversation .chat-list.left .check-message-icon {
  display: none;
}
.chat-conversation .chat-list .message-box-drop {
  visibility: hidden;
}
.chat-conversation .chat-list:hover .message-box-drop {
  visibility: visible;
}
.chat-conversation .chat-avatar {
  margin: 0 0 0 16px;
}
.chat-conversation .chat-avatar img {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}
.chat-conversation .chat-day-title {
  position: relative;
  text-align: center;
  margin-bottom: 24px;
  margin-top: 12px;
  width: 100%;
}
.chat-conversation .chat-day-title .title {
  background-color: #fff;
  position: relative;
  font-size: 13px;
  z-index: 1;
  padding: 6px 12px;
  border-radius: 5px;
}
.chat-conversation .chat-day-title:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  right: 0;
  left: 0;
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  top: 10px;
}
.chat-conversation .chat-day-title .badge {
  font-size: 12px;
}
.chat-conversation .conversation-list {
  margin-bottom: 24px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: relative;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  max-width: 80%;
}
@media (max-width: 575.98px) {
  .chat-conversation .conversation-list {
    max-width: 90%;
  }
}
.chat-conversation .conversation-list .ctext-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 10px;
}
.chat-conversation .conversation-list .ctext-content {
  word-wrap: break-word;
  word-break: break-word;
  color: var(--bs-chat-text-color);
}
.chat-conversation .conversation-list .ctext-wrap-content {
  padding: 12px 20px;
  background-color: var(--bs-chat-primary-bg);
  position: relative;
  border-radius: 3px;
  -webkit-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
          box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
}
@media (max-width: 575.98px) {
  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .attached-file-avatar {
    display: none;
  }
  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .dropdown .dropdown-toggle {
    display: block;
  }
}
.chat-conversation .conversation-list .conversation-name {
  font-weight: 500;
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 8px;
}
.chat-conversation .conversation-list .dropdown .dropdown-toggle {
  font-size: 18px;
  padding: 4px;
  color: #797c8c;
}
@media (max-width: 575.98px) {
  .chat-conversation .conversation-list .dropdown .dropdown-toggle {
    display: none;
  }
}
.chat-conversation .conversation-list .chat-time {
  font-size: 12px;
  margin-top: 4px;
  text-align: left;
}
.chat-conversation .conversation-list .message-img {
  border-radius: 0.2rem;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 8px;
}
.chat-conversation .conversation-list .message-img .message-img-list {
  position: relative;
}
.chat-conversation .conversation-list .message-img img {
  max-width: 150px;
}
.chat-conversation .conversation-list .message-img .message-img-link {
  position: absolute;
  left: 10px;
  right: auto;
  bottom: 10px;
}
.chat-conversation .conversation-list .message-img .message-img-link li > a {
  font-size: 18px;
  color: #fff;
  display: inline-block;
  line-height: 20px;
  width: 26px;
  height: 24px;
  border-radius: 3px;
  background-color: rgba(52, 58, 64, 0.7);
  text-align: center;
}
.chat-conversation .right {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.chat-conversation .right .chat-avatar {
  -webkit-box-ordinal-group: 4;
      -ms-flex-order: 3;
          order: 3;
  margin-left: 0px;
  margin-right: 16px;
}
.chat-conversation .right .chat-time {
  text-align: right;
  color: #797c8c;
}
.chat-conversation .right .conversation-list {
  text-align: left;
}
.chat-conversation .right .conversation-list .ctext-wrap {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content {
  -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
          order: 2;
  background-color: rgba(var(--bs-primary-rgb), 0.23);
  text-align: left;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block {
  background-color: rgba(255, 255, 255, 0.5);
  border-color: rgba(var(--bs-primary-rgb), 1);
  color: #495057;
}
.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block .conversation-name {
  color: rgba(var(--bs-primary-rgb), 1);
}
.chat-conversation .right .conversation-list .conversation-name {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.chat-conversation .right .conversation-list .conversation-name .check-message-icon {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}
.chat-conversation .right .conversation-list .conversation-name .time {
  -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
          order: 2;
}
.chat-conversation .right .conversation-list .conversation-name .name {
  -webkit-box-ordinal-group: 4;
      -ms-flex-order: 3;
          order: 3;
}
.chat-conversation .right .conversation-list .dropdown {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}
.chat-conversation .right .dot {
  background-color: #343a40;
}

.videocallModal .modal-content {
  min-height: 450px;
  overflow: hidden;
}
@media (max-width: 575.98px) {
  .videocallModal .modal-content {
    min-height: 350px;
  }
}

.videocallModal-bg {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.user-chat-remove {
  background-color: rgba(var(--bs-primary-rgb), 1);
  color: #fff;
  border-radius: 3px;
  line-height: 1;
}

.chat-input-section {
  background-color: var(--bs-footer-bg);
  border-top: 1px solid var(--bs-border-color);
  -webkit-backdrop-filter: blur(7px);
          backdrop-filter: blur(7px);
  position: relative;
  z-index: 1;
}
@media (max-width: 991.98px) {
  .chat-input-section {
    position: fixed;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 1;
  }
}
.chat-input-section .chat-input-collapse {
  position: absolute;
  bottom: 92px;
  right: 0;
  left: 0;
  border-top: 1px solid var(--bs-border-color);
  overflow: hidden;
}
@media (max-width: 991.98px) {
  .chat-input-section .chat-input-collapse {
    bottom: 74px;
  }
}
.chat-input-section .chat-input-feedback {
  display: none;
  position: absolute;
  top: -18px;
  right: 16px;
  font-size: 12px;
  color: #EF476F;
}
.chat-input-section .show {
  display: block;
}
.chat-input-section .replyCollapse {
  z-index: 1;
}

.file_Upload {
  background-color: var(--bs-card-bg);
  border-top: 1px solid var(--bs-border-color);
  padding: 16px 24px;
}
.file_Upload .card {
  margin-bottom: 0;
  border-color: rgba(var(--bs-primary-rgb), 1) !important;
}

.replyCard, .file_Upload {
  position: absolute;
  right: 0;
  left: 0;
  border-top: 1px solid var(--bs-border-color);
  overflow: hidden;
  opacity: 0;
  bottom: 0;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}
@media (max-width: 991.98px) {
  .replyCard, .file_Upload {
    bottom: -12px;
  }
}
.replyCard.show, .file_Upload.show {
  -webkit-transform: translateY(-92px);
          transform: translateY(-92px);
  opacity: 1;
}
@media (max-width: 991.98px) {
  .replyCard.show, .file_Upload.show {
    -webkit-transform: translateY(-86px);
            transform: translateY(-86px);
  }
}

.contact-modal-list .contact-list li {
  margin: 2px 0px;
}
.contact-modal-list .contact-list li.selected {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.chat-input-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.chat-input-links .links-list-item > .btn {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
  font-size: 22px;
  width: 43px;
  height: 43px;
}
.chat-input-links .links-list-item > .btn.btn-link {
  color: var(--bs-gray-600);
}

.animate-typing .dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  margin-left: -1px;
  background: #343a40;
  -webkit-animation: wave 1.3s linear infinite;
          animation: wave 1.3s linear infinite;
  opacity: 0.6;
}
.animate-typing .dot:nth-child(2) {
  -webkit-animation-delay: -1.1s;
          animation-delay: -1.1s;
}
.animate-typing .dot:nth-child(3) {
  -webkit-animation-delay: -0.9s;
          animation-delay: -0.9s;
}

@-webkit-keyframes wave {
  0%, 60%, 100% {
    -webkit-transform: initial;
            transform: initial;
  }
  30% {
    -webkit-transform: translateY(-5px);
            transform: translateY(-5px);
  }
}

@keyframes wave {
  0%, 60%, 100% {
    -webkit-transform: initial;
            transform: initial;
  }
  30% {
    -webkit-transform: translateY(-5px);
            transform: translateY(-5px);
  }
}
@-moz-document url-prefix() {
  .user-chat-topbar, .chat-input-section {
    background-color: #f2f2f2 !important;
  }
}
.user-profile-sidebar {
  height: 100vh;
  background-color: var(--bs-card-bg);
  display: none;
  min-width: 380px;
  max-width: 380px;
}
@media (min-width: 992px) {
  .user-profile-sidebar {
    border-right: 4px solid var(--bs-border-color);
  }
}
@media (max-width: 1199.98px) {
  .user-profile-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
  }
}
@media (max-width: 575.98px) {
  .user-profile-sidebar {
    min-width: 100%;
  }
}

.user-profile-img {
  position: relative;
}
.user-profile-img .overlay-content {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  background: -webkit-gradient(linear, right top, right bottom, color-stop(10%, rgba(0, 0, 0, 0.5)), color-stop(60%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.5)));
  background: linear-gradient(-180deg, rgba(0, 0, 0, 0.5) 10%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.5) 100%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.user-profile-img .user-name {
  font-size: 16px;
  color: #fff;
}
.user-profile-img .profile-img {
  width: 100%;
  height: 250px;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (max-width: 991px) {
  .user-profile-img .profile-img {
    height: 160px;
  }
}
.user-profile-img .profile-foreground-img-file-input {
  display: none;
}
.user-profile-img .profile-photo-edit {
  cursor: pointer;
}

.user-profile-image {
  -o-object-fit: cover;
     object-fit: cover;
}

.user-profile-desc {
  height: calc(100vh - 285px);
}
@media (max-width: 991.98px) {
  .user-profile-desc {
    height: calc(100vh - 194px);
  }
}

.profile-desc {
  height: calc(100vh - 285px);
}
@media (max-width: 991.98px) {
  .profile-desc {
    height: calc(100vh - 330px);
  }
}

.profile-media-img {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
}
.profile-media-img .media-img-list {
  position: relative;
}
.profile-media-img .media-img-list a {
  display: block;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}
.profile-media-img .media-img-list a img {
  width: 76px;
  height: 76px;
  -o-object-fit: cover;
     object-fit: cover;
}
.profile-media-img .media-img-list a .bg-overlay {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #fff;
}
.profile-media-img .media-img-list .image-remove {
  position: absolute;
  top: 0;
  left: 0;
  color: #fff;
  background: rgba(52, 58, 64, 0.7);
  width: 18px;
  height: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 2px;
  margin: 4px;
}

.favourite-btn.active .bx-heart {
  color: #EF476F;
}
.favourite-btn.active .bx-heart:before {
  content: "\ed36";
}

.edit-input.form-control[readonly] {
  padding: 0;
  font-weight: 500;
}
.edit-input.form-control[readonly]:focus {
  border-color: transparent;
}

.user-setting {
  height: calc(100vh - 288px);
}
@media (max-width: 991.98px) {
  .user-setting {
    height: calc(100vh - 320px);
  }
}

.auth-logo .logo {
  margin: 0px auto;
}
.auth-logo .logo-dark {
  display: block;
}
.auth-logo .logo-light {
  display: none;
}

.auth-bg {
  background-color: rgba(var(--bs-primary-rgb), 1);
  min-height: 100vh;
  background-size: cover;
  background-position: center;
}

.auth-logo-section {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 992px) {
  .auth-logo-section {
    height: 100vh;
  }
}
@media (max-width: 991.98px) {
  .auth-logo-section {
    text-align: center;
  }
}

.authentication-page-content {
  height: calc(100% - 48px);
  background-color: #fff;
  border-radius: 16px;
  margin: 24px;
}

.auth-img {
  position: relative;
}
@media (min-width: 992px) and (max-width: 1549.98px) {
  .auth-img {
    max-width: 220%;
  }
}
@media (min-width: 1550px) {
  .auth-img {
    max-width: 200%;
  }
}
@media (max-width: 991.98px) {
  .auth-img {
    display: none;
  }
}

.auth-pass-inputgroup input[type=text] + .btn .ri-eye-fill:before {
  content: "\ec80";
}

.signin-other-title {
  position: relative;
}
.signin-other-title:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  right: 0;
  left: 0;
  background-color: var(--bs-border-color);
  top: 10px;
}
.signin-other-title .title {
  display: inline-block;
  position: relative;
  z-index: 9;
  background-color: var(--bs-card-bg);
  padding: 2px 16px;
}
/*# sourceMappingURL=app-rtl.min.css.map */
