import LeftMenu from "../../components/LeftMenu";
import { useState, useEffect, useRef, useContext } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import styles from "./report.module.css";
import { SlCalender } from "react-icons/sl";
import { BsCalendarDate } from "react-icons/bs";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { AuthContext } from "../../context/AuthContext";
const Report = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const { currentUser } = useContext(AuthContext);
  // Date range state: array with two elements [startDate, endDate]
  const [dateRange, setDateRange] = useState([new Date().toISOString(), new Date().toISOString()]);
  const [fromDate, toDate] = dateRange; // Destructure for easy use in payloads

  const [activeTab, setActiveTab] = useState("customer-report");
  const [fetchDataFn, setFetchDataFn] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const datePickerRef = useRef(null);

  useEffect(() => {
    if (location.pathname === "/report") {
      navigate("/report/customer-report", { replace: true });
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    if (location.pathname.includes("broadcast-report")) {
      setActiveTab("broadcast-report");
    } else if (location.pathname.includes("agent-report")) {
      setActiveTab("agent-report");
    } else if (location.pathname.includes("customer-report")) {
      setActiveTab("customer-report");
    } else if (location.pathname.includes("dashboard")) {
      setActiveTab("dashboard");
    }
  }, [location.pathname]);

  // Close DatePicker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target)) {
        setShowDatePicker(false);
      }
    };

    if (showDatePicker) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showDatePicker]);



  return (
    <div className={`${styles.pageContainer}`}>
      <LeftMenu />
      <div className={`d-flex flex-column justify-content-start align-items-center ${styles.main}`}>
        <header className={`w-100 p-3 ${styles.header}`}>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center justify-content-between w-100" style={{ height: "40px" }}>
              <h4 className="fw-bold">Reports</h4>
              {activeTab !== "dashboard" && <div className={`d-none d-md-flex w-50 justify-content-center align-items-center p-2 ${styles.searchBar}`}>
                <div className="d-flex  flex-md-row w-100 align-items-center gap-2">
                  <div className="d-flex  w-100 w-md-75">
                    {/* <label className="mb-1 fw-bold">Select Date Range</label> */}
                    <DatePicker
                      selectsRange
                      startDate={fromDate}
                      endDate={toDate}
                      onChange={(update) => {
                        setDateRange(update);
                      }}
                      isClearable={true}
                      maxDate={new Date()}
                      className="form-control"
                      placeholderText="Select Date Range"
                    />
                    <span className="text-danger mb-2 ms-1 fw-1">*</span>
                  </div>
                  <div className="w-50">
                    <button className="btn btn-primary w-100" onClick={() => fetchDataFn && fetchDataFn()}>
                      Search
                    </button>
                  </div>
                </div>
              </div>}

            </div>

            {/* Calendar Icon (Visible only on small screens) */}
            {activeTab !== "dashboard" && <div className="d-block d-md-none position-relative">
              <BsCalendarDate
                size={24}
                role="button"
                onClick={() => setShowDatePicker(!showDatePicker)}

              />
              {showDatePicker && (
                <div ref={datePickerRef} className={`position-absolute bg-white shadow p-3 rounded ${styles.datePickerContainer}`}>
                  {/* <label className="fw-bold">Select Date Range</label> */}
                  <DatePicker
                    selectsRange
                    startDate={fromDate}
                    endDate={toDate}
                    onChange={(update) => {
                      if (update) {
                        const [start, end] = update;
                        setDateRange([
                          start ? new Date(start.getFullYear(), start.getMonth(), start.getDate(), 0, 0, 0, 0) : null, // Clone start date at 00:00:00
                          end ? new Date(end.getFullYear(), end.getMonth(), end.getDate(), 23, 59, 59, 999) : null, // Clone end date at 23:59:59
                        ]);
                      } else {
                        setDateRange([null, null]); // Reset if cleared
                      }
                    }}
                    isClearable={true}
                    className="form-control"
                    maxDate={new Date()}
                    placeholderText="Select Date Range"
                  />
                  <button className="btn btn-primary w-100 mt-2" onClick={() => { setShowDatePicker(false); fetchDataFn && fetchDataFn(); }}>
                    Search
                  </button>
                </div>
              )}
            </div>}

          </div>

          {/* Navigation Tabs */}
          <ul className={`d-flex align-items-center list-unstyled ${styles.navTabs}`}>
            <li
              className={`me-4 px-3 py-2 ${activeTab === "customer-report" ? styles.activeTab : styles.inactiveTab}`}
              role="button"
              onClick={() => {

                navigate("/report/customer-report");
              }}
            >
              Customer Report
            </li>
            <li
              className={`me-4 px-3 py-2 ${activeTab === "broadcast-report" ? styles.activeTab : styles.inactiveTab}`}
              role="button"
              onClick={() => {
                navigate("/report/broadcast-report");
              }}
            >
              Broadcast Report
            </li>
            <li
              className={`me-4 px-3 py-2 ${activeTab === "agent-report" ? styles.activeTab : styles.inactiveTab}`}
              role="button"
              onClick={() => {

                navigate("/report/agent-report");
              }}
            >
              Agent Report
            </li>

          </ul>
        </header>

        {/* Search Section (Always Visible on Large Screens) */}


        <Outlet context={{ fromDate, toDate, setFetchDataFn }} />
      </div>
    </div>
  );
};

export default Report;
