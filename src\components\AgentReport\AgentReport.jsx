import { useState, useEffect, useContext } from "react";
import { AuthContext } from "../../context/AuthContext";
import axios from "axios";
import { BASE_URL2 } from "../../api/api";
import { useOutletContext } from "react-router-dom";
import styles from "./agentReport.module.css";
import { CircularProgress } from "@mui/material";
import { useMaskNo } from "../../customHooks/useMaskNo";
import { formatLocalDate } from "../../utils/Utils";
const AgentReport = () => {
  const { currentUser } = useContext(AuthContext);
  const maskNo = useMaskNo();
  const [reportData, setReportData] = useState({});
  const [loading, setLoading] = useState(false);
  const { fromDate, toDate, setFetchDataFn } = useOutletContext();

  useEffect(() => {
    if (
      currentUser.user_id &&
      currentUser.parent_token &&
      currentUser.parent_id &&
      fromDate &&
      toDate
    ) {
      fetchAgentData();
    }
    
  }, [currentUser]);

  useEffect(() => {
    if (setFetchDataFn) setFetchDataFn(() => fetchAgentData);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setFetchDataFn, fromDate, toDate]);

  const fetchAgentData = async () => {
    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "daywise_agent_report",
      user_type: currentUser.user_type,
      agent_id:currentUser.user_id,
      brand_number: `${currentUser.brand_number}`,
      from_date: formatLocalDate(fromDate),
      to_date: formatLocalDate(toDate),
    };

    setLoading(true);

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_report`, payload);
      if (data.success) {
        const groupedData = groupByAgent(data.data);
        setReportData(groupedData);
      } else {
        setReportData({});
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // Group data by agent name
  const groupByAgent = (data) => {
  return data.reduce((acc, item) => {
    const id = item.agent_id;
    const name = item.agentname?.trim() || 'Admin';
    const agentKey = id !== null && id !== undefined ? `${name} (${id})` : 'Admin';

    if (!acc[agentKey]) {
      acc[agentKey] = [];
    }

    acc[agentKey].push(item);
    return acc;
  }, {});
};


  // Inner component: renders each row with collapsible customer details
  const AgentRow = ({ report }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const toggleExpand = () => setIsExpanded(!isExpanded);

    return (
      <>
        <tr>
          <td>{report.date}</td>
          <td>{report.totalnumber}</td>
          <td>{report.replynumcount}</td>
          <td>{report.totalnumber - report.replynumcount}</td>
          <td>{report.totalrepeatnumber}</td>
          <td>
            {report.numbers && report.numbers.length > 0 && (
              <button className="btn btn-link" onClick={toggleExpand}>
                {isExpanded
                  ? "Hide Customer Details"
                  : `View ${report.numbers.length} Customer Details`}
              </button>
            )}
          </td>
        </tr>
        {isExpanded && (
          <tr>
            <td colSpan="6">
              <div className={styles.collapsibleContent}>
                {report.numbers.map((customer, idx) => (
                  <div key={idx} className={styles.customerDetail}>
                    <strong>{customer.name}</strong> (<em>{currentUser.user_type === "admin" ? customer.mobile: maskNo(customer.mobile)}</em>)
                    <br />
                    <small>
                    <strong>Start:</strong>   {customer.starttime || "N/A"} <strong>| End:</strong>{" "}
                      {customer.endtime || "N/A"}<strong>| Duration:</strong>{" "}
                      {customer.duration || "0"}
                    </small>
                  </div>
                ))}
              </div>
            </td>
          </tr>
        )}
      </>
    );
  };

  return (
    <div className={`w-100 ${styles.scrollContainer}`}>
      {loading ? (
        <div className="text-center">
          <CircularProgress />
        </div>
      ) : Object.keys(reportData).length === 0 ? (
        <div className="text-center fw-bold">No data present for selected date range</div>
      ) : (
        Object.entries(reportData).map(([agentName, reports]) => (
          <div
            key={agentName}
            className={`w-100 p-3 d-flex flex-column justify-content-center align-items-center ${styles.reportCard}`}
          >
            {/* Agent Name */}
            <h3 className="text-center">{agentName}</h3>

            {/* Table for each Agent */}
            <div style={{ overflowX: "auto", width: "100%" }}>
              <table className="table w-100" style={{ minWidth: "600px" }}>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Total Chats</th>
                    <th>Read Chats</th>
                    <th>Unread Chats</th>
                    <th>Repeated Chats</th>
                    <th>Customer Details</th>
                  </tr>
                </thead>
                <tbody>
                  {reports
                    .sort((a, b) => new Date(a.date) - new Date(b.date))
                    .map((item) => (
                      <AgentRow key={item._id} report={item} />
                    ))}
                </tbody>
              </table>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default AgentReport;
