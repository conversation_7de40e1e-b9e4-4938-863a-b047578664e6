const OnlineUsersCount = () => {
    const countryWiseData = [
        { country: 'India', users: 45, flag: '🇮🇳' },
        { country: 'USA', users: 23, flag: '🇺🇸' },
        { country: 'UK', users: 12, flag: '🇬🇧' },
        { country: 'Canada', users: 8, flag: '🇨🇦' },
        { country: 'Australia', users: 5, flag: '🇦🇺' },
    ];

    const totalLiveUsers = countryWiseData.reduce((sum, item) => sum + item.users, 0);

    return (
        <div className="bg-white p-3 shadow w-100 w-lg-50" style={{ borderRadius: ".8rem" }}>
            <h6 className="text-center mt-2 mb-4">Live Users</h6>
            <div className="d-flex flex-column h-100">
                {/* Total Live Users */}
                <div className="text-center mb-4">
                    <div className="d-flex justify-content-center align-items-center mb-3">
                        <div className="bg-success rounded-circle d-flex justify-content-center align-items-center" 
                             style={{ width: "70px", height: "70px" }}>
                            <span className="text-white fw-bold fs-3">{totalLiveUsers}</span>
                        </div>
                    </div>
                    <p className="text-muted mb-0 fw-medium">Total Live Users</p>
                </div>

                {/* Country-wise breakdown */}
                <div className="flex-grow-1" style={{ maxHeight: "180px", overflowY: "auto" }}>
                    <h6 className="text-center mb-3" style={{ fontSize: "14px", color: "maroon", fontWeight: "600" }}>
                        Country Wise
                    </h6>
                    {countryWiseData.map((item, index) => (
                        <div key={index} className="d-flex justify-content-between align-items-center mb-3 p-3" 
                             style={{ 
                                 backgroundColor: "#f8f9fa", 
                                 borderRadius: "8px",
                                 border: "1px solid #e9ecef"
                             }}>
                            <div className="d-flex align-items-center">
                                <span className="me-3" style={{ fontSize: "20px" }}>{item.flag}</span>
                                <span style={{ fontSize: "14px", fontWeight: "600", color: "#495057" }}>
                                    {item.country}
                                </span>
                            </div>
                            <div className="d-flex align-items-center">
                                <div className="bg-primary rounded-circle d-flex justify-content-center align-items-center me-3" 
                                     style={{ width: "28px", height: "28px" }}>
                                    <span className="text-white" style={{ fontSize: "12px", fontWeight: "bold" }}>
                                        {item.users}
                                    </span>
                                </div>
                                <div className="progress" style={{ width: "70px", height: "8px" }}>
                                    <div className="progress-bar bg-primary" 
                                         style={{ 
                                             width: `${(item.users / totalLiveUsers) * 100}%`,
                                             borderRadius: "4px"
                                         }}>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default OnlineUsersCount;
