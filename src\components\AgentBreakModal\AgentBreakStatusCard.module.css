.cardContainer {
  position: fixed;
  top: 1rem;
  right: 25vw;
  z-index: 1050;
}

.card {
  background-color: #ffffff;
  padding: 1rem;
  border-radius: 1rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  min-width: 230px;
  max-width: 300px;
  transition: all 0.3s ease;
}

.pill {
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 9999px;
  padding: 0.4rem 1rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  width: fit-content;
  max-width: 100%;
  white-space: nowrap;
}
