import React from "react";
import { IoClose } from "react-icons/io5";
import { FaFileAudio, FaWpforms } from "react-icons/fa";
import { IoDocumentText } from "react-icons/io5";
import { FaLocationDot } from "react-icons/fa6";
import { RiContactsFill } from "react-icons/ri";
import styles from './replyPreview.module.css'
const ReplyPreview = ({ reply, onClose, replied }) => {
    if (!reply) return null;
        
    const { req_from:user, message_type:contentType, message_content:content, file_url:data } = reply;
    
    return (
        <div className={`${styles.replyContainer} 
        d-flex justify-content-between ${user === "USER" ? styles.userMessage : styles.agentMessage}`}
            // style={replied && user !== "USER" ? { backgroundColor: "lightgreen" } : { backgroundColor: "inherit" }}
        >
            <div className={`flex-grow-1 d-flex  gap-2 
                ${(contentType !== "TEXT" ? 'flex-row justify-content-between align-items-start' : 'flex-column')}`}>
                <div className="d-flex flex-column text-muted">
                    <span className={`${user === "USER" ? styles.userName : styles.agentName}`}>{user === "BOT_REPLY" ? "Bot" : user}</span>
                    {contentType !== 'TEXT' &&
                        <div style={{ fontSize: ".8rem" }}>{contentType}</div>}
                    {/* {contentType !== 'TEXT' &&
                        <div style={{ fontSize: ".8rem" }}>{content}</div>} */}
                   

                </div>

                {(contentType === "TEXT") &&

                    <span className={`${styles.replyText}`}>{content?.split("\n").map((line, index) => (
                        <React.Fragment key={index}>
                            {line
                                .split(/(\*[^*]\*|_[^_]_+|~[^~]~)/) // Splitting based on single delimiters
                                .map((part, partIndex) => {
                                    if (!part) return null;

                                    if (part.startsWith("*") && part.endsWith("*") && part.length > 2) {
                                        // Apply bold to the middle part and remove the surrounding '*'
                                        return (
                                            <strong key={partIndex}>
                                                {part.slice(1, -1)} {/* Remove first and last '*' */}
                                            </strong>
                                        );
                                    }
                                    if (part.startsWith("_") && part.endsWith("_") && part.length > 2) {
                                        // Apply italic to the middle part and remove the surrounding '_'
                                        return (
                                            <em key={partIndex}>
                                                {part.slice(1, -1)} {/* Remove first and last '_' */}
                                            </em>
                                        );
                                    }
                                    if (part.startsWith("~") && part.endsWith("~") && part.length > 2) {
                                        // Apply strikethrough to the middle part and remove the surrounding '~'
                                        return (
                                            <del key={partIndex}>
                                                {part.slice(1, -1)} {/* Remove first and last '~' */}
                                            </del>
                                        );
                                    }

                                    // In case the part is just normal text
                                    return <span key={partIndex}>{part}</span>;
                                })}
                            <br />
                        </React.Fragment>
                    ))}</span>
                }
                {contentType === "IMAGE" &&
                    <>
                        <img src={data} alt="image" style={{ width: "50px", height: "50px" }} />

                    </>
                }
                {contentType === "VIDEO" &&
                    <>
                        <video src={data} style={{ width: "75px", height: "75px" }} />

                       
                    </>
                }
                {contentType === "AUDIO" &&
                    <>
                        <FaFileAudio size={20} />
                        
                    </>
                }
                {
                   ( contentType === "DOCUMENT" || contentType === "document") &&
                    <>
                        <IoDocumentText size={20} />
                        
                    </>
                }
                {
                    contentType === "LOCATION" &&
                    <>
                        <FaLocationDot size={20} />

                    </>
                }
                {
                    contentType === "FLOW" && user !== "BOT_REPLY" &&
                    <>
                        <div className="d-flex align-items-center gap-2 mb-2">
                            <FaWpforms />
                            <span>Form Data</span>
                        </div>
                        {Object.entries(data?.content).splice(0, 1).map(
                            ([label, value], index) => (
                                <li key={index}>
                                    <strong>{label.replace(/_/g, " ")}: </strong> {value}
                                </li>
                            )
                        )}
                    </>
                }
                {
                    contentType === "FLOW" && user === "BOT_REPLY" &&

                    <div className="d-flex align-items-center gap-2 mb-2">
                        {data?.content}

                    </div>


                }
                {
                    contentType === "CONTACTS" &&
                    <>
                        <RiContactsFill size={20} />



                    </>
                }

            </div>

            {/* Close Button */}
            {!replied && <button className={styles.closeButton} onClick={onClose}>
                <IoClose />
            </button>}
        </div>
    );
};

export default ReplyPreview;
