import React, { useState, useRef, useEffect } from 'react';
import styles from './ContactCard.module.css';
import { formatDistanceToNow, format } from 'date-fns';
import { useMaskNo } from '../../customHooks/useMaskNo';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import ContactActivityPopup from './ContactActivityPopup';
import { IoIosArrowDroprightCircle, IoMdMove } from "react-icons/io";
const ContactCard = ({ currentUser, contact, groupBy }) => {
  const [expanded, setExpanded] = useState(false);
  const [hovered, setHovered] = useState(false);
  const maskNo = useMaskNo();
  const [showActivities, setShowActivities] = useState(false);
  const [activities, setActivities] = useState([]);
  const cardRef = useRef(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: contact.id,disabled: groupBy === "agent",});
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 'auto',
    position: 'relative',

  };


  const toggleExpand = () => setExpanded(prev => !prev);

  const formattedCreatedAt = format(new Date(contact.createdAt), 'PPP');
  const lastActivityAgo = formatDistanceToNow(new Date(contact.lastActivity), { addSuffix: true });
 
  const toggleActivities = async (e) => {
    e.stopPropagation();
    
    setShowActivities(prev => !prev);
  };

  return (
    <div ref={setNodeRef} style={style} >
      <div
        className={`card mb-2 ${styles.contactCard} ${expanded ? styles.expanded : ''}`}
        onClick={toggleExpand}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        ref={cardRef}
        style={{ position: 'relative' }}
      >

        {hovered && (
          <div
           {...listeners} {...attributes}
           className={`${styles.threeDotsHandle}`}>
            <span
              className={`${styles.threeDots}`}
              onClick={e => {
                e.stopPropagation();
                toggleExpand();
              }}
            >
              &#8230; {/* Unicode for "three dots" */}
            </span>
          </div>
        )}
        {hovered && (
          <span
            className={`${styles.dropdownArrow}`}
            onClick={e => {
              e.stopPropagation();
              toggleExpand();
            }}
          >
            {expanded ? '▲' : '▼'}
          </span>
        )}
        <div className="card-body p-2 "
        style={{width:"80%",marginTop:"1rem"}}
        >
          <h6 className={`card-title mb-1 ${contact.unread ? styles.unread : ''}`}>
            {contact.name}
          </h6>

          {contact.phone && <p><strong>Phone:</strong> {currentUser.user_type === "admin" ? contact.phone : maskNo(contact.phone)}</p>}
          {contact.email && <p><strong>Email:</strong> {contact.email}</p>}


          {(groupBy === 'agent' || groupBy === 'pipeline' ) && contact.labels?.length > 0 && (
            <div className="mb-1">
              {contact.labels.map(label => (
                <span key={label.id} className={`badge me-1 ${styles.labelBadge}`}>
                  {label.name}
                </span>
              ))}
            </div>
          )}

          <div className='d-flex justify-content-end w-100 dropdown'>
            {/* <span
              className={`${styles.activityArrow} dropdown-toggle `}
              onClick={toggleActivities}
              type="button"
              style={{color:"#fa5e51"}}
            >
              <IoIosArrowDroprightCircle size={20} />
            </span> */}
            {showActivities && (
                <ContactActivityPopup
                anchorRef={cardRef}
                  activities={activities}
                  setActivities={setActivities}
                  onAddActivity={() => console.log('Open modal to schedule activity')}
                  onClose={() => setShowActivities(false)}
                  phone={contact.phone}
                />
            )}

          </div>


          <div
            className={`${styles.expandSection}`}
            style={{ maxHeight: expanded ? '200px' : '0' }}
          >
            <p><strong>Agent:</strong> {contact.agent}</p>
            <p><strong>Last Contacted:</strong> {contact.lastContacted}</p>
            <p><strong>Created:</strong> {formattedCreatedAt}</p>
            <p><strong>Last Activity:</strong> {lastActivityAgo}</p>
          </div>
        </div>
      </div>

    </div >
  );
};

export default ContactCard;
