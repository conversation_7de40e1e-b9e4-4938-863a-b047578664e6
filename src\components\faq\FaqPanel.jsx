import React, { useState, useContext, useEffect } from "react";
import { Accordion, Form } from "react-bootstrap";
import { IoCloseSharp } from "react-icons/io5";
import styles from './faqSettings.module.css'
import { AuthContext } from "../../context/AuthContext";
import axios from "axios";
import { BASE_URL2 } from "../../api/api";
import { CircularProgress } from "@mui/material";
const FaqPanel = ({ setFaqOpen }) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedCategory, setSelectedCategory] = useState("All");
    const [faqs, setFaqs] = useState([]);
    const { currentUser } = useContext(AuthContext);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const fetchFaqs = async () => {
            setLoading(true);
            const payload = {
                user_id: currentUser.parent_id,
                token: currentUser.parent_token,
                method: "retrieve_faq",

            }
            try {
                const { data } = await axios.post(`${BASE_URL2}/whatsapp_setting`, payload);
                if (data.success) {
                    setFaqs(data.data);
                } else {
                    setFaqs([]);
                }

            } catch (error) {
                console.log(error);

            }
            finally {
                setLoading(false);
            }

        }

        fetchFaqs();

    }, [currentUser])


    // Filter FAQs based on search term and selected category
    const filteredFaqs = faqs.filter(faq =>
        (faq.question.toLowerCase().includes(searchTerm.toLowerCase()) || faq.answer.toLowerCase().includes(searchTerm.toLowerCase())) &&
        (selectedCategory === "All" || faq.category === selectedCategory)
    );

    return (
        <div className={`d-flex flex-column bg-light ${styles.faqPanel}`}>
            <div className="p-3">
                <div className="d-flex mb-2 justify-content-between aling-items-center">
                    <h4>Help</h4>
                    <IoCloseSharp
                        size={24}
                        onClick={() => setFaqOpen(false)}
                        style={{ cursor: "pointer" }}

                    />

                </div>
                {/* Search Bar */}
                <Form.Control
                    type="text"
                    placeholder="Search FAQs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />

                {/* Category Dropdown */}
                {/* <Form.Select
                    aria-label="Select category"
                    className="mt-2"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                >
                    <option value="All">All Categories</option>
                    <option value="Account">Account</option>
                    <option value="Support">Support</option>
                    <option value="Settings">Settings</option>
                </Form.Select> */}
            </div>

            <div className="overflow-auto p-3" style={{ flex: 1 }}>
                {/* Collapsible FAQ List */}
                {loading ?
                    <div className="w-100 text-center">
                        <CircularProgress />
                    </div>
                    :
                    filteredFaqs.length === 0 ? (
                        <p>No FAQs found</p>
                    ) :
                        <Accordion>
                            {filteredFaqs.map((faq, index) => (
                                <Accordion.Item eventKey={index.toString()} key={index}>
                                    <Accordion.Header>
                                        <strong>Q: {faq.question}</strong>
                                    </Accordion.Header>
                                    <Accordion.Body>
                                        <strong>A:</strong> {faq.answer}
                                    </Accordion.Body>
                                </Accordion.Item>
                            ))}
                        </Accordion>}
            </div>
        </div>
    );
};

export default FaqPanel;
