import React from "react";
import style from "./replyDropdown.module.css";
const ReplyDropdown = ({ isOpen, onClose, options,right }) => {
  if (!isOpen) return null;

  return (
    <div className={`${style.dropdownMenu} ${right?style.agentMessage:style.userMessage}`}>
      <ul>
        {options.map((option, index) => (
          <li key={index} onClick={() => option.action()}>
            {option.label}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ReplyDropdown;
