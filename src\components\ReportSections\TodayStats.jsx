import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, LabelList } from 'recharts';

const TodayStats = ({ labelData, liveChats, readChats, unreadChats, repeatedChats }) => {

    // Count occurrences of each label
    const labelCountMap = {};
    let noLabelCount = 0;
    labelData.forEach(chat => {
        if (chat.label && chat.label.length > 0) {
            chat.label.forEach(({ name, color_code }) => {
                if (labelCountMap[name]) {
                    labelCountMap[name].count += 1;
                } else {
                    labelCountMap[name] = { count: 1, color: color_code };
                }
            });
        } else {
            noLabelCount += 1;
        }
    });

    // Convert label count to chart data
    const labelChartData = Object.keys(labelCountMap).map(name => ({
        name,
        value: labelCountMap[name].count,
        color: labelCountMap[name].color
    }));

    if (noLabelCount > 0) {
        labelChartData.push({ name: "No Label", value: noLabelCount, color: "#D3D3D3" });
    }

    // Mock country-wise live users data (replace with actual data)
    const countryWiseData = [
        { country: 'India', users: 45, flag: '🇮🇳' },
        { country: 'USA', users: 23, flag: '🇺🇸' },
        { country: 'UK', users: 12, flag: '🇬🇧' },
        { country: 'Canada', users: 8, flag: '🇨🇦' },
        { country: 'Australia', users: 5, flag: '🇦🇺' },
    ];

    const totalLiveUsers = countryWiseData.reduce((sum, item) => sum + item.users, 0);

    const hasData = labelChartData.length > 0;

    return (
        <div className="d-flex bg-white flex-column flex-lg-row justify-content-between w-100 shadow" style={{ borderRadius: ".8rem", gap: "1rem" }}>
            {/* Bar Chart - Label Distribution */}
            <div className="bg-white p-2 shadow w-100 w-lg-50" style={{ borderRadius: ".8rem" }}>
                <h6 className="text-center mt-2">Labels Distribution</h6>
                <ResponsiveContainer width="100%" height={250}>
                    {hasData ? (
                        <BarChart data={labelChartData} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <defs>
                                <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                                </linearGradient>
                            </defs>
                            <Bar dataKey="value" fill="url(#colorValue)"
                                animationDuration={800}
                                animationEasing="ease-in-out"
                                radius={[8, 8, 0, 0]}>
                                <LabelList dataKey="value" position="top" fill="#444" />
                            </Bar>
                        </BarChart>
                    ) : (
                        <p className="text-muted text-center">No data available</p>
                    )}
                </ResponsiveContainer>
            </div>

            {/* Live Users Statistics */}
            <div className="bg-white p-2 shadow w-100 w-lg-50" style={{ borderRadius: ".8rem" }}>
                <h6 className="text-center mt-2">Live Users</h6>
                <div className="d-flex flex-column h-100">
                    {/* Total Live Users */}
                    <div className="text-center mb-3">
                        <div className="d-flex justify-content-center align-items-center mb-2">
                            <div className="bg-success rounded-circle d-flex justify-content-center align-items-center" 
                                 style={{ width: "60px", height: "60px" }}>
                                <span className="text-white fw-bold fs-4">{totalLiveUsers}</span>
                            </div>
                        </div>
                        <p className="text-muted mb-0">Total Live Users</p>
                    </div>

                    {/* Country-wise breakdown */}
                    <div className="flex-grow-1" style={{ maxHeight: "180px", overflowY: "auto" }}>
                        <h6 className="text-center mb-2" style={{ fontSize: "14px", color: "maroon" }}>Country Wise</h6>
                        {countryWiseData.map((item, index) => (
                            <div key={index} className="d-flex justify-content-between align-items-center mb-2 p-2" 
                                 style={{ backgroundColor: "#f8f9fa", borderRadius: "6px" }}>
                                <div className="d-flex align-items-center">
                                    <span className="me-2" style={{ fontSize: "18px" }}>{item.flag}</span>
                                    <span style={{ fontSize: "14px", fontWeight: "500" }}>{item.country}</span>
                                </div>
                                <div className="d-flex align-items-center">
                                    <div className="bg-primary rounded-circle d-flex justify-content-center align-items-center me-2" 
                                         style={{ width: "24px", height: "24px" }}>
                                        <span className="text-white" style={{ fontSize: "12px", fontWeight: "bold" }}>
                                            {item.users}
                                        </span>
                                    </div>
                                    <div className="progress" style={{ width: "60px", height: "6px" }}>
                                        <div className="progress-bar bg-primary" 
                                             style={{ width: `${(item.users / totalLiveUsers) * 100}%` }}>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TodayStats;