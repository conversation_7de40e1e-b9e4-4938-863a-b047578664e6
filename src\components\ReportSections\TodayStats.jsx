import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>s, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, LabelList } from 'recharts';

const TodayStats = ({ labelData, liveChats, readChats, unreadChats, repeatedChats }) => {

    // Count occurrences of each label
    const labelCountMap = {};
    let noLabelCount = 0;
    labelData.forEach(chat => {
        if (chat.label && chat.label.length > 0) {
            chat.label.forEach(({ name, color_code }) => {
                if (labelCountMap[name]) {
                    labelCountMap[name].count += 1;
                } else {
                    labelCountMap[name] = { count: 1, color: color_code };
                }
            });
        } else {
            noLabelCount += 1;
        }
    });

    // Convert label count to chart data
    const labelChartData = Object.keys(labelCountMap).map(name => ({
        name,
        value: labelCountMap[name].count,
        color: labelCountMap[name].color
    }));

    if (noLabelCount > 0) {
        labelChartData.push({ name: "No Label", value: noLabelCount, color: "#D3D3D3" });
    }

    const hasData = labelChartData.length > 0;

    return (
            {/* Bar Chart - Label Distribution */}
            <div className="bg-white p-3 shadow w-100 w-lg-50" style={{ borderRadius: ".8rem" }}>
                <h6 className="text-center mt-2 mb-4">Labels Distribution</h6>
                <ResponsiveContainer width="100%" height={250}>
                    {hasData ? (
                        <BarChart data={labelChartData} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <defs>
                                <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                                </linearGradient>
                            </defs>
                            <Bar dataKey="value" fill="url(#colorValue)"
                                animationDuration={800}
                                animationEasing="ease-in-out"
                                radius={[8, 8, 0, 0]}>
                                <LabelList dataKey="value" position="top" fill="#444" />
                            </Bar>
                        </BarChart>
                    ) : (
                        <p className="text-muted text-center">No data available</p>
                    )}
                </ResponsiveContainer>
            </div>

    );
};

export default TodayStats;