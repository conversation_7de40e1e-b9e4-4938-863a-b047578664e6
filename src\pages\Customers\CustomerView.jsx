import styles from './customerView.module.css';
import LeftMenu from '../../components/LeftMenu';
import { useState, useEffect, useContext } from 'react';
import ActivityLog from './ActivityLog';
import { useParams } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import axios from 'axios'
import { BASE_URL2 } from '../../api/api';
import { FaPhoneAlt } from "react-icons/fa";
import { IoLocation } from "react-icons/io5";
import { MdEmail } from "react-icons/md";
import LabelCard from '../../components/Labels/LabelCard';
const CustomerView = () => {
    const { currentUser } = useContext(AuthContext);
    const [activeTab, setActiveTab] = useState("overview");
    const [customerData, setCustomerData] = useState({
        name: '<PERSON>',
        email: '<EMAIL>',
        location: 'California, USA',
        phone: '+01 (375) 2589 645',
        stats: {
            followers: '28.65K',
            following: '38.85K',
            engagement: '43.67K',
        },
        label: [{
            name: 'Deal Won',
            color: '#ffd9a8',
        }],
        agents: [
            { agent_id: 65, agent_name: 'Ritu' },
            { agent_id: 1, agent_name: 'admin' },
            { agent_id: 65, agent_name: 'kajal' },
        ],
    });
    const [loading, setLoading] = useState(false);
    const { encodedMobile } = useParams();
    const mobile = encodedMobile ? atob(encodedMobile) : null;


    useEffect(() => {
        if (!currentUser?.parent_id || !currentUser?.parent_token || !mobile) return;
        fetchCustomerData();
    }, [currentUser, mobile])



    const fetchCustomerData = async () => {
        try {
            setLoading(true);
            const response = await axios.post(`${BASE_URL2}/whatsapp_report`, {
                user_id: currentUser.parent_id,
                token: currentUser.parent_token,
                method: "customer_history",
                mobile: mobile,
                brand_number: currentUser.brand_number,
            });
            if (response.data.success) {

                // setCustomerData(response.data.data);

            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    }


    return (
        <div className={`${styles.pageContainer}`}>
            <LeftMenu />
            <div className={`d-flex flex-column flex-lg-row justify-content-center gap-3 align-items-center w-100 ${styles.main}`}>
                <div className={`${styles.profileCard} ${styles.card}`}>
                    <div className="d-flex flex-column w-100 align-items-center mt-4">

                        {/* User Avatar */}
                        <div
                            className="chat-user-img online user-own-img mb-3"
                            style={{ width: "70px", height: "70px" }}
                        >
                            {/* SVG Avatar */}
                            <svg
                                viewBox="0 0 212 212"
                                height="212"
                                width="212"
                                preserveAspectRatio="xMidYMid meet"
                                className="xh8yej3 x5yr21d"
                            >
                                <title>default-user</title>
                                <path fill="#DFE5E7" d="M106.251,0.5C164.653,0.5,212,47.846,212,106.25S164.653,212,106.25,212C47.846,212,0.5,164.654,0.5,106.25 S47.846,0.5,106.251,0.5z" />
                                <g>
                                    <path fill="#FFFFFF" d="M173.561,171.615c-..." />
                                </g>
                            </svg>
                        </div>

                        {/* Name & Email */}
                        <div className="text-center mb-3">
                            <strong className="fs-5">Alexander Dalla</strong>
                            <p className="text-muted small"><EMAIL></p>
                            {/* Label */}
                            {customerData?.label?.length > 0 && (
                                <div className="w-100 mt-3">
                                    <LabelCard labelData={customerData.label[0]} />
                                </div>
                            )}
                        </div>



                        {/* Contact Info Section */}
                        <div className={`${styles.contactInfoSection} w-100`}>
                            <InfoRow icon={<IoLocation />} label="Location" value={customerData?.location} />
                            <InfoRow icon={<FaPhoneAlt />} label="Mobile" value={customerData?.phone} />
                            <InfoRow icon={<MdEmail />} label="Email" value={customerData?.email} />
                        </div>


                        {/* Agents */}
                        {customerData?.agents.length > 0 && (
                            <div className={`${styles.agentSection} mt-4 w-100 px-3`}>
                                <p className="fw-bold mb-2 text-center">Assigned Agents</p>
                                <ul className="list-group list-group-flush">
                                    {customerData.agents
                                        .filter((a) => a.agent_name)
                                        .map((a, i) => (
                                            <li key={i} className="list-group-item d-flex justify-content-between align-items-center">
                                                {a.agent_name}
                                                {/* Future: add avatar/status etc. */}
                                            </li>
                                        ))}
                                </ul>
                            </div>
                        )}
                    </div>
                </div>

                <div className={`${styles.detailCard} ${styles.card}`}>
                    <div>
                        <ul className={`nav nav-tabs ${styles.tabsContainer}`}>
                            <li className="nav-item">
                                <button
                                    className={`${styles.navLink} ${activeTab === "overview" ? styles.active : ""}`}
                                    onClick={() => setActiveTab("overview")}
                                >
                                    Overview
                                </button>
                            </li>
                            <li className="nav-item">
                                <button
                                    className={`${styles.navLink} ${activeTab === "activity" ? styles.active : ""}`}
                                    onClick={() => setActiveTab("activity")}
                                >
                                    Activity
                                </button>
                            </li>
                            <li className="nav-item">
                                <button
                                    className={`${styles.navLink} ${activeTab === "notifications" ? styles.active : ""}`}
                                    onClick={() => setActiveTab("notifications")}
                                >
                                    Notifications
                                </button>
                            </li>

                        </ul>

                        <div className="mt-3 p-3">
                            {activeTab === "overview" &&
                                <div>
                                    <div className="profile mb-4">
                                        <h5 class="fw-bold">Profile About:</h5>
                                        Lorem, ipsum dolor sit amet consectetur adipisicing elit. Obcaecati architecto neque asperiores sunt corrupti, blanditiis, corporis eligendi qui porro, vero numquam eius illo est incidunt dolores. Culpa, necessitatibus aut quisquam soluta, amet unde nobis neque magnam eum sapiente incidunt saepe doloremque aliquid odit velit odio. Excepturi reprehenderit omnis mollitia iusto.
                                    </div>
                                    <div>

                                        <div class="mb-3 d-flex w-100 justify-content-between align-items-center ">
                                            <h5 class="fw-bold">Profile Details:</h5>
                                            <button class="btn btn-outline-primary">Edit Profile</button>
                                        </div>
                                        <div class="row gy-3">
                                            <div class="col-md-6">
                                                <strong>Full Name:</strong> Alexandra Della
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Surname:</strong> Della
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Company:</strong> Theme Ocean
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Date of Birth:</strong> 26 May, 2000
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Mobile Number:</strong> +01 (375) 5896 3214
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Email Address:</strong> <EMAIL>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Location:</strong> California, United States
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Joining Date:</strong> 20 Dec, 2023
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Country:</strong> United States
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Communication:</strong> Email, Phone
                                            </div>
                                        </div>


                                    </div>

                                </div>
                            }
                            {activeTab === "activity" && <ActivityLog />}
                            {activeTab === "notifications" && <p>Notifications Content</p>}
                        </div>
                    </div>
                </div>

            </div>
        </div>

    )
}

const InfoRow = ({ icon, label, value }) => (
    <div className="d-flex justify-content-between align-items-center mb-2 w-100 px-2">
        <div className="d-flex align-items-center text-muted">
            <span className="me-2">{icon}</span>
            <span className="fw-semibold">{label}</span>
        </div>
        <div className="text-end">{value || "-"}</div>
    </div>
);


export default CustomerView;