{"version": 3, "sources": ["app.scss", "custom/fonts/_fonts.scss", "app.css", "_theme-light.scss", "_theme-dark.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "_variables.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_accordion.scss", "custom/components/_modals.scss", "custom/components/_forms.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_emoji-picker.scss", "custom/structure/_general.scss", "custom/structure/_sidemenu.scss", "custom/pages/_chat-leftsidebar.scss", "custom/pages/_user-chat.scss", "custom/pages/_user-profile-details.scss", "custom/pages/_authentication.scss"], "names": [], "mappings": "AAAA;;;;;;;CAAA;ACIQ,iGAAA;AAMR;EACI,sCAAA;EACA,0CAAA;EACA,uFAAA;EACA,gBAAA;ACAJ;ADGA;EACI,sCAAA;EACA,4CAAA;EACA,2FAAA;EACA,gBAAA;ACDJ;ADGA;EACI,sCAAA;EACA,2CAAA;EACA,yFAAA;EACA,gBAAA;ACDJ;ADGA;EACI,sCAAA;EACA,6CAAA;EACA,6FAAA;EACA,gBAAA;ACDJ;ADGA;EACI,sCAAA;EACA,yCAAA;EACA,qFAAA;EACA,gBAAA;ACDJ;ACpCA;EAIE,wBAAA;EACA,qCAAA;EACA,4CAAA;EACA,yCAAA;EACA,4BAAA;EAIC,qCAAA;EAGD,uBAAA;EACA,yBAAA;EAGA,6BAAA;EACA,6BAAA;EAKA,mBAAA;EACA,6BAAA;EACA,kBAAA;EACA,yBAAA;EACA,wBAAA;EAGA,wBAAA;EACA,8BAAA;EAGA,0BAAA;EAGA,sBAAA;EACA,mCAAA;EACA,iCAAA;EACA,uCAAA;EACA,oCAAA;EACA,+BAAA;EAGA,kBAAA;EACA,sBAAA;EACA,+BAAA;EACA,0BAAA;EACA,0BAAA;EACA,uCAAA;EAGA,mBAAA;EACA,2BAAA;EACA,wCAAA;EAIA,wCAAA;EACA,qCAAA;EAGA,2CAAA;EACA,uBAAA;EACA,iCAAA;EACA,oDAAA;EAGE,yBAAA;EAGA,2BAAA;EAGA,gCAAA;EACA,qBAAA;EACA,wBAAA;EACA,+BAAA;EAGF,yBAAA;EAGA,sDAAA;EACA,2CAAA;EACA,mDAAA;EAGA,iCAAA;EAGA,qBAAA;EAGA,iCAAA;EAGA,mBAAA;EACA,4CAAA;EACA,0BAAA;EACA,gCAAA;EACA,gCAAA;EACA,+BAAA;EAGA,qCAAA;EACA,kCAAA;EAMA,+CAAA;ADhBF;;AElGA;EA2BM,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAKJ,wBAAA;EACA,qCAAA;EACA,4CAAA;EACA,yCAAA;EACA,4BAAA;EAGA,iCAAA;EAGA,wBAAA;EACA,wBAAA;EAGE,0CAAA;EACA,6BAAA;EAGF,6BAAA;EAIA,uBAAA;EACA,+BAAA;EACA,mCAAA;EAGA,qCAAA;EACA,4BAAA;EAGA,qEAAA;EACA,oCAAA;EACA,4EAAA;EACA,gDAAA;EACA,kEAAA;EAGA,2BAAA;EAGA,2BAAA;EAIA,mBAAA;EACA,0BAAA;EACA,kBAAA;EACA,4BAAA;EACA,wBAAA;EAGA,wBAAA;EACA,8BAAA;EAGA,0BAAA;EAGA,yBAAA;EACA,mCAAA;EACA,iCAAA;EACA,uCAAA;EACA,oCAAA;EACA,+BAAA;EAGA,qBAAA;EACA,yBAAA;EACA,+BAAA;EACA,yBAAA;EACA,2BAAA;EACA,uCAAA;EAGA,sBAAA;EACA,8BAAA;EACA,wCAAA;EAGA,wCAAA;EACA,qCAAA;EAGA,wCAAA;EACA,8BAAA;EACA,iCAAA;EACA,uCAAA;EAGA,yBAAA;EAGA,2BAAA;EAGE,gCAAA;EACA,qBAAA;EACA,wBAAA;EACA,+BAAA;EAGF,yBAAA;EAGA,mDAAA;EACA,iDAAA;EACA,yDAAA;EAGA,iCAAA;EAGA,wBAAA;EAGA,iCAAA;EAGA,sBAAA;EACA,4CAAA;EACA,0BAAA;EACA,gCAAA;EACA,gCAAA;EACA,+BAAA;EAGA,qCAAA;EACA,kCAAA;EAGA,gCAAA;AFqBF;;AG1LA;;;;;;uDAAA;AAOC;EACG,kBAAA;EACA,eAAA;EACA,qBAAA;EACA,gBAAA;EACA,yBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,wCAAA;AH6LJ;;AG3LE;EACE,kBAAA;EACA,kBAAA;EACA,YAAA;EACA,aAAA;EACA,iBAAA;EACA,kBAAA;EACA,UAAA;EACA,8BAAA;EAIA,qJAAA;EACA,qCAAA;EAGA,6BAAA;EACA,uDAAA;EAGA,uDAAA;EAAA,+CAAA;EAAA,uCAAA;EAAA,0DAAA;EACA,2CAAA;EAIA,mCAAA;EACA,oBAAA;AH8LJ;;AG5LE;EACE,oCAAA;EAIA,6KAAA;AH+LJ;;AG7LE;EACE,8BAAA;AHgMJ;;AG9LE;EACE,oCAAA;AHiMJ;;AG/LE;EACE,mCAAA;EAGA,2BAAA;AHkMJ;;AGhME;;EAEE,gCAAA;EAIA,wBAAA;EACA,2EAAA;AHmMJ;;AGjME;;;;EAIE,mBAAA;EACA,sBAAA;EACA,eAAA;EACA,YAAA;EACA,aAAA;EACA,cAAA;EACA,kCAAA;EACA,cAAA;EACA,gBAAA;EACA,kBAAA;EACA,qBAAA;EACA,UAAA;AHoMJ;;AGlME;EACE,qBAAA;EACA,oBAAA;AHqMJ;;AGnME;EACE,SAAA;EACA,qBAAA;AHsMJ;;AGpME;EACE,oBAAA;EACA,sBAAA;AHuMJ;;AGrME;EACE,UAAA;AHwMJ;;AGtME;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,UAAA;AHyMJ;;AGvME;EACE,kBAAA;EACA,YAAA;EACA,aAAA;EACA,kBAAA;EACA,kBAAA;AH0MJ;;AGxME;EACE,wBAAA;EACA,yDAAA;EACA,iDAAA;EACA,6BAAA;EAGA,qBAAA;AH2MJ;;AGzME;EACE,uDAAA;EACA,+CAAA;AH4MJ;;AG1ME;EACE,cAAA;AH6MJ;;AGzMI;EACI,0CAAA;AH4MR;;AGvMI;EACI,yCAAA;AH0MR;;AGtMI;EACI,wCAAA;AHyMR;;AGrMI;EACI,yCAAA;AHwMR;;AGpMI;EACI,0CAAA;AHuMR;;AGnMI;EACI,yCAAA;AHsMR;;AIvWA;EACE,cAAA;EACA,aAAA;AJ0WF;;AIvWA;EACE,cAAA;EACA,aAAA;AJ0WF;;AIvWA;EACE,YAAA;EACA,WAAA;AJ0WF;;AIvWA;EACE,YAAA;EACA,WAAA;AJ0WF;;AIvWA;EACE,YAAA;EACA,WAAA;AJ0WF;;AIvWA;EACE,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,gDAAA;EACA,WCSS;EDRT,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,gBCXmB;EDYnB,YAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;EACA,WAAA;AJ0WF;;AItWA;EACE,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,mBAAA;MAAA,eAAA;EACA,iBAAA;AJyWF;AIxWE;EACE,iBAAA;EACA,mCAAA;EACA,kBAAA;EACA,4BAAA;EAAA,oBAAA;AJ0WJ;AIzWI;EACE,kBAAA;EACA,mCAAA;UAAA,2BAAA;AJ2WN;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AM3ZA;EACI,0BAAA;AN8ZJ;;AMzZA;EACI,gBDhCiB;AL4brB;;AMzZA;EACI,gBDnCmB;AL+bvB;;AMvZA;EACI,YAAA;EACA,WAAA;EACA,6BAAA;EACA,cAAA;EACA,yBAAA;EACA,kBAAA;EACA,cDzBO;EC0BP,kBAAA;EACA,4BAAA;EAAA,oBAAA;AN0ZJ;AMzZI;EACI,cD5BG;EC6BH,yBDjCG;AL4bX;;AMvZA;EACI,eAAA;AN0ZJ;;AMxZA;EACI,eAAA;AN2ZJ;;AMzZA;EACI,gBAAA;AN4ZJ;;AM1ZA;EACI,gBAAA;AN6ZJ;;AM1ZA;EACI,gBAAA;AN6ZJ;;AM1ZA;EACI,kBAAA;EACA,YAAA;EACA,WAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,MAAA;EACA,uCAAA;AN6ZJ;;AM1ZA;EACI,uDAAA;AN6ZJ;;AMzZA;EACI,uCAAA;AN4ZJ;;AM1ZA;EACI,wCAAA;AN6ZJ;;AMzZI;EACI,oCAAA;AN4ZR;AM1ZI;EACI,0DAAA;UAAA,kDAAA;AN4ZR;;AOrhBA;EACE,YAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,QAAA;APwhBF;AOthBE;EACI,gDAAA;UAAA,wCAAA;APwhBN;AOthBE;EACI,mDAAA;UAAA,2CAAA;APwhBN;AOthBE;EACI,kDAAA;UAAA,0CAAA;APwhBN;AOthBE;EACI,mDAAA;UAAA,2CAAA;APwhBN;AOthBE;EACI,gDAAA;UAAA,wCAAA;APwhBN;;AOphBA;EACI,qBAAA;EACA,UAAA;EACA,YAAA;EACA,mBAAA;EACA,iDAAA;APuhBJ;;AOphBA;EACI;IACE,YAAA;EPuhBJ;EOrhBE;IACE,YAAA;EPuhBJ;EOrhBE;IACE,YAAA;EPuhBJ;AACF;;AOhiBA;EACI;IACE,YAAA;EPuhBJ;EOrhBE;IACE,YAAA;EPuhBJ;EOrhBE;IACE,YAAA;EPuhBJ;AACF;AQ5jBQ;EACI,kBAAA;AR8jBZ;AQzjBQ;EACI,eAAA;AR2jBZ;AQvjBgB;EACI,iBAAA;ARyjBpB;;AQjjBI;EACI,iBAAA;EACA,eAAA;ARojBR;;AQ5iBQ;EACI,iSAAA;AR+iBZ;;AShlBI;EACI,gDAAA;EACA,wBAAA;ATmlBR;;AUjlBC;EACE,gBAAA;EAAkB,aAAA;AVwlBrB;;AUzlBC;EACE,gBAAA;EAAkB,aAAA;AVwlBrB;;AUzlBC;EACE,gBAAA;EAAkB,aAAA;AVwlBrB;;AUzlBC;EACE,gBAAA;EAAkB,aAAA;AVwlBrB;;AUzlBC;;;;EACE,gBAAA;EAAkB,aAAA;AVwlBrB;;AUnlBA;;;EAGE,eAAA;EACA,gBAAA;AVslBF;;AUnlBA;EACE,gDAAA;EACA,4CAAA;AVslBF;;AW9mBA;EACE,kBAAA;EACA,4BAAA;EAAA,6BAAA;MAAA,0BAAA;UAAA,sBAAA;EACA,mBAAA;MAAA,eAAA;EACA,uBAAA;MAAA,oBAAA;UAAA,2BAAA;EACA,yBAAA;MAAA,yBAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;AXinBF;;AW9mBA;EACE,gBAAA;EACA,cAAA;EACA,eAAA;EACA,kBAAA;EACA,mBAAA;AXinBF;;AW9mBA;EACE,kBAAA;EACA,kBAAA;EACA,gBAAA;EACA,UAAA;EACA,SAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,QAAA;EACA,sBAAA;EACA,uBAAA;EACA,UAAA;AXinBF;;AW9mBA;EACE,6BAAA;EACA,sCAAA;UAAA,8BAAA;EACA,uBAAA;EACA,kBAAA;EACA,MAAA;EACA,kBAAA;EACA,SAAA;EACA,mBAAA;EACA,UAAA;EACA,SAAA;EACA,iCAAA;AXinBF;;AW9mBA;EACE,kBAAA;EACA,yCAAA;UAAA,iCAAA;EACA,kBAAA;EACA,cAAA;EACA,YAAA;EAAc,mGAAA;EACd,WAAA;EACA,mBAAA;EACA,cAAA;EAAgB,mFAAA;EAChB,eAAA;EAAiB,kDAAA;EACjB,gBAAA;EAAkB,0CAAA;EAClB,qBAAA;EACA,uBAAA;AXqnBF;;AWlnBA;;EAEE,aAAA;AXqnBF;;AWlnBA;;EAEE,YAAA;EACA,cAAA;AXqnBF;;AWlnBA;EACE,gBAAA;EACA,eAAA;EACA,WAAA;EACA,oBAAA;AXqnBF;;AWlnBA;EACE,sCAAA;UAAA,8BAAA;EACA,YAAA;EACA,WAAA;EACA,cAAA;EACA,kBAAA;EACA,WAAA;EACA,eAAA;EACA,gBAAA;EACA,WAAA;EACA,UAAA;EACA,SAAA;EACA,oBAAA;EACA,yBAAA;MAAA,0BAAA;UAAA,kBAAA;EACA,oBAAA;MAAA,cAAA;EACA,0BAAA;MAAA,aAAA;AXqnBF;;AWlnBA;EACE,2BAAA;UAAA,mBAAA;EACA,cAAA;EACA,UAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,aAAA;EACA,YAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;EACA,oBAAA;EACA,WAAA;AXqnBF;;AWlnBA;EACE,UAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,oBAAA;EACA,gBAAA;AXqnBF;;AWlnBA;EACE,oBAAA;EACA,sBAAA;GAAA,qBAAA;OAAA,iBAAA;EACA,yBAAA;AXqnBF;;AWlnBA;EACE,mBAAA;AXqnBF;;AWlnBA;EACE,kBAAA;EACA,UAAA;EACA,UAAA;EACA,gBAAA;AXqnBF;;AWlnBA;EACE,kBAAA;EACA,WAAA;EACA,mBAAA;EACA,kBAAA;EACA,OAAA;EACA,QAAA;EACA,UAAA;EACA,uCAAA;EAAA,+BAAA;AXqnBF;;AWlnBA;EACE,0DAAA;EACA,YAAA;EACA,qCAAA;EAAA,6BAAA;AXqnBF;;AWlnBA;EACE,MAAA;EACA,WAAA;AXqnBF;;AWlnBA;EACE,QAAA;EACA,WAAA;AXqnBF;;AWlnBA;EACE,OAAA;EACA,YAAA;AXqnBF;;AWlnBA;EACE,YAAA;EACA,SAAA;EACA,UAAA;AXqnBF;;AWlnBA;EACE,WAAA;EACA,OAAA;EACA,QAAA;EACA,WAAA;EACA,aAAA;EACA,eAAA;EACA,WAAA;AXqnBF;;AWlnBA,gBAAA;AACA;EACE,WAAA;EACA,OAAA;AXqnBF;;AWlnBA;EACE,cAAA;EACA,eAAA;EACA,UAAA;EACA,kBAAA;EACA,aAAA;EACA,YAAA;EACA,kBAAA;EACA,kBAAA;AXqnBF;;AWlnBA;EACE,eAAA;EACA,OAAA;EACA,kBAAA;EACA,kBAAA;EACA,qBAAA;AXqnBF;;AWlnBA;EACE,YAAA;AXqnBF;;AYt0BA;EACI,8CAAA;EACA,uBAAA;EACA,+DAAA;UAAA,uDAAA;EACA,oBAAA;EACA,YAAA;AZy0BJ;AYx0BI;EACI,iDAAA;EACA,yBAAA;AZ00BR;AYx0BI;EAVJ;IAWQ,qBAAA;IACA,oBAAA;IACA,YAAA;EZ20BN;AACF;AYz0BI;EACI,gCAAA;AZ20BR;AYx0BI;EACI,uBAAA;AZ00BR;AYz0BQ;EACI,+CAAA;EACA,sCAAA;EACA,wCAAA;EACA,+BAAA;EACA,+BAAA;AZ20BZ;AY10BY;EACI,mDAAA;AZ40BhB;AY70BY;EACI,mDAAA;AZ40BhB;AY70BY;EACI,mDAAA;AZ40BhB;AY70BY;EACI,mDAAA;AZ40BhB;AY70BY;EACI,mDAAA;AZ40BhB;AYx0BQ;EACI,qCAAA;EACA,WAAA;EACA,SAAA;AZ00BZ;AYt0BI;EACI,4CAAA;AZw0BR;AYv0BQ;EACI,kDAAA;AZy0BZ;AYt0BY;EACI,kDAAA;AZw0BhB;;AYj0BA;EACI,6DAAA;AZo0BJ;;AYj0BA;EACI,4CAAA;AZo0BJ;;AYh0BA;EACI,qCAAA;AZm0BJ;;AY/zBA;EACI,8CAAA;AZk0BJ;;Aa14BA;EACI,iBAAA;Ab64BJ;;Ac34BA;EACI,eAAA;EACA,eAAA;EACA,aAAA;EACA,iBAAA;EACA,sCTDS;ESET,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,UAAA;EACA,4CAAA;EACA,cAAA;Ad84BJ;Ac54BI;EAXJ;IAYQ,eAAA;IACA,SAAA;IACA,YAAA;IACA,eAAA;IACA,gBAAA;IACA,0CAAA;Ed+4BN;AACF;Ac74BI;EACI,kBAAA;Ad+4BR;Ac74BQ;EACI,oCAAA;Ad+4BZ;Ac54BQ;EAPJ;IAQQ,aAAA;Ed+4BV;AACF;Ac74BQ;EACI,iBAAA;Ad+4BZ;Ac54BQ;EACI,cTpBI;ALk6BhB;Ac34BQ;EACI,aTzBG;ALs6Bf;;Acx4BA;EACI,YAAA;Ad24BJ;Ac14BI;EAFJ;IAGQ,WAAA;Ed64BN;Ec34BM;IACI,aAAA;Ed64BV;AACF;;Acz4BA;EACI,YAAA;EACA,4BAAA;EAAA,6BAAA;MAAA,0BAAA;UAAA,sBAAA;Ad44BJ;Ac34BI;EAHJ;IAIQ,oCAAA;QAAA,iCAAA;YAAA,yCAAA;IACA,8BAAA;IAAA,6BAAA;QAAA,uBAAA;YAAA,mBAAA;Ed84BN;AACF;Ac74BI;EACI,aAAA;EACA,cAAA;EACA,WAAA;Ad+4BR;Ac74BQ;EALJ;IAMQ,0BAAA;QAAA,aAAA;IACA,mBAAA;QAAA,oBAAA;YAAA,YAAA;IACA,aAAA;Edg5BV;AACF;Ac/4BQ;EACI,kBAAA;EACA,eAAA;EACA,wCTvEc;ESwEd,WAAA;EACA,YAAA;EACA,iBAAA;EACA,gBAAA;EACA,UAAA;EACA,kBAAA;Adi5BZ;Ac/4BY;EACI,sBAAA;Adi5BhB;Ac94BY;EACI,kBAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,WAAA;EACA,SAAA;Adg5BhB;Ac/4BgB;EAPJ;IAQQ,WAAA;IACA,WAAA;IACA,WAAA;IACA,SAAA;IACA,SAAA;IACA,mCAAA;YAAA,2BAAA;Edk5BlB;AACF;Ac/4BY;EAhCJ;IAiCQ,eAAA;IACA,WAAA;IACA,YAAA;IACA,iBAAA;IACA,gBAAA;Edk5Bd;AACF;Ach5BY;EACI,6BAAA;EACA,qCAAA;Adk5BhB;Ach5BgB;EACI,gDAAA;Adk5BpB;Ac74BQ;EACI,qCAAA;Ad+4BZ;Ac34BI;EACI,YAAA;EACA,WAAA;EACA,oCAAA;EACA,YAAA;Ad64BR;;Acz4BA;EACI,aAAA;Ad44BJ;;Acx4BI;EACI,qBAAA;Ad24BR;Acx4BI;EACI,aAAA;Ad04BR;;Ach4BwB;EACI,gBAAA;Adm4B5B;Ac33BI;EACI,qBAAA;Ad63BR;Ac13BI;EACI,aAAA;Ad43BR;;AeriCA;EACI,0BAAA;EACA,kBAAA;EACA,0CVIc;EUHd,oDVwjB0B;UUxjB1B,4CVwjB0B;ALgf9B;AetiCI;EANJ;IAOQ,gBAAA;IACA,gBAAA;IACA,aAAA;EfyiCN;AACF;AeviCI;EACI,yBV0BG;EUzBH,YAAA;EACA,kBAAA;EACA,kBAAA;EACA,gBAAA;EACA,cAAA;AfyiCR;AeviCQ;EACI,kBAAA;EACA,MAAA;EACA,OAAA;EACA,QAAA;AfyiCZ;;AeniCI;EACI,SAAA;AfsiCR;AeniCI;EACI,eAAA;EACA,kBAAA;EACA,WAAA;EACA,MAAA;AfqiCR;;AejiCA;EACI,+BAAA;AfoiCJ;AeliCI;EAHJ;IAIQ,2BAAA;EfqiCN;AACF;;AeliCA;EACI,2BAAA;AfqiCJ;AepiCI;EAFJ;IAGQ,2BAAA;EfuiCN;AACF;;AeniCA;EACI,SAAA;AfsiCJ;AeliCY;EACI,gDAAA;EACA,WV7BL;ALikCX;AeliCgB;EACI,sBAAA;AfoiCpB;AeniCoB;EACI,sDAAA;AfqiCxB;AejiCgB;EACI,sDAAA;EACA,sBAAA;AfmiCpB;AehiCgB;EACI,sDAAA;AfkiCpB;Ae7hCQ;EACI,cAAA;EACA,iBAAA;EACA,yBAAA;EACA,4BAAA;EAAA,oBAAA;EACA,eAAA;Af+hCZ;Ae5hCQ;EACI,eAAA;Af8hCZ;Ae3hCQ;EACI,gBAAA;Af6hCZ;AezhCY;EACI,qCAAA;EACA,gBVtFK;ALinCrB;AezhCgB;EACI,gDAAA;Af2hCpB;AethCQ;EACI,kBAAA;EACA,qBAAA;EACA,WAAA;EACA,UAAA;EACA,SAAA;AfwhCZ;AethCY;EACI,iBAAA;EACA,gBVtGO;EUuGP,eAAA;AfwhChB;;AelhCA;EACI,kBAAA;AfqhCJ;AenhCI;EACI,WAAA;EACA,YAAA;EACA,yBV7FG;EU8FH,kBAAA;EACA,mCAAA;EACA,kBAAA;EACA,QAAA;EACA,UAAA;EACA,SAAA;AfqhCR;AejhCQ;EACI,yBVxEF;AL2lCV;Ae9gCQ;EACI,yBV/EF;AL+lCV;;AexgCI;EACI,eAAA;EACA,iBAAA;Af2gCR;;AevgCA;EACI,iBAAA;EACA,qCAAA;EACA,gBVtJiB;EUuJjB,kBAAA;EACA,eAAA;Af0gCJ;AezgCI;EACI,WAAA;EACA,WAAA;EACA,kBAAA;EACA,QAAA;EACA,mCAAA;UAAA,2BAAA;EACA,UAAA;EACA,QAAA;EACA,wCV8XsB;AL6oB9B;;AergCA;EACI,8BAAA;AfwgCJ;AetgCI;EAHJ;IAIQ,2BAAA;EfygCN;AACF;AexgCI;EACI,kBAAA;EACA,kBAAA;EACA,+CAAA;Af0gCR;AexgCQ;EACI,gBAAA;Af0gCZ;;AejgCA;EACI,kBAAA;EACA,qBAAA;AfogCJ;AelgCI;EACI,kBAAA;EACA,QAAA;EACA,UAAA;EACA,SAAA;EACA,eAAA;AfogCR;AejgCI;EACI,oBAAA;KAAA,iBAAA;AfmgCR;AehgCI;EACI,aAAA;AfkgCR;;Ae5/BQ;EACI,WVlMD;ALisCX;Ae5/BI;EACI,UAAA;Af8/BR;Ae7/BQ;EACI,aAAA;Af+/BZ;Ae5/BQ;EACI,kBAAA;Af8/BZ;Ae1/BY;EACI,gBAAA;EACA,gCAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,oDAAA;UAAA,4CAAA;EACA,qCAAA;EACA,eAAA;EACA,UAAA;Af4/BhB;Aex/BgB;EACI,qCAAA;Af0/BpB;Aen/BQ;EACI,wCAAA;Afq/BZ;;Ae/+BA;EACI,eAAA;EACA,oCAAA;Afk/BJ;;AgBlwCA;EACI,sDAAA;EACA,4BAAA;EAAA,oBAAA;EACA,kBAAA;EACA,mCAAA;AhBqwCJ;AgBnwCI;EACI,WAAA;EACA,kBAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,QAAA;EACA,6BAAA;EACA,YAAA;AhBqwCR;AgBlwCI;EAjBJ;IAkBQ,eAAA;IACA,OAAA;IACA,MAAA;IACA,WAAA;IACA,YAAA;IACA,kBAAA;IACA,mCAAA;YAAA,2BAAA;IACA,WAAA;EhBqwCN;EgBnwCM;IACI,mBAAA;IACA,gCAAA;YAAA,wBAAA;EhBqwCV;AACF;AgBlwCI;EACI,kBAAA;AhBowCR;AgBhwCQ;EACI,aAAA;AhBkwCZ;AgB/vCY;EADJ;IAEQ,+BAAA;IAAA,+BAAA;IAAA,wBAAA;EhBkwCd;AACF;;AgB7vCA;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,WAAA;EACA,aAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;EACA,kBAAA;AhBgwCJ;AgB/vCI;EAPJ;IAQQ,aAAA;EhBkwCN;AACF;;AgB/vCA;EACI,kBAAA;EACA,SAAA;EACA,SAAA;EACA,mCAAA;UAAA,2BAAA;AhBkwCJ;;AgB/vCA;EACI,kBAAA;EACA,OAAA;EACA,QAAA;EACA,MAAA;EACA,UAAA;EACA,2CAAA;EACA,+CAAA;EACA,kCAAA;UAAA,0BAAA;AhBkwCJ;AgBjwCI;EATJ;IAUQ,eAAA;IACA,0CAAA;EhBowCN;AACF;AgBlwCI;EACI,kBAAA;EACA,aAAA;EACA,OAAA;EACA,QAAA;EACA,gBAAA;AhBowCR;AgBjwCY;EACI,cAAA;EACA,eAAA;EACA,iBAAA;EACA,gDAAA;EACA,mBAAA;AhBmwChB;AgBlwCgB;EACI,iBAAA;AhBowCpB;AgBjwCY;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,gBAAA;AhBmwChB;AgBjwCgB;EACI,wBAAA;AhBmwCpB;AgBhwCgB;EACI,WAAA;AhBkwCpB;AgB/vCgB;EACI,WAAA;AhBiwCpB;AgB9vCgB;EACI,uCAAA;EACA,mBAAA;EACA,6BAAA;AhBgwCpB;AgB7vCgB;EACI,mBAAA;AhB+vCpB;AgB1vCQ;EACI,kBAAA;AhB4vCZ;;AgBvvCA;EACC,mDAAA;UAAA,2CAAA;AhB0vCD;;AgBtvCI;EACI,YAAA;EACA,WAAA;EACA,iBAAA;EACA,wBAAA;UAAA,gBAAA;EACA,UAAA;EACA,eAAA;EACA,yBAAA;AhByvCR;AgBvvCI;EAVJ;IAWQ,oBAAA;IAAA,oBAAA;IAAA,aAAA;IACA,qBAAA;QAAA,kBAAA;YAAA,yBAAA;EhB0vCN;AACF;;AgBvvCA;EACI,kBAAA;EACA,eAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,kDAAA;EACA,qDAAA;AhB0vCJ;AgBxvCI;EACI,qCAAA;EACA,eAAA;AhB0vCR;;AgBtvCA;EACI,0BAAA;AhByvCJ;AgBxvCI;EAFJ;IAGQ,0BAAA;IACA,mBAAA;EhB2vCN;AACF;AgBzvCI;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,4BAAA;EAAA,6BAAA;MAAA,0BAAA;UAAA,sBAAA;AhB2vCR;AgBzvCQ;EACI,gBAAA;AhB2vCZ;AgBvvCI;EACI,gBAAA;EACA,iBAAA;EACA,gBAAA;AhByvCR;AgBxvCQ;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;AhB0vCZ;AgBpvCY;EACI,gBAAA;AhBsvChB;AgB9uCY;EACI,aAAA;AhBgvChB;AgB7uCQ;EACI,kBAAA;AhB+uCZ;AgB3uCY;EACI,mBAAA;AhB6uChB;AgBxuCI;EACI,kBAAA;AhB0uCR;AgBxuCQ;EACI,WAAA;EACA,YAAA;EACA,kBAAA;AhB0uCZ;AgBtuCI;EACI,kBAAA;EACA,kBAAA;EACA,mBAAA;EACA,gBAAA;EACA,WAAA;AhBwuCR;AgBtuCQ;EACI,sBX5MD;EW6MC,kBAAA;EACA,eAAA;EACA,UAAA;EACA,iBAAA;EACA,kBAAA;AhBwuCZ;AgBruCQ;EACI,WAAA;EACA,kBAAA;EACA,WAAA;EACA,WAAA;EACA,OAAA;EACA,QAAA;EACA,kDAAA;EACA,SAAA;AhBuuCZ;AgBpuCQ;EACI,eAAA;AhBsuCZ;AgBluCI;EACI,mBAAA;EACA,2BAAA;EAAA,2BAAA;EAAA,oBAAA;EACA,kBAAA;EACA,sBAAA;MAAA,mBAAA;UAAA,qBAAA;EACA,cAAA;AhBouCR;AgBluCQ;EAPJ;IAQQ,cAAA;EhBquCV;AACF;AgBnuCQ;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,mBAAA;AhBquCZ;AgBluCQ;EACI,qBAAA;EACA,sBAAA;EACA,gCXrQS;ALy+CrB;AgB/tCQ;EACI,kBAAA;EACA,2CX3QU;EW4QV,kBAAA;EACA,kBAAA;EACA,oDXuRkB;UWvRlB,4CXuRkB;AL08B9B;AgB9tCgB;EACI;IACI,aAAA;EhBguCtB;EgB7tCkB;IACI,cAAA;EhB+tCtB;AACF;AgB1tCQ;EACI,gBXnSS;EWoST,eAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,mBAAA;MAAA,eAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,QAAA;AhB4tCZ;AgBxtCY;EACI,eAAA;EACA,YAAA;EACA,cXvRL;ALi/CX;AgBxtCgB;EALJ;IAMQ,aAAA;EhB2tClB;AACF;AgBttCQ;EACI,eAAA;EACA,eAAA;EACA,iBAAA;AhBwtCZ;AgBrtCQ;EACI,qBAAA;EACA,kBAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,mBAAA;MAAA,eAAA;EACA,QAAA;AhButCZ;AgBrtCY;EACI,kBAAA;AhButChB;AgBptCY;EACI,gBAAA;AhBstChB;AgBntCY;EACI,kBAAA;EACA,WAAA;EACA,UAAA;EACA,YAAA;AhBqtChB;AgBltCoB;EACI,eAAA;EACA,WXpUb;EWqUa,qBAAA;EACA,iBAAA;EACA,WAAA;EACA,YAAA;EACA,kBAAA;EACA,uCAAA;EACA,kBAAA;AhBotCxB;AgB7sCI;EACI,qBAAA;MAAA,kBAAA;UAAA,yBAAA;AhB+sCR;AgB7sCQ;EACI,4BAAA;MAAA,iBAAA;UAAA,QAAA;EACA,iBAAA;EACA,iBAAA;AhB+sCZ;AgB5sCQ;EACI,gBAAA;EACA,cXvVD;ALqiDX;AgB3sCQ;EACI,iBAAA;AhB6sCZ;AgB3sCY;EACI,qBAAA;MAAA,kBAAA;UAAA,yBAAA;AhB6sChB;AgB3sCgB;EACI,4BAAA;MAAA,iBAAA;UAAA,QAAA;EACA,mDXpXG;EWsXH,iBAAA;EACA,wBAAA;UAAA,gBAAA;AhB4sCpB;AgB1sCoB;EACI,0CAAA;EACA,4CAAA;EACA,cXzWb;ALqjDX;AgB1sCwB;EACI,qCAAA;AhB4sC5B;AgBtsCY;EACI,qBAAA;MAAA,kBAAA;UAAA,yBAAA;AhBwsChB;AgBtsCgB;EACI,4BAAA;MAAA,iBAAA;UAAA,QAAA;AhBwsCpB;AgBrsCgB;EACI,4BAAA;MAAA,iBAAA;UAAA,QAAA;AhBusCpB;AgBpsCgB;EACI,4BAAA;MAAA,iBAAA;UAAA,QAAA;AhBssCpB;AgBlsCY;EACI,4BAAA;MAAA,iBAAA;UAAA,QAAA;AhBosChB;AgBhsCQ;EACI,yBXvYD;ALykDX;;AgB5rCI;EACI,iBAAA;EACA,gBAAA;AhB+rCR;AgB9rCQ;EAHJ;IAIQ,iBAAA;EhBisCV;AACF;;AgB7rCA;EACI,WAAA;EACA,YAAA;EACA,oBAAA;KAAA,iBAAA;AhBgsCJ;;AgB7rCA;EACI,gDAAA;EACA,WXtaO;EWuaP,kBAAA;EACA,cAAA;AhBgsCJ;;AgB7rCA;EACI,qCXrcS;EWscT,4CAAA;EACA,kCAAA;UAAA,0BAAA;EACA,kBAAA;EACA,UAAA;AhBgsCJ;AgB9rCI;EAPJ;IAQQ,eAAA;IACA,OAAA;IACA,QAAA;IACA,SAAA;IACA,UAAA;EhBisCN;AACF;AgB/rCI;EACI,kBAAA;EACA,YAAA;EACA,OAAA;EACA,QAAA;EACA,4CAAA;EACA,gBAAA;AhBisCR;AgB/rCQ;EARJ;IASQ,YAAA;EhBksCV;AACF;AgB/rCI;EACI,aAAA;EACA,kBAAA;EACA,UAAA;EACA,UAAA;EACA,eAAA;EACA,cX3aE;AL4mDV;AgB9rCI;EACI,cAAA;AhBgsCR;AgB7rCI;EACI,UAAA;AhB+rCR;;AgB3rCA;EACQ,mCXuxB4B;EWtxB5B,4CAAA;EACA,kBAAA;AhB8rCR;AgB5rCQ;EACI,gBAAA;EACA,uDAAA;AhB8rCZ;;AgBzrCA;EACI,kBAAA;EACA,OAAA;EACA,QAAA;EACA,4CAAA;EACA,gBAAA;EACA,UAAA;EACA,SAAA;EACA,4BAAA;EAAA,oBAAA;AhB4rCJ;AgB1rCI;EAVJ;IAWQ,aAAA;EhB6rCN;AACF;AgB3rCI;EACI,oCAAA;UAAA,4BAAA;EACA,UAAA;AhB6rCR;AgB3rCQ;EAJJ;IAKQ,oCAAA;YAAA,4BAAA;EhB8rCV;AACF;;AgBxrCQ;EACI,eAAA;AhB2rCZ;AgB1rCY;EACI,kDAAA;AhB4rChB;;AgBrrCA;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;AhBwrCJ;AgBtrCQ;EACI,wBAAA;UAAA,gBAAA;EACA,UAAA;EACA,eAAA;EACA,WAAA;EACA,YAAA;AhBwrCZ;AgBvrCY;EACI,yBAAA;AhByrChB;;AgBxqCC;EACC,qBAAA;EACA,UAAA;EACA,WAAA;EACA,kBAAA;EACA,kBAAA;EACA,mBXliBS;EWmiBH,4CAAA;UAAA,oCAAA;EACA,YAAA;AhB2qCR;AgBzqCE;EACC,8BAAA;UAAA,sBAAA;AhB2qCH;AgBxqCE;EACC,8BAAA;UAAA,sBAAA;AhB0qCH;;AgBrqCA;EACC;IACC,0BAAA;YAAA,kBAAA;EhBwqCA;EgBrqCD;IACC,mCAAA;YAAA,2BAAA;EhBuqCA;AACF;;AgB9qCA;EACC;IACC,0BAAA;YAAA,kBAAA;EhBwqCA;EgBrqCD;IACC,mCAAA;YAAA,2BAAA;EhBuqCA;AACF;AgBlqCA;EACI;IACI,oCAAA;EhBoqCN;AACF;AiB9wDA;EACI,aAAA;EACA,mCZkxCgC;EYjxChC,aAAA;EACA,gBAAA;EACA,gBAAA;AjBgxDJ;AiB9wDI;EAPJ;IAQQ,6CAAA;EjBixDN;AACF;AiB/wDI;EAXJ;IAYQ,eAAA;IACA,QAAA;IACA,MAAA;IACA,WAAA;EjBkxDN;AACF;AiBhxDI;EAlBJ;IAmBQ,eAAA;EjBmxDN;AACF;;AiBhxDA;EACI,kBAAA;AjBmxDJ;AiBjxDI;EACI,WAAA;EACA,kBAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,QAAA;EACA,2JAAA;EAAA,0GAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,YAAA;EACA,+BAAA;EACA,4BAAA;EAAA,6BAAA;MAAA,0BAAA;UAAA,sBAAA;AjBmxDR;AiBhxDI;EACI,eAAA;EACA,WZPG;ALyxDX;AiB/wDI;EACI,WAAA;EACA,aAAA;EACA,oBAAA;KAAA,iBAAA;AjBixDR;AiB/wDQ;EALJ;IAMQ,aAAA;EjBkxDV;AACF;AiB/wDI;EACI,aAAA;AjBixDR;AiB9wDI;EACI,eAAA;AjBgxDR;;AiB5wDA;EACI,oBAAA;KAAA,iBAAA;AjB+wDJ;;AiB5wDA;EACI,2BAAA;AjB+wDJ;AiB7wDI;EAHJ;IAIQ,2BAAA;EjBgxDN;AACF;;AiB7wDA;EACI,2BAAA;AjBgxDJ;AiB9wDI;EAHJ;IAIQ,2BAAA;EjBixDN;AACF;;AiB9wDA;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,QAAA;AjBixDJ;AiB/wDI;EACI,kBAAA;AjBixDR;AiBhxDQ;EACI,cAAA;EACA,kBAAA;EACA,kBAAA;EACA,gBAAA;AjBkxDZ;AiBjxDY;EACI,WAAA;EACA,YAAA;EACA,oBAAA;KAAA,iBAAA;AjBmxDhB;AiBhxDY;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,WZtEL;ALw1DX;AiB9wDQ;EACI,kBAAA;EACA,MAAA;EACA,QAAA;EACA,WZ9ED;EY+EC,iCAAA;EACA,WAAA;EACA,YAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,kBAAA;EACA,WAAA;AjBgxDZ;;AiBzwDQ;EACI,cZ5DF;ALw0DV;AiB3wDY;EACI,gBAAA;AjB6wDhB;;AiBrwDI;EACI,UAAA;EACA,gBZqgBsB;ALmwC9B;AiBtwDQ;EACI,yBAAA;AjBwwDZ;;AiBjwDA;EACI,2BAAA;AjBowDJ;AiBlwDI;EAHJ;IAIQ,2BAAA;EjBqwDN;AACF;;AkBl6DI;EACI,gBAAA;AlBq6DR;AkBl6DI;EACI,cbUQ;AL05DhB;AkBj6DI;EACI,abKO;AL85Df;;AkB/5DA;EACI,gDAAA;EACA,iBAAA;EACA,sBAAA;EACA,2BAAA;AlBk6DJ;;AkB/5DA;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,4BAAA;EAAA,6BAAA;MAAA,0BAAA;UAAA,sBAAA;AlBk6DJ;AkBj6DI;EAHJ;IAIQ,aAAA;ElBo6DN;AACF;AkBl6DI;EAPJ;IAQQ,kBAAA;ElBq6DN;AACF;;AkBl6DA;EACI,yBAAA;EACA,sBbEO;EaDP,mBAAA;EACA,YAAA;AlBq6DJ;;AkBl6DA;EACI,kBAAA;AlBq6DJ;AkBp6DI;EAFJ;IAGQ,eAAA;ElBu6DN;AACF;AkBt6DI;EALJ;IAMQ,eAAA;ElBy6DN;AACF;AkBx6DI;EARJ;IASQ,aAAA;ElB26DN;AACF;;AkBp6DQ;EACI,gBAAA;AlBu6DZ;;AkB/5DA;EACI,kBAAA;AlBk6DJ;AkBj6DI;EACI,WAAA;EACA,kBAAA;EACA,WAAA;EACA,WAAA;EACA,OAAA;EACA,QAAA;EACA,wCbuesB;EatetB,SAAA;AlBm6DR;AkBh6DI;EACI,qBAAA;EACA,kBAAA;EACA,UAAA;EACA,mCbmsC4B;EalsC5B,iBAAA;AlBk6DR", "file": "app-rtl.min.css", "sourcesContent": ["/*\r\nTemplate Name: Doot - Responsive Bootstrap 5 Chat App\r\nAuthor: Themesbrand\r\nVersion: 1.0.0\r\nWebsite: https://Themesbrand.com/\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\r\n\r\n//Fonts\r\n@import \"custom/fonts/fonts\";\r\n\r\n//Core files\r\n@import \"./node_modules/bootstrap/scss/functions\";\r\n@import \"./node_modules/bootstrap/scss/variables\";\r\n@import \"variables\";\r\n@import \"./node_modules/bootstrap/scss/mixins.scss\";\r\n@import \"variables\";\r\n@import \"theme-light\";\r\n@import \"theme-dark\";\r\n\r\n// Components\r\n@import \"custom/components/waves\";\r\n@import \"custom/components/avatar\";\r\n@import \"custom/components/helper\";\r\n@import \"custom/components/preloader\";\r\n@import \"custom/components/accordion\";\r\n@import \"custom/components/modals\";\r\n@import \"custom/components/forms\";\r\n\r\n\r\n// Plugins\r\n@import \"custom/plugins/custom-scrollbar\";\r\n@import \"custom/plugins/emoji-picker\";\r\n\r\n// structure\r\n@import \"custom/structure/general\";\r\n@import \"custom/structure/sidemenu\";\r\n\r\n// Pages\r\n@import \"custom/pages/chat-leftsidebar\";\r\n@import \"custom/pages/user-chat\";\r\n@import \"custom/pages/user-profile-details\";\r\n@import \"custom/pages/authentication\";\r\n\r\n", "//\r\n// Google font - Public Sans\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');\r\n\r\n//\r\n// Premium font - Cerebri Sans\r\n//\r\n\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-light.eot\");\r\n    src: local('Cerebri-sans Light'), url(\"../fonts/cerebrisans-light.woff\") format(\"woff\");\r\n    font-weight: 300;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-regular.eot\");\r\n    src: local('Cerebri-sans Regular'), url(\"../fonts/cerebrisans-regular.woff\") format(\"woff\");\r\n    font-weight: 400;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-medium.eot\");\r\n    src: local('Cerebri-sans Medium'), url(\"../fonts/cerebrisans-medium.woff\") format(\"woff\");\r\n    font-weight: 500;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-semibold.eot\");\r\n    src: local('Cerebri-sans Semibold'), url(\"../fonts/cerebrisans-semibold.woff\") format(\"woff\");\r\n    font-weight: 600;\r\n}\r\n@font-face {\r\n    font-family: \"Cerebri Sans,sans-serif\";\r\n    src: url(\"../fonts/cerebrisans-bold.eot\");\r\n    src: local('Cerebri-sans Bold'), url(\"../fonts/cerebrisans-bold.woff\") format(\"woff\");\r\n    font-weight: 700;\r\n}", "/*\nTemplate Name: Doot - Responsive Bootstrap 5 Chat App\nAuthor: Themesbrand\nVersion: 1.0.0\nWebsite: https://Themesbrand.com/\nContact: <EMAIL>\nFile: Main Css File\n*/\n@import url(\"https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap\");\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-light.eot\");\n  src: local(\"Cerebri-sans Light\"), url(\"../fonts/cerebrisans-light.woff\") format(\"woff\");\n  font-weight: 300;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-regular.eot\");\n  src: local(\"Cerebri-sans Regular\"), url(\"../fonts/cerebrisans-regular.woff\") format(\"woff\");\n  font-weight: 400;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-medium.eot\");\n  src: local(\"Cerebri-sans Medium\"), url(\"../fonts/cerebrisans-medium.woff\") format(\"woff\");\n  font-weight: 500;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-semibold.eot\");\n  src: local(\"Cerebri-sans Semibold\"), url(\"../fonts/cerebrisans-semibold.woff\") format(\"woff\");\n  font-weight: 600;\n}\n@font-face {\n  font-family: \"Cerebri Sans,sans-serif\";\n  src: url(\"../fonts/cerebrisans-bold.eot\");\n  src: local(\"Cerebri-sans Bold\"), url(\"../fonts/cerebrisans-bold.woff\") format(\"woff\");\n  font-weight: 700;\n}\n:root {\n  --bs-sidebar-bg: #2e2e2e;\n  --bs-sidebar-menu-item-color: #878a92;\n  --bs-sidebar-menu-item-active-color: #4eac6d;\n  --bs-sidebar-menu-item-active-bg: #f7f7ff;\n  --bs-sidebar-sub-bg: #ffffff;\n  --bs-footer-bg: rgba(255,255,255,.05);\n  --bs-display-none: none;\n  --bs-display-block: block;\n  --bs-chat-text-color: #495057;\n  --bs-chat-primary-bg: #ffffff;\n  --bs-light: #f6f6f9;\n  --bs-light-rgb: 246, 246, 249;\n  --bs-dark: #212529;\n  --bs-dark-rgb: 33, 37, 41;\n  --bs-text-muted: #797c8c;\n  --bs-link-color: #4eac6d;\n  --bs-link-hover-color: #4eac6d;\n  --bs-border-color: #eaeaf1;\n  --bs-dropdown-bg: #fff;\n  --bs-dropdown-border-color: #f6f6f9;\n  --bs-dropdown-link-color: #212529;\n  --bs-dropdown-link-hover-color: #1f2327;\n  --bs-dropdown-link-hover-bg: #f8f9fa;\n  --bs-dropdown-border-width: 1px;\n  --bs-card-bg: #fff;\n  --bs-card-cap-bg: #fff;\n  --bs-card-border-color: #eff0f2;\n  --bs-card-logo-dark: block;\n  --bs-card-logo-light: none;\n  --bs-card-box-shadow: 0 2px 3px #e6e8eb;\n  --bs-modal-bg: #fff;\n  --bs-modal-content-bg: #fff;\n  --bs-modal-content-border-color: #f6f6f9;\n  --bs-nav-tabs-link-active-color: #495057;\n  --bs-nav-tabs-link-active-bg: #fafafa;\n  --bs-accordion-button-active-color: #469b62;\n  --bs-accordion-bg: #fff;\n  --bs-accordion-button-bg: #f9f9f9;\n  --bs-accordion-button-active-bg:rgba(246,246,249,.7);\n  --bs-table-color: #495057;\n  --bs-boxed-body-bg: #e9ebf0;\n  --bs-body-heading-color: #495057;\n  --bs-body-bg: #f2f2f2;\n  --bs-body-color: #495057;\n  --bs-body-color-rgb: 73, 80, 87;\n  --bs-progress-bg: #f9f9f9;\n  --bs-toast-background-color: rgba(255, 255, 255, 0.85);\n  --bs-toast-border-color: rgba(0, 0, 0, 0.1);\n  --bs-toast-header-border-color: rgba(0, 0, 0, 0.05);\n  --bs-list-group-hover-bg: #f8f9fa;\n  --bs-popover-bg: #fff;\n  --bs-pagination-hover-bg: #f9f9f9;\n  --bs-input-bg: #fff;\n  --bs-input-group-addon-border-color: #e6ebf5;\n  --bs-input-border: #e6ebf5;\n  --bs-input-border-color: #cfd4d8;\n  --bs-input-focus-border: #a7d6b6;\n  --bs-input-disabled-bg: #f9f9f9;\n  --bs-input-placeholder-color: #797c8c;\n  --bs-input-group-addon-bg: #f9f9f9;\n  --bs-input-check-border: var(--bs-input-border);\n}\n\n[data-layout-mode=dark] {\n  --bs-gray-100: #212529;\n  --bs-gray-200: #262626;\n  --bs-gray-300: #2e2e2e;\n  --bs-gray-400: #757575;\n  --bs-gray-500: #8f9198;\n  --bs-gray-600: #adb5bd;\n  --bs-gray-700: #adb5bd;\n  --bs-gray-800: #f9f9f9;\n  --bs-gray-900: #f8f9fa;\n  --bs-sidebar-bg: #2e2e2e;\n  --bs-sidebar-menu-item-color: #878a92;\n  --bs-sidebar-menu-item-active-color: #4eac6d;\n  --bs-sidebar-menu-item-active-bg: #f7f7ff;\n  --bs-sidebar-sub-bg: #262626;\n  --bs-footer-bg: rgba(46,46,46,.5);\n  --bs-display-none: block;\n  --bs-display-block: none;\n  --bs-chat-text-color: rgba(255,255,255,.8);\n  --bs-chat-primary-bg: #383838;\n  --bs-card-title-desc: #757575;\n  --bs-topnav-bg: #2c2c2c;\n  --bs-topnav-item-color: #757575;\n  --bs-topnav-item-color-active: #fff;\n  --bs-twocolumn-menu-iconview-bg: #fff;\n  --bs-twocolumn-menu-bg: #fff;\n  --bs-twocolumn-menu-iconview-bg-dark: var(--bs-vertical-menu-bg-dark);\n  --bs-twocolumn-menu-bg-dark: #30363a;\n  --bs-twocolumn-menu-item-color-dark: var(--bs-vertical-menu-item-color-dark);\n  --bs-twocolumn-menu-item-active-color-dark: #fff;\n  --bs-twocolumn-menu-item-active-bg-dark: rgba(255, 255, 255, 0.15);\n  --bs-boxed-body-bg: #181b1e;\n  --bs-heading-color: #adb5bd;\n  --bs-light: #2e2e2e;\n  --bs-light-rgb: 46, 46, 46;\n  --bs-dark: #f9f9f9;\n  --bs-dark-rgb: 249, 249, 249;\n  --bs-text-muted: #8f9198;\n  --bs-link-color: #adb5bd;\n  --bs-link-hover-color: #adb5bd;\n  --bs-border-color: #333333;\n  --bs-dropdown-bg: #333333;\n  --bs-dropdown-border-color: #383838;\n  --bs-dropdown-link-color: #8f9198;\n  --bs-dropdown-link-hover-color: #8f9198;\n  --bs-dropdown-link-hover-bg: #383838;\n  --bs-dropdown-border-width: 1px;\n  --bs-card-bg: #262626;\n  --bs-card-cap-bg: #262626;\n  --bs-card-border-color: #082a3e;\n  --bs-card-logo-dark: none;\n  --bs-card-logo-light: block;\n  --bs-card-box-shadow: 0 2px 3px #04121a;\n  --bs-modal-bg: #262626;\n  --bs-modal-content-bg: #262626;\n  --bs-modal-content-border-color: #2e2e2e;\n  --bs-nav-tabs-link-active-color: #f8f9fa;\n  --bs-nav-tabs-link-active-bg: #2e2e2e;\n  --bs-accordion-button-active-color: #fff;\n  --bs-accordion-bg: transparent;\n  --bs-accordion-button-bg: #2e2e2e;\n  --bs-accordion-button-active-bg:#2e2e2e;\n  --bs-table-color: #757575;\n  --bs-boxed-body-bg: #333847;\n  --bs-body-heading-color: #adb5bd;\n  --bs-body-bg: #2e2e2e;\n  --bs-body-color: #8f9198;\n  --bs-body-color-rgb: 73, 80, 87;\n  --bs-progress-bg: #2e2e2e;\n  --bs-toast-background-color: rgba(46, 46, 46, 0.85);\n  --bs-toast-border-color: rgba(255, 255, 255, 0.1);\n  --bs-toast-header-border-color: rgba(255, 255, 255, 0.05);\n  --bs-list-group-hover-bg: #2c2c2c;\n  --bs-popover-bg: #292929;\n  --bs-pagination-hover-bg: #303030;\n  --bs-input-bg: #333333;\n  --bs-input-group-addon-border-color: #2e2e2e;\n  --bs-input-border: #2e2e2e;\n  --bs-input-border-color: #333333;\n  --bs-input-focus-border: #383838;\n  --bs-input-disabled-bg: #262626;\n  --bs-input-placeholder-color: #8f9198;\n  --bs-input-group-addon-bg: #2e2e2e;\n  --bs-input-check-border: #383838;\n}\n\n/*!\n * Waves v0.7.6\n * http://fian.my.id/Waves \n * \n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \n * Released under the MIT license \n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n}\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important;\n}\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\n}\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1;\n}\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em;\n}\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em;\n}\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom;\n}\n\n.waves-input-wrapper.waves-button {\n  padding: 0;\n}\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%;\n}\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms;\n}\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n}\n\n.waves-block {\n  display: block;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4);\n}\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(78, 172, 109, 0.4);\n}\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(6, 214, 160, 0.4);\n}\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(80, 165, 241, 0.4);\n}\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(255, 209, 102, 0.4);\n}\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(239, 71, 111, 0.4);\n}\n\n.avatar-xs {\n  height: 1.8rem;\n  width: 1.8rem;\n}\n\n.avatar-sm {\n  height: 2.4rem;\n  width: 2.4rem;\n}\n\n.avatar-md {\n  height: 4rem;\n  width: 4rem;\n}\n\n.avatar-lg {\n  height: 5rem;\n  width: 5rem;\n}\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: #fff;\n  display: flex;\n  font-weight: 500;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 8px;\n}\n.avatar-group .avatar-group-item {\n  margin-left: -8px;\n  border: 2px solid var(--bs-card-bg);\n  border-radius: 50%;\n  transition: all 0.2s;\n}\n.avatar-group .avatar-group-item:hover {\n  position: relative;\n  transform: translateY(-2px);\n}\n\n.font-size-10 {\n  font-size: 10px !important;\n}\n\n.font-size-11 {\n  font-size: 11px !important;\n}\n\n.font-size-12 {\n  font-size: 12px !important;\n}\n\n.font-size-13 {\n  font-size: 13px !important;\n}\n\n.font-size-14 {\n  font-size: 14px !important;\n}\n\n.font-size-15 {\n  font-size: 15px !important;\n}\n\n.font-size-16 {\n  font-size: 16px !important;\n}\n\n.font-size-17 {\n  font-size: 17px !important;\n}\n\n.font-size-18 {\n  font-size: 18px !important;\n}\n\n.font-size-20 {\n  font-size: 20px !important;\n}\n\n.font-size-22 {\n  font-size: 22px !important;\n}\n\n.font-size-24 {\n  font-size: 24px !important;\n}\n\n.fw-medium {\n  font-weight: 500;\n}\n\n.fw-semibold {\n  font-weight: 600;\n}\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 4px);\n  display: block;\n  border: 2px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd;\n  text-align: center;\n  transition: all 0.4s;\n}\n.social-list-item:hover {\n  color: #797c8c;\n  background-color: #f9f9f9;\n}\n\n.w-xs {\n  min-width: 80px;\n}\n\n.w-sm {\n  min-width: 95px;\n}\n\n.w-md {\n  min-width: 110px;\n}\n\n.w-lg {\n  min-width: 140px;\n}\n\n.w-xl {\n  min-width: 160px;\n}\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  background-color: rgba(52, 58, 64, 0.7);\n}\n\n.border-primary {\n  border-color: rgba(var(--bs-primary-rgb), 1) !important;\n}\n\n.border-dark {\n  border-color: var(--bs-dark) !important;\n}\n\n.border-light {\n  border-color: var(--bs-light) !important;\n}\n\n[data-layout-mode=dark] .text-body {\n  color: var(--bs-gray-500) !important;\n}\n[data-layout-mode=dark] .btn-close {\n  filter: invert(1) grayscale(100%) brightness(200%);\n}\n\n.loader-line {\n  height: 28px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n.loader-line .line:nth-last-child(1) {\n  animation: loadingLine 1.25s 1s infinite;\n}\n.loader-line .line:nth-last-child(2) {\n  animation: loadingLine 1.25s 0.75s infinite;\n}\n.loader-line .line:nth-last-child(3) {\n  animation: loadingLine 1.25s 0.5s infinite;\n}\n.loader-line .line:nth-last-child(4) {\n  animation: loadingLine 1.25s 0.25s infinite;\n}\n.loader-line .line:nth-last-child(5) {\n  animation: loadingLine 1.25s 0s infinite;\n}\n\n.line {\n  display: inline-block;\n  width: 4px;\n  height: 10px;\n  border-radius: 14px;\n  background-color: rgba(var(--bs-text-muted), 0.7);\n}\n\n@keyframes loadingLine {\n  0% {\n    height: 10px;\n  }\n  50% {\n    height: 28px;\n  }\n  100% {\n    height: 10px;\n  }\n}\n.custom-accordion .card + .card {\n  margin-top: 0.5rem;\n}\n.custom-accordion a i.accor-plus-icon {\n  font-size: 16px;\n}\n.custom-accordion a.collapsed i.accor-plus-icon:before {\n  content: \"\\f0142\";\n}\n\n.accordion-button:after {\n  margin-left: auto;\n  margin-right: 0;\n}\n\n[data-layout-mode=dark] .accordion-button:after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23adb5bd'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e\");\n}\n\n.modal-header-colored .modal-header {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  margin: 0 -1px -1px -1px;\n}\n\n[type=tel]::placeholder,\n[type=url]::placeholder,\n[type=email]::placeholder,\n[type=number]::placeholder {\n  text-align: left;\n  /*rtl: right*/\n}\n\n.form-check,\n.form-check-input,\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0;\n}\n\n.form-check-input:checked {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  border-color: rgba(var(--bs-primary-rgb), 1);\n}\n\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto;\n  /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: \" \";\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 7px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: \"\";\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}\n\n.fg-emoji-picker {\n  background-color: var(--bs-card-bg) !important;\n  width: 250px !important;\n  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12) !important;\n  top: auto !important;\n  bottom: 70px;\n}\n.fg-emoji-picker * {\n  font-family: var(--bs-font-sans-serif) !important;\n  color: #495057 !important;\n}\n@media (max-width: 991.98px) {\n  .fg-emoji-picker {\n    left: 14px !important;\n    top: auto !important;\n    bottom: 60px;\n  }\n}\n.fg-emoji-picker .fg-emoji-picker-container-title {\n  color: var(--bs-dark) !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search {\n  height: 40px !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search input {\n  background-color: var(--bs-input-bg) !important;\n  color: var(--bs-body-color) !important;\n  border: 1px solid var(--bs-border-color);\n  padding: 0.5rem 1rem !important;\n  font-size: 0.9375rem !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search input::placeholder {\n  color: var(--bs-input-placeholder-color) !important;\n}\n.fg-emoji-picker .fg-emoji-picker-search svg {\n  fill: var(--bs-body-color) !important;\n  right: 11px;\n  top: 12px;\n}\n.fg-emoji-picker .fg-emoji-picker-categories {\n  background-color: var(--bs-light) !important;\n}\n.fg-emoji-picker .fg-emoji-picker-categories li.active {\n  background-color: rgba(var(--bs-primary-rgb), 0.2);\n}\n.fg-emoji-picker .fg-emoji-picker-categories a:hover {\n  background-color: rgba(var(--bs-primary-rgb), 0.2);\n}\n\n.fg-emoji-picker-grid > li:hover {\n  background-color: rgba(var(--bs-primary-rgb), 0.2) !important;\n}\n\na.fg-emoji-picker-close-button {\n  background-color: var(--bs-light) !important;\n}\n\n.fg-emoji-picker-categories svg {\n  fill: var(--bs-body-color) !important;\n}\n\n.fg-emoji-picker-grid > li {\n  background-color: var(--bs-card-bg) !important;\n}\n\n.ml-44 {\n  margin-left: 44px;\n}\n\n.side-menu {\n  min-width: 75px;\n  max-width: 75px;\n  height: 100vh;\n  min-height: 570px;\n  background-color: var(--bs-sidebar-bg);\n  display: flex;\n  z-index: 9;\n  border-right: 1px solid var(--bs-sidebar-bg);\n  padding: 0 8px;\n}\n@media (max-width: 991.98px) {\n  .side-menu {\n    position: fixed;\n    bottom: 0;\n    height: 60px;\n    min-width: 100%;\n    min-height: auto;\n    border-top: 1px solid var(--bs-sidebar-bg);\n  }\n}\n.side-menu .navbar-brand-box {\n  text-align: center;\n}\n.side-menu .navbar-brand-box svg {\n  fill: rgba(var(--bs-primary-rgb), 1);\n}\n@media (max-width: 991.98px) {\n  .side-menu .navbar-brand-box {\n    display: none;\n  }\n}\n.side-menu .navbar-brand-box .logo {\n  line-height: 70px;\n}\n.side-menu .navbar-brand-box .logo-dark {\n  display: block;\n}\n.side-menu .navbar-brand-box .logo-light {\n  display: none;\n}\n\n.sidemenu-navigation {\n  height: 100%;\n}\n@media (max-width: 991.98px) {\n  .sidemenu-navigation {\n    width: 100%;\n  }\n  .sidemenu-navigation .tooltip {\n    display: none;\n  }\n}\n\n.side-menu-nav {\n  height: 100%;\n  flex-direction: column;\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav {\n    justify-content: space-between !important;\n    flex-direction: row;\n  }\n}\n.side-menu-nav .nav-item {\n  margin: 7px 0;\n  display: block;\n  width: 100%;\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    margin: 5px 0;\n  }\n}\n.side-menu-nav .nav-item .nav-link {\n  text-align: center;\n  font-size: 28px;\n  color: var(--bs-sidebar-menu-item-color);\n  width: 100%;\n  height: 56px;\n  line-height: 56px;\n  border-radius: 0;\n  padding: 0;\n  position: relative;\n}\n.side-menu-nav .nav-item .nav-link i {\n  vertical-align: middle;\n}\n.side-menu-nav .nav-item .nav-link::before {\n  position: absolute;\n  content: \"\";\n  height: 20px;\n  width: 2px;\n  right: -8px;\n  top: 18px;\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav .nav-item .nav-link::before {\n    width: 20px;\n    height: 2px;\n    right: auto;\n    top: -5px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n}\n@media (max-width: 991.98px) {\n  .side-menu-nav .nav-item .nav-link {\n    font-size: 20px;\n    width: 48px;\n    height: 48px;\n    line-height: 48px;\n    margin: 0px auto;\n  }\n}\n.side-menu-nav .nav-item .nav-link.active {\n  background-color: transparent;\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.side-menu-nav .nav-item .nav-link.active::before {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n}\n.side-menu-nav .nav-item.show > .nav-link {\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.side-menu-nav .profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: var(--bs-gray-300);\n  padding: 3px;\n}\n\n.light-mode {\n  display: none;\n}\n\nbody[data-layout-mode=dark] .light-mode {\n  display: inline-block;\n}\nbody[data-layout-mode=dark] .dark-mode {\n  display: none;\n}\n\nbody[data-layout-mode=dark] .side-menu-nav .nav-item .nav-link.light-dark .bx-moon:before {\n  content: \"\\eb90\";\n}\nbody[data-layout-mode=dark] .light-mode {\n  display: inline-block;\n}\nbody[data-layout-mode=dark] .dark-mode {\n  display: none;\n}\n\n.chat-leftsidebar {\n  height: calc(100vh - 60px);\n  position: relative;\n  background-color: var(--bs-sidebar-sub-bg);\n  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);\n}\n@media (min-width: 992px) {\n  .chat-leftsidebar {\n    min-width: 300px;\n    max-width: 300px;\n    height: 100vh;\n  }\n}\n.chat-leftsidebar .user-status-box {\n  background-color: #f6f6f9;\n  padding: 8px;\n  border-radius: 8px;\n  text-align: center;\n  margin-top: 16px;\n  display: block;\n}\n.chat-leftsidebar .user-status-box .chat-user-img {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n}\n\n.chat-search-box .form-control {\n  border: 0;\n}\n.chat-search-box .search-icon-btn {\n  font-size: 16px;\n  position: absolute;\n  right: 13px;\n  top: 0;\n}\n\n.chat-room-list {\n  max-height: calc(100vh - 130px);\n}\n@media (max-width: 991.98px) {\n  .chat-room-list {\n    height: calc(100vh - 190px);\n  }\n}\n\n.chat-group-list {\n  height: calc(100vh - 140px);\n}\n@media (max-width: 991.98px) {\n  .chat-group-list {\n    height: calc(100vh - 198px);\n  }\n}\n\n.chat-list {\n  margin: 0;\n}\n.chat-list li.active a {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: #fff;\n}\n.chat-list li.active a span.avatar-title {\n  color: #fff !important;\n}\n.chat-list li.active a span.avatar-title.bg-light {\n  background-color: rgba(246, 246, 249, 0.25) !important;\n}\n.chat-list li.active a .badge {\n  background-color: rgba(246, 246, 249, 0.25) !important;\n  color: #fff !important;\n}\n.chat-list li.active a .bg-primary {\n  background-color: rgba(255, 255, 255, 0.25) !important;\n}\n.chat-list li a {\n  display: block;\n  padding: 5px 24px;\n  color: var(--bs-gray-700);\n  transition: all 0.4s;\n  font-size: 14px;\n}\n.chat-list li .chat-user-message {\n  font-size: 14px;\n}\n.chat-list li .unread-msg-user {\n  font-weight: 600;\n}\n.chat-list li.typing .chat-user-message {\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-weight: 500;\n}\n.chat-list li.typing .chat-user-message .dot {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n}\n.chat-list li .unread-message {\n  position: absolute;\n  display: inline-block;\n  right: 24px;\n  left: auto;\n  top: 33px;\n}\n.chat-list li .unread-message .badge {\n  line-height: 16px;\n  font-weight: 600;\n  font-size: 10px;\n}\n\n.chat-user-img {\n  position: relative;\n}\n.chat-user-img .user-status {\n  width: 10px;\n  height: 10px;\n  background-color: #adb5bd;\n  border-radius: 50%;\n  border: 2px solid var(--bs-card-bg);\n  position: absolute;\n  right: 0;\n  left: auto;\n  bottom: 0;\n}\n.chat-user-img.online .user-status {\n  background-color: #06D6A0;\n}\n.chat-user-img.away .user-status {\n  background-color: #FFD166;\n}\n\n.contact-list li {\n  cursor: pointer;\n  padding: 8px 24px;\n}\n\n.contact-list-title {\n  padding: 6px 24px;\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-weight: 500;\n  position: relative;\n  font-size: 12px;\n}\n.contact-list-title:after {\n  content: \"\";\n  height: 1px;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  left: 50px;\n  right: 0;\n  background-color: var(--bs-border-color);\n}\n\n.chat-call-list, .chat-bookmark-list {\n  max-height: calc(100vh - 68px);\n}\n@media (max-width: 991.98px) {\n  .chat-call-list, .chat-bookmark-list {\n    height: calc(100vh - 125px);\n  }\n}\n.chat-call-list li, .chat-bookmark-list li {\n  position: relative;\n  padding: 10px 24px;\n  border-bottom: 1px solid var(--bs-border-color);\n}\n.chat-call-list li:last-child, .chat-bookmark-list li:last-child {\n  border-bottom: 0;\n}\n\n.profile-user {\n  position: relative;\n  display: inline-block;\n}\n.profile-user .profile-photo-edit {\n  position: absolute;\n  right: 0;\n  left: auto;\n  bottom: 0;\n  cursor: pointer;\n}\n.profile-user .user-profile-img {\n  object-fit: cover;\n}\n.profile-user .profile-img-file-input {\n  display: none;\n}\n\n.theme-btn-list.theme-color-list .form-check .form-check-input:checked + .form-check-label::before {\n  color: #fff;\n}\n.theme-btn-list .form-check {\n  padding: 0;\n}\n.theme-btn-list .form-check .form-check-input {\n  display: none;\n}\n.theme-btn-list .form-check .form-check-label {\n  position: relative;\n}\n.theme-btn-list .form-check .form-check-input:checked + .form-check-label::before {\n  content: \"\\e9a4\";\n  font-family: boxicons !important;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translateX(-50%) translateY(-50%);\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-size: 16px;\n  z-index: 1;\n}\n.theme-btn-list .form-check .form-check-input:checked + .form-check-label.light-background::before {\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.theme-btn-list.theme-btn-list-img .theme-btn {\n  background-color: transparent !important;\n}\n\n.theme-btn {\n  cursor: pointer;\n  border: 1px solid var(--bs-gray-400);\n}\n\n.user-chat {\n  background: url(\"../images/bg-pattern/pattern-05.png\");\n  transition: all 0.4s;\n  position: relative;\n  background-color: var(--bs-body-bg);\n}\n.user-chat .user-chat-overlay {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  background-color: transparent;\n  opacity: 0.1;\n}\n@media (max-width: 991.98px) {\n  .user-chat {\n    position: fixed;\n    left: 0;\n    top: 0;\n    width: 100%;\n    height: 100%;\n    visibility: hidden;\n    transform: translateX(100%);\n    z-index: 99;\n  }\n  .user-chat.user-chat-show {\n    visibility: visible;\n    transform: translateX(0);\n  }\n}\n.user-chat .chat-content {\n  position: relative;\n}\n.user-chat.user-chat-show .chat-welcome-section {\n  display: none;\n}\n@media (min-width: 992px) {\n  .user-chat.user-chat-show .chat-content {\n    display: flex !important;\n  }\n}\n\n.chat-welcome-section {\n  display: flex;\n  width: 100%;\n  height: 100vh;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n@media (max-width: 991.98px) {\n  .chat-welcome-section {\n    display: none;\n  }\n}\n\n.copyclipboard-alert {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.user-chat-topbar {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  z-index: 1;\n  background-color: rgba(255, 255, 255, 0.05);\n  border-bottom: 1px solid var(--bs-border-color);\n  backdrop-filter: blur(7px);\n}\n@media (max-width: 991.98px) {\n  .user-chat-topbar {\n    position: fixed;\n    background-color: rgba(255, 255, 255, 0.8);\n  }\n}\n.user-chat-topbar .topbar-bookmark {\n  position: absolute;\n  bottom: -51px;\n  left: 0;\n  right: 0;\n  border-radius: 0;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-links {\n  color: #cc8f00;\n  font-size: 14px;\n  padding: 1px 16px;\n  border-right: 1px solid rgba(255, 209, 102, 0.7);\n  white-space: nowrap;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-links:first-child {\n  padding-left: 4px;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link {\n  display: flex;\n  overflow-x: auto;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar {\n  -webkit-appearance: none;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar:vertical {\n  width: 12px;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar:horizontal {\n  height: 5px;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar-thumb {\n  background-color: rgba(52, 58, 64, 0.1);\n  border-radius: 10px;\n  border: 2px solid transparent;\n}\n.user-chat-topbar .topbar-bookmark .bookmark-tabs .tab-list-link::-webkit-scrollbar-track {\n  border-radius: 10px;\n}\n.user-chat-topbar .topbar-bookmark .btn-close {\n  padding: 12px 20px;\n}\n\n.call-close-btn {\n  box-shadow: 0px 0px 0 6px var(--bs-card-bg);\n}\n\n.user-chat-nav .nav-btn {\n  height: 40px;\n  width: 40px;\n  line-height: 40px;\n  box-shadow: none;\n  padding: 0;\n  font-size: 22px;\n  color: var(--bs-gray-600);\n}\n@media (max-width: 575.98px) {\n  .user-chat-nav {\n    display: flex;\n    justify-content: flex-end;\n  }\n}\n\n.replymessage-block {\n  padding: 12px 20px;\n  font-size: 14px;\n  margin-bottom: 8px;\n  text-align: left;\n  border-radius: 4px;\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\n  border-left: 2px solid rgba(var(--bs-primary-rgb), 1);\n}\n.replymessage-block .conversation-name {\n  color: rgba(var(--bs-primary-rgb), 1);\n  font-size: 15px;\n}\n\n.chat-conversation {\n  height: calc(100vh - 94px);\n}\n@media (max-width: 991.98px) {\n  .chat-conversation {\n    height: calc(100vh - 80px);\n    margin-bottom: 78px;\n  }\n}\n.chat-conversation .simplebar-content-wrapper {\n  display: flex;\n  flex-direction: column;\n}\n.chat-conversation .simplebar-content-wrapper .simplebar-content {\n  margin-top: auto;\n}\n.chat-conversation .chat-conversation-list {\n  margin-top: 90px;\n  padding-top: 10px;\n  margin-bottom: 0;\n}\n.chat-conversation .chat-conversation-list > li {\n  display: flex;\n}\n.chat-conversation li:last-of-type .conversation-list {\n  margin-bottom: 0;\n}\n.chat-conversation .chat-list.left .check-message-icon {\n  display: none;\n}\n.chat-conversation .chat-list .message-box-drop {\n  visibility: hidden;\n}\n.chat-conversation .chat-list:hover .message-box-drop {\n  visibility: visible;\n}\n.chat-conversation .chat-avatar {\n  margin: 0 16px 0 0;\n}\n.chat-conversation .chat-avatar img {\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n}\n.chat-conversation .chat-day-title {\n  position: relative;\n  text-align: center;\n  margin-bottom: 24px;\n  margin-top: 12px;\n  width: 100%;\n}\n.chat-conversation .chat-day-title .title {\n  background-color: #fff;\n  position: relative;\n  font-size: 13px;\n  z-index: 1;\n  padding: 6px 12px;\n  border-radius: 5px;\n}\n.chat-conversation .chat-day-title:before {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 1px;\n  left: 0;\n  right: 0;\n  background-color: rgba(var(--bs-primary-rgb), 0.2);\n  top: 10px;\n}\n.chat-conversation .chat-day-title .badge {\n  font-size: 12px;\n}\n.chat-conversation .conversation-list {\n  margin-bottom: 24px;\n  display: inline-flex;\n  position: relative;\n  align-items: flex-end;\n  max-width: 80%;\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .conversation-list {\n    max-width: 90%;\n  }\n}\n.chat-conversation .conversation-list .ctext-wrap {\n  display: flex;\n  margin-bottom: 10px;\n}\n.chat-conversation .conversation-list .ctext-content {\n  word-wrap: break-word;\n  word-break: break-word;\n  color: var(--bs-chat-text-color);\n}\n.chat-conversation .conversation-list .ctext-wrap-content {\n  padding: 12px 20px;\n  background-color: var(--bs-chat-primary-bg);\n  position: relative;\n  border-radius: 3px;\n  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .attached-file-avatar {\n    display: none;\n  }\n  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .dropdown .dropdown-toggle {\n    display: block;\n  }\n}\n.chat-conversation .conversation-list .conversation-name {\n  font-weight: 500;\n  font-size: 14px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  gap: 8px;\n}\n.chat-conversation .conversation-list .dropdown .dropdown-toggle {\n  font-size: 18px;\n  padding: 4px;\n  color: #797c8c;\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n    display: none;\n  }\n}\n.chat-conversation .conversation-list .chat-time {\n  font-size: 12px;\n  margin-top: 4px;\n  text-align: right;\n}\n.chat-conversation .conversation-list .message-img {\n  border-radius: 0.2rem;\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n.chat-conversation .conversation-list .message-img .message-img-list {\n  position: relative;\n}\n.chat-conversation .conversation-list .message-img img {\n  max-width: 150px;\n}\n.chat-conversation .conversation-list .message-img .message-img-link {\n  position: absolute;\n  right: 10px;\n  left: auto;\n  bottom: 10px;\n}\n.chat-conversation .conversation-list .message-img .message-img-link li > a {\n  font-size: 18px;\n  color: #fff;\n  display: inline-block;\n  line-height: 20px;\n  width: 26px;\n  height: 24px;\n  border-radius: 3px;\n  background-color: rgba(52, 58, 64, 0.7);\n  text-align: center;\n}\n.chat-conversation .right {\n  justify-content: flex-end;\n}\n.chat-conversation .right .chat-avatar {\n  order: 3;\n  margin-right: 0px;\n  margin-left: 16px;\n}\n.chat-conversation .right .chat-time {\n  text-align: left;\n  color: #797c8c;\n}\n.chat-conversation .right .conversation-list {\n  text-align: right;\n}\n.chat-conversation .right .conversation-list .ctext-wrap {\n  justify-content: flex-end;\n}\n.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content {\n  order: 2;\n  background-color: rgba(var(--bs-primary-rgb), 0.23);\n  text-align: right;\n  box-shadow: none;\n}\n.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block {\n  background-color: rgba(255, 255, 255, 0.5);\n  border-color: rgba(var(--bs-primary-rgb), 1);\n  color: #495057;\n}\n.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block .conversation-name {\n  color: rgba(var(--bs-primary-rgb), 1);\n}\n.chat-conversation .right .conversation-list .conversation-name {\n  justify-content: flex-end;\n}\n.chat-conversation .right .conversation-list .conversation-name .check-message-icon {\n  order: 1;\n}\n.chat-conversation .right .conversation-list .conversation-name .time {\n  order: 2;\n}\n.chat-conversation .right .conversation-list .conversation-name .name {\n  order: 3;\n}\n.chat-conversation .right .conversation-list .dropdown {\n  order: 1;\n}\n.chat-conversation .right .dot {\n  background-color: #343a40;\n}\n\n.videocallModal .modal-content {\n  min-height: 450px;\n  overflow: hidden;\n}\n@media (max-width: 575.98px) {\n  .videocallModal .modal-content {\n    min-height: 350px;\n  }\n}\n\n.videocallModal-bg {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-chat-remove {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: #fff;\n  border-radius: 3px;\n  line-height: 1;\n}\n\n.chat-input-section {\n  background-color: var(--bs-footer-bg);\n  border-top: 1px solid var(--bs-border-color);\n  backdrop-filter: blur(7px);\n  position: relative;\n  z-index: 1;\n}\n@media (max-width: 991.98px) {\n  .chat-input-section {\n    position: fixed;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 1;\n  }\n}\n.chat-input-section .chat-input-collapse {\n  position: absolute;\n  bottom: 92px;\n  left: 0;\n  right: 0;\n  border-top: 1px solid var(--bs-border-color);\n  overflow: hidden;\n}\n@media (max-width: 991.98px) {\n  .chat-input-section .chat-input-collapse {\n    bottom: 74px;\n  }\n}\n.chat-input-section .chat-input-feedback {\n  display: none;\n  position: absolute;\n  top: -18px;\n  left: 16px;\n  font-size: 12px;\n  color: #EF476F;\n}\n.chat-input-section .show {\n  display: block;\n}\n.chat-input-section .replyCollapse {\n  z-index: 1;\n}\n\n.file_Upload {\n  background-color: var(--bs-card-bg);\n  border-top: 1px solid var(--bs-border-color);\n  padding: 16px 24px;\n}\n.file_Upload .card {\n  margin-bottom: 0;\n  border-color: rgba(var(--bs-primary-rgb), 1) !important;\n}\n\n.replyCard, .file_Upload {\n  position: absolute;\n  left: 0;\n  right: 0;\n  border-top: 1px solid var(--bs-border-color);\n  overflow: hidden;\n  opacity: 0;\n  bottom: 0;\n  transition: all 0.4s;\n}\n@media (max-width: 991.98px) {\n  .replyCard, .file_Upload {\n    bottom: -12px;\n  }\n}\n.replyCard.show, .file_Upload.show {\n  transform: translateY(-92px);\n  opacity: 1;\n}\n@media (max-width: 991.98px) {\n  .replyCard.show, .file_Upload.show {\n    transform: translateY(-86px);\n  }\n}\n\n.contact-modal-list .contact-list li {\n  margin: 2px 0px;\n}\n.contact-modal-list .contact-list li.selected {\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\n}\n\n.chat-input-links {\n  display: flex;\n}\n.chat-input-links .links-list-item > .btn {\n  box-shadow: none;\n  padding: 0;\n  font-size: 22px;\n  width: 43px;\n  height: 43px;\n}\n.chat-input-links .links-list-item > .btn.btn-link {\n  color: var(--bs-gray-600);\n}\n\n.animate-typing .dot {\n  display: inline-block;\n  width: 4px;\n  height: 4px;\n  border-radius: 50%;\n  margin-right: -1px;\n  background: #343a40;\n  animation: wave 1.3s linear infinite;\n  opacity: 0.6;\n}\n.animate-typing .dot:nth-child(2) {\n  animation-delay: -1.1s;\n}\n.animate-typing .dot:nth-child(3) {\n  animation-delay: -0.9s;\n}\n\n@keyframes wave {\n  0%, 60%, 100% {\n    transform: initial;\n  }\n  30% {\n    transform: translateY(-5px);\n  }\n}\n@-moz-document url-prefix() {\n  .user-chat-topbar, .chat-input-section {\n    background-color: #f2f2f2 !important;\n  }\n}\n.user-profile-sidebar {\n  height: 100vh;\n  background-color: var(--bs-card-bg);\n  display: none;\n  min-width: 380px;\n  max-width: 380px;\n}\n@media (min-width: 992px) {\n  .user-profile-sidebar {\n    border-left: 4px solid var(--bs-border-color);\n  }\n}\n@media (max-width: 1199.98px) {\n  .user-profile-sidebar {\n    position: fixed;\n    right: 0;\n    top: 0;\n    z-index: 99;\n  }\n}\n@media (max-width: 575.98px) {\n  .user-profile-sidebar {\n    min-width: 100%;\n  }\n}\n\n.user-profile-img {\n  position: relative;\n}\n.user-profile-img .overlay-content {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 10%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.5) 100%);\n  display: flex;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  flex-direction: column;\n}\n.user-profile-img .user-name {\n  font-size: 16px;\n  color: #fff;\n}\n.user-profile-img .profile-img {\n  width: 100%;\n  height: 250px;\n  object-fit: cover;\n}\n@media (max-width: 991px) {\n  .user-profile-img .profile-img {\n    height: 160px;\n  }\n}\n.user-profile-img .profile-foreground-img-file-input {\n  display: none;\n}\n.user-profile-img .profile-photo-edit {\n  cursor: pointer;\n}\n\n.user-profile-image {\n  object-fit: cover;\n}\n\n.user-profile-desc {\n  height: calc(100vh - 285px);\n}\n@media (max-width: 991.98px) {\n  .user-profile-desc {\n    height: calc(100vh - 194px);\n  }\n}\n\n.profile-desc {\n  height: calc(100vh - 285px);\n}\n@media (max-width: 991.98px) {\n  .profile-desc {\n    height: calc(100vh - 330px);\n  }\n}\n\n.profile-media-img {\n  display: flex;\n  gap: 8px;\n}\n.profile-media-img .media-img-list {\n  position: relative;\n}\n.profile-media-img .media-img-list a {\n  display: block;\n  position: relative;\n  border-radius: 4px;\n  overflow: hidden;\n}\n.profile-media-img .media-img-list a img {\n  width: 76px;\n  height: 76px;\n  object-fit: cover;\n}\n.profile-media-img .media-img-list a .bg-overlay {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #fff;\n}\n.profile-media-img .media-img-list .image-remove {\n  position: absolute;\n  top: 0;\n  right: 0;\n  color: #fff;\n  background: rgba(52, 58, 64, 0.7);\n  width: 18px;\n  height: 18px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border-radius: 2px;\n  margin: 4px;\n}\n\n.favourite-btn.active .bx-heart {\n  color: #EF476F;\n}\n.favourite-btn.active .bx-heart:before {\n  content: \"\\ed36\";\n}\n\n.edit-input.form-control[readonly] {\n  padding: 0;\n  font-weight: 500;\n}\n.edit-input.form-control[readonly]:focus {\n  border-color: transparent;\n}\n\n.user-setting {\n  height: calc(100vh - 288px);\n}\n@media (max-width: 991.98px) {\n  .user-setting {\n    height: calc(100vh - 320px);\n  }\n}\n\n.auth-logo .logo {\n  margin: 0px auto;\n}\n.auth-logo .logo-dark {\n  display: block;\n}\n.auth-logo .logo-light {\n  display: none;\n}\n\n.auth-bg {\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  min-height: 100vh;\n  background-size: cover;\n  background-position: center;\n}\n\n.auth-logo-section {\n  display: flex;\n  flex-direction: column;\n}\n@media (min-width: 992px) {\n  .auth-logo-section {\n    height: 100vh;\n  }\n}\n@media (max-width: 991.98px) {\n  .auth-logo-section {\n    text-align: center;\n  }\n}\n\n.authentication-page-content {\n  height: calc(100% - 48px);\n  background-color: #fff;\n  border-radius: 16px;\n  margin: 24px;\n}\n\n.auth-img {\n  position: relative;\n}\n@media (min-width: 992px) and (max-width: 1549.98px) {\n  .auth-img {\n    max-width: 220%;\n  }\n}\n@media (min-width: 1550px) {\n  .auth-img {\n    max-width: 200%;\n  }\n}\n@media (max-width: 991.98px) {\n  .auth-img {\n    display: none;\n  }\n}\n\n.auth-pass-inputgroup input[type=text] + .btn .ri-eye-fill:before {\n  content: \"\\ec80\";\n}\n\n.signin-other-title {\n  position: relative;\n}\n.signin-other-title:after {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 1px;\n  left: 0;\n  right: 0;\n  background-color: var(--bs-border-color);\n  top: 10px;\n}\n.signin-other-title .title {\n  display: inline-block;\n  position: relative;\n  z-index: 9;\n  background-color: var(--bs-card-bg);\n  padding: 2px 16px;\n}", "// :root CSS variables\r\n\r\n:root{\r\n\r\n  // Vertical Sidebar - Default Light\r\n\r\n  --#{$variable-prefix}sidebar-bg: #2e2e2e;\r\n  --#{$variable-prefix}sidebar-menu-item-color: #878a92;\r\n  --#{$variable-prefix}sidebar-menu-item-active-color: #4eac6d;\r\n  --#{$variable-prefix}sidebar-menu-item-active-bg: #f7f7ff;\r\n  --#{$variable-prefix}sidebar-sub-bg: #ffffff;\r\n\r\n\r\n   // footer\r\n   --#{$variable-prefix}footer-bg: rgba(255,255,255,.05); \r\n\r\n  // Display\r\n  --#{$variable-prefix}display-none: none;\r\n  --#{$variable-prefix}display-block: block;\r\n\r\n  // Chat\r\n  --#{$variable-prefix}chat-text-color:     #495057;\r\n  --#{$variable-prefix}chat-primary-bg:     #ffffff;\r\n  \r\n  // component variable\r\n\r\n  // theme-color\r\n  --#{$variable-prefix}light: #{$gray-300};\r\n  --#{$variable-prefix}light-rgb: #{to-rgb($gray-300)};\r\n  --#{$variable-prefix}dark: #{$gray-900};\r\n  --#{$variable-prefix}dark-rgb: #{to-rgb($gray-900)};\r\n  --#{$variable-prefix}text-muted: #{$gray-600};\r\n\r\n  // link\r\n  --#{$variable-prefix}link-color: #{$primary};\r\n  --#{$variable-prefix}link-hover-color: #{$primary};\r\n\r\n  // Border variable\r\n  --#{$variable-prefix}border-color:  #eaeaf1;\r\n\r\n  // dropdown\r\n  --#{$variable-prefix}dropdown-bg: #{$white};\r\n  --#{$variable-prefix}dropdown-border-color : #f6f6f9;\r\n  --#{$variable-prefix}dropdown-link-color: #{$gray-900};\r\n  --#{$variable-prefix}dropdown-link-hover-color: #{shade-color($gray-900, 5%)};\r\n  --#{$variable-prefix}dropdown-link-hover-bg: #{$gray-100};\r\n  --#{$variable-prefix}dropdown-border-width: 1px;\r\n\r\n  // card\r\n  --#{$variable-prefix}card-bg: #{$white};\r\n  --#{$variable-prefix}card-cap-bg: #{$white};\r\n  --#{$variable-prefix}card-border-color: #eff0f2;\r\n  --#{$variable-prefix}card-logo-dark: block;\r\n  --#{$variable-prefix}card-logo-light: none;\r\n  --#{$variable-prefix}card-box-shadow:   0 2px 3px #e6e8eb;\r\n\r\n  // modal\r\n  --#{$variable-prefix}modal-bg: #{$white};\r\n  --#{$variable-prefix}modal-content-bg: #{$white};\r\n  --#{$variable-prefix}modal-content-border-color: #{$gray-300};\r\n\r\n\r\n  // nav tabs\r\n  --#{$variable-prefix}nav-tabs-link-active-color: #{$gray-700};\r\n  --#{$variable-prefix}nav-tabs-link-active-bg: #{$body-bg};\r\n\r\n  // accordion\r\n  --#{$variable-prefix}accordion-button-active-color: #{shade-color($primary, 10%)};\r\n  --#{$variable-prefix}accordion-bg: #{$white};\r\n  --#{$variable-prefix}accordion-button-bg: #{$gray-200};\r\n  --#{$variable-prefix}accordion-button-active-bg :rgba(246,246,249,.7);\r\n\r\n    // table\r\n    --#{$variable-prefix}table-color: #{$body-color};\r\n\r\n    // Boxed layout \r\n    --#{$variable-prefix}boxed-body-bg:       #e9ebf0;\r\n\r\n    // body\r\n    --#{$variable-prefix}body-heading-color:  #{$gray-700};\r\n    --#{$variable-prefix}body-bg: #f2f2f2;\r\n    --#{$variable-prefix}body-color: #{$gray-700};\r\n    --#{$variable-prefix}body-color-rgb: #{to-rgb($body-color)};\r\n\r\n  // progress\r\n  --#{$variable-prefix}progress-bg: #{$gray-200};\r\n\r\n  // toast\r\n  --#{$variable-prefix}toast-background-color: #{rgba($white, .85)};\r\n  --#{$variable-prefix}toast-border-color: #{rgba($black, .1)};\r\n  --#{$variable-prefix}toast-header-border-color: #{rgba($black, .05)};\r\n\r\n  //list\r\n  --#{$variable-prefix}list-group-hover-bg: #{$gray-100};\r\n\r\n  // popover\r\n  --#{$variable-prefix}popover-bg: #{$white};\r\n\r\n  // pagination\r\n  --#{$variable-prefix}pagination-hover-bg: #{$gray-200};\r\n\r\n  // form\r\n  --#{$variable-prefix}input-bg: #{$white};\r\n  --#{$variable-prefix}input-group-addon-border-color: #{$gray-400};\r\n  --#{$variable-prefix}input-border: #{$gray-400};\r\n  --#{$variable-prefix}input-border-color: #cfd4d8;\r\n  --#{$variable-prefix}input-focus-border: #{tint-color($component-active-bg, 50%)};\r\n  --#{$variable-prefix}input-disabled-bg: #{$gray-200};\r\n\r\n  // input\r\n  --#{$variable-prefix}input-placeholder-color: #{$gray-600};\r\n  --#{$variable-prefix}input-group-addon-bg: #{$gray-200};\r\n\r\n\r\n  // Breadcrumbs\r\n\r\n  //check\r\n  --#{$variable-prefix}input-check-border: var(--#{$variable-prefix}input-border);\r\n\r\n  \r\n}\r\n", "// :root CSS dark variables\r\n\r\n\r\n[data-layout-mode=\"dark\"]{\r\n\r\n  // Color system - Dark Mode only\r\n  $gray-100: #212529;\r\n  $gray-200: #262626;\r\n  $gray-300: #2e2e2e;\r\n  $gray-400: #757575;\r\n  $gray-500: #8f9198;\r\n  $gray-600: #adb5bd;\r\n  $gray-700: #adb5bd;\r\n  $gray-800: #f9f9f9;\r\n  $gray-900: #f8f9fa;\r\n\r\n  $grays: (\r\n      \"100\": $gray-100,\r\n      \"200\": $gray-200,\r\n      \"300\": $gray-300,\r\n      \"400\": $gray-400,\r\n      \"500\": $gray-500,\r\n      \"600\": $gray-600,\r\n      \"700\": $gray-700,\r\n      \"800\": $gray-800,\r\n      \"900\": $gray-900\r\n  );\r\n\r\n  // Prefix for :root CSS variables\r\n  @each $color, $value in $grays {\r\n      --#{$variable-prefix}gray-#{$color}: #{$value};\r\n  }\r\n\r\n  // Vertical Sidebar - Default Light\r\n\r\n  --#{$variable-prefix}sidebar-bg: #2e2e2e;\r\n  --#{$variable-prefix}sidebar-menu-item-color: #878a92;\r\n  --#{$variable-prefix}sidebar-menu-item-active-color: #4eac6d;\r\n  --#{$variable-prefix}sidebar-menu-item-active-bg: #f7f7ff;\r\n  --#{$variable-prefix}sidebar-sub-bg: #262626;\r\n\r\n  // footer\r\n  --#{$variable-prefix}footer-bg: rgba(46,46,46,.5); \r\n\r\n  // Display\r\n  --#{$variable-prefix}display-none: block;\r\n  --#{$variable-prefix}display-block: none;\r\n\r\n    // Chat\r\n    --#{$variable-prefix}chat-text-color:     rgba(255,255,255,.8);\r\n    --#{$variable-prefix}chat-primary-bg:     #383838;\r\n\r\n    // card-title-desc\r\n  --#{$variable-prefix}card-title-desc: #{$gray-400};\r\n\r\n\r\n  // Horizontal nav\r\n  --#{$variable-prefix}topnav-bg: #{lighten($gray-200, 2.5%)};\r\n  --#{$variable-prefix}topnav-item-color: #{$gray-400};\r\n  --#{$variable-prefix}topnav-item-color-active: #{$white};\r\n\r\n  // twocolumn menu\r\n  --#{$variable-prefix}twocolumn-menu-iconview-bg: #{$white};\r\n  --#{$variable-prefix}twocolumn-menu-bg:  #{$white};\r\n    \r\n  // two column dark\r\n  --#{$variable-prefix}twocolumn-menu-iconview-bg-dark: var(--#{$variable-prefix}vertical-menu-bg-dark);\r\n  --#{$variable-prefix}twocolumn-menu-bg-dark:  #30363a;\r\n  --#{$variable-prefix}twocolumn-menu-item-color-dark: var(--#{$variable-prefix}vertical-menu-item-color-dark);\r\n  --#{$variable-prefix}twocolumn-menu-item-active-color-dark:  #{$white};\r\n  --#{$variable-prefix}twocolumn-menu-item-active-bg-dark:  #{rgba($white, .15)};\r\n\r\n  // boxed \r\n  --#{$variable-prefix}boxed-body-bg: #{darken($gray-100, 4%)};\r\n\r\n  // heading-color\r\n  --#{$variable-prefix}heading-color: #{$gray-700};\r\n  \r\n  // component variable\r\n\r\n  --#{$variable-prefix}light: #{$gray-300};\r\n  --#{$variable-prefix}light-rgb: #{to-rgb($gray-300)};\r\n  --#{$variable-prefix}dark: #{$gray-800};\r\n  --#{$variable-prefix}dark-rgb: #{to-rgb($gray-800)};\r\n  --#{$variable-prefix}text-muted: #{$gray-500};\r\n\r\n  // link\r\n  --#{$variable-prefix}link-color: #{$gray-700};\r\n  --#{$variable-prefix}link-hover-color: #{$gray-700};\r\n\r\n  // Border variable\r\n  --#{$variable-prefix}border-color:  #333333;\r\n\r\n  // dropdown\r\n  --#{$variable-prefix}dropdown-bg: #333333;\r\n  --#{$variable-prefix}dropdown-border-color : #383838;\r\n  --#{$variable-prefix}dropdown-link-color: #{$gray-500};\r\n  --#{$variable-prefix}dropdown-link-hover-color:  #{$gray-500};\r\n  --#{$variable-prefix}dropdown-link-hover-bg: #383838;\r\n  --#{$variable-prefix}dropdown-border-width: 1px;\r\n\r\n  // card\r\n  --#{$variable-prefix}card-bg: #262626;\r\n  --#{$variable-prefix}card-cap-bg: #{$gray-200};\r\n  --#{$variable-prefix}card-border-color: #082a3e;\r\n  --#{$variable-prefix}card-logo-dark: none;\r\n  --#{$variable-prefix}card-logo-light: block;\r\n  --#{$variable-prefix}card-box-shadow:   0 2px 3px #04121a;\r\n\r\n  // modal\r\n  --#{$variable-prefix}modal-bg: #{$gray-200};\r\n  --#{$variable-prefix}modal-content-bg: #{$gray-200};\r\n  --#{$variable-prefix}modal-content-border-color: #{$gray-300};\r\n\r\n  // nav tabs\r\n  --#{$variable-prefix}nav-tabs-link-active-color: #{$gray-900};\r\n  --#{$variable-prefix}nav-tabs-link-active-bg: #{$gray-300};\r\n\r\n    // accordion\r\n  --#{$variable-prefix}accordion-button-active-color: #{$white};\r\n  --#{$variable-prefix}accordion-bg: #{transparent};\r\n  --#{$variable-prefix}accordion-button-bg: #{$gray-300};\r\n  --#{$variable-prefix}accordion-button-active-bg :#2e2e2e;\r\n\r\n  // table\r\n  --#{$variable-prefix}table-color: #{$gray-400};\r\n\r\n  // Boxed layout \r\n  --#{$variable-prefix}boxed-body-bg:       #333847;\r\n\r\n    // body\r\n    --#{$variable-prefix}body-heading-color:  #{$gray-600};\r\n    --#{$variable-prefix}body-bg : #2e2e2e;\r\n    --#{$variable-prefix}body-color: #{$gray-500};\r\n    --#{$variable-prefix}body-color-rgb: #{to-rgb($body-color)};\r\n\r\n  // progress\r\n  --#{$variable-prefix}progress-bg: #{$gray-300};\r\n\r\n  // toast\r\n  --#{$variable-prefix}toast-background-color: #{rgba($gray-300, .85)};\r\n  --#{$variable-prefix}toast-border-color: #{rgba($white, .1)};\r\n  --#{$variable-prefix}toast-header-border-color: #{rgba($white, .05)};\r\n\r\n  //list\r\n  --#{$variable-prefix}list-group-hover-bg: #{lighten($gray-200, 2.5%)};\r\n\r\n  // popover\r\n  --#{$variable-prefix}popover-bg: #{lighten($gray-200, 1%)};\r\n\r\n  // pagination\r\n  --#{$variable-prefix}pagination-hover-bg: #{lighten($gray-200, 4%)};\r\n\r\n  //form\r\n  --#{$variable-prefix}input-bg: #333333;\r\n  --#{$variable-prefix}input-group-addon-border-color: #{$gray-300};\r\n  --#{$variable-prefix}input-border: #{$gray-300};\r\n  --#{$variable-prefix}input-border-color: #333333;\r\n  --#{$variable-prefix}input-focus-border: #{lighten($gray-300, 4%)};\r\n  --#{$variable-prefix}input-disabled-bg: #{$gray-200};\r\n\r\n  // input-group-addon\r\n  --#{$variable-prefix}input-placeholder-color: #{$gray-500};\r\n  --#{$variable-prefix}input-group-addon-bg: #{$gray-300};\r\n\r\n  //check\r\n  --#{$variable-prefix}input-check-border:  #{lighten($gray-300, 4%)};\r\n\r\n}", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\n// avatar.scss\n//\n\n.avatar-xs {\n  height: 1.8rem;\n  width: 1.8rem;\n}\n\n.avatar-sm {\n  height: 2.4rem;\n  width: 2.4rem;\n}\n\n.avatar-md {\n  height: 4rem;\n  width: 4rem;\n}\n\n.avatar-lg {\n  height: 5rem;\n  width: 5rem;\n}\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: rgba(var(--bs-primary-rgb), 1);\n  color: $white;\n  display: flex;\n  font-weight: $font-weight-medium;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n// avatar group\n.avatar-group {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 8px;\n  .avatar-group-item {\n    margin-left: -8px;\n    border: 2px solid $card-bg;\n    border-radius: 50%;\n    transition: all 0.2s;\n    &:hover{\n      position: relative;\n      transform: translateY(-2px);\n    }\n  }\n}\n", "// Variables\n\n//\n// custom-variables\n//\n\n// Vertical Sidebar - Default Light\n\n$sidebar-bg: var(--#{$variable-prefix}sidebar-bg);\n$sidebar-menu-item-color: var(--#{$variable-prefix}sidebar-menu-item-color);\n$sidebar-menu-item-active-color: var(--#{$variable-prefix}sidebar-menu-item-active-color);\n$sidebar-menu-item-active-bg: var(--#{$variable-prefix}idebar-menu-item-active-bg);\n$sidebar-sub-bg:  var(--#{$variable-prefix}sidebar-sub-bg);\n\n\n// footer\n$footer-bg:  var(--#{$variable-prefix}footer-bg);\n\n// Display\n$display-none: none;\n$display-block: block;\n\n// Font Weight\n$font-weight-medium: 500;\n$font-weight-semibold: 600;\n\n// Chat\n$chat-text-color:    var(--#{$variable-prefix}chat-text-color);\n$chat-primary-bg:     var(--#{$variable-prefix}chat-primary-bg);\n$chat-secondary-bg:    rgba(var(--bs-primary-rgb), 0.23);\n\n\n$btn-close-bg-dark:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$white}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\");\n\n// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n// scss-docs-start gray-color-variables\n$white:    #fff;\n$gray-100: #f8f9fa;\n$gray-200: #f9f9f9;\n$gray-300: #f6f6f9;\n$gray-400: #e6ebf5;\n$gray-500: #adb5bd;\n$gray-600: #797c8c;\n$gray-700: #495057;\n$gray-800: #343a40;\n$gray-900: #212529;\n$black:    #000;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n);\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #4eac6d;\n$indigo:  #560BAD;\n$purple:  #6153CC;\n$pink:    #e83e8c;\n$red:     #EF476F;\n$orange:  #FC814A;\n$yellow:  #FFD166;\n$green:   #06D6A0;\n$teal:    #050505;\n$cyan:    #50a5f1;\n// scss-docs-end color-variables\n\n\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n);\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary:       $blue;\n$secondary:     $gray-600;\n$success:       $green;\n$info:          $cyan;\n$warning:       $yellow;\n$danger:        $red;\n$light:         $gray-300;\n$dark:          $gray-800;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"light\":      $light,\n  \"indigo\":     $indigo,\n  \"dark\":       $dark\n);\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-colors-rgb\n$theme-colors-rgb: map-loop($theme-colors, to-rgb, \"$value\");\n// scss-docs-end theme-colors-rgb\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   1.8;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $gray-900;\n$color-contrast-light:     $white;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%);\n$blue-200: tint-color($blue, 60%);\n$blue-300: tint-color($blue, 40%);\n$blue-400: tint-color($blue, 20%);\n$blue-500: $blue;\n$blue-600: shade-color($blue, 20%);\n$blue-700: shade-color($blue, 40%);\n$blue-800: shade-color($blue, 60%);\n$blue-900: shade-color($blue, 80%);\n\n$indigo-100: tint-color($indigo, 80%);\n$indigo-200: tint-color($indigo, 60%);\n$indigo-300: tint-color($indigo, 40%);\n$indigo-400: tint-color($indigo, 20%);\n$indigo-500: $indigo;\n$indigo-600: shade-color($indigo, 20%);\n$indigo-700: shade-color($indigo, 40%);\n$indigo-800: shade-color($indigo, 60%);\n$indigo-900: shade-color($indigo, 80%);\n\n$purple-100: tint-color($purple, 80%);\n$purple-200: tint-color($purple, 60%);\n$purple-300: tint-color($purple, 40%);\n$purple-400: tint-color($purple, 20%);\n$purple-500: $purple;\n$purple-600: shade-color($purple, 20%);\n$purple-700: shade-color($purple, 40%);\n$purple-800: shade-color($purple, 60%);\n$purple-900: shade-color($purple, 80%);\n\n$pink-100: tint-color($pink, 80%);\n$pink-200: tint-color($pink, 60%);\n$pink-300: tint-color($pink, 40%);\n$pink-400: tint-color($pink, 20%);\n$pink-500: $pink;\n$pink-600: shade-color($pink, 20%);\n$pink-700: shade-color($pink, 40%);\n$pink-800: shade-color($pink, 60%);\n$pink-900: shade-color($pink, 80%);\n\n$red-100: tint-color($red, 80%);\n$red-200: tint-color($red, 60%);\n$red-300: tint-color($red, 40%);\n$red-400: tint-color($red, 20%);\n$red-500: $red;\n$red-600: shade-color($red, 20%);\n$red-700: shade-color($red, 40%);\n$red-800: shade-color($red, 60%);\n$red-900: shade-color($red, 80%);\n\n$orange-100: tint-color($orange, 80%);\n$orange-200: tint-color($orange, 60%);\n$orange-300: tint-color($orange, 40%);\n$orange-400: tint-color($orange, 20%);\n$orange-500: $orange;\n$orange-600: shade-color($orange, 20%);\n$orange-700: shade-color($orange, 40%);\n$orange-800: shade-color($orange, 60%);\n$orange-900: shade-color($orange, 80%);\n\n$yellow-100: tint-color($yellow, 80%);\n$yellow-200: tint-color($yellow, 60%);\n$yellow-300: tint-color($yellow, 40%);\n$yellow-400: tint-color($yellow, 20%);\n$yellow-500: $yellow;\n$yellow-600: shade-color($yellow, 20%);\n$yellow-700: shade-color($yellow, 40%);\n$yellow-800: shade-color($yellow, 60%);\n$yellow-900: shade-color($yellow, 80%);\n\n$green-100: tint-color($green, 80%);\n$green-200: tint-color($green, 60%);\n$green-300: tint-color($green, 40%);\n$green-400: tint-color($green, 20%);\n$green-500: $green;\n$green-600: shade-color($green, 20%);\n$green-700: shade-color($green, 40%);\n$green-800: shade-color($green, 60%);\n$green-900: shade-color($green, 80%);\n\n$teal-100: tint-color($teal, 80%);\n$teal-200: tint-color($teal, 60%);\n$teal-300: tint-color($teal, 40%);\n$teal-400: tint-color($teal, 20%);\n$teal-500: $teal;\n$teal-600: shade-color($teal, 20%);\n$teal-700: shade-color($teal, 40%);\n$teal-800: shade-color($teal, 60%);\n$teal-900: shade-color($teal, 80%);\n\n$cyan-100: tint-color($cyan, 80%);\n$cyan-200: tint-color($cyan, 60%);\n$cyan-300: tint-color($cyan, 40%);\n$cyan-400: tint-color($cyan, 20%);\n$cyan-500: $cyan;\n$cyan-600: shade-color($cyan, 20%);\n$cyan-700: shade-color($cyan, 40%);\n$cyan-800: shade-color($cyan, 60%);\n$cyan-900: shade-color($cyan, 80%);\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n);\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n);\n\n$purples: (\n  \"purple-100\": $purple-200,\n  \"purple-200\": $purple-100,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n);\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n);\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n);\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n);\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n);\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n);\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n);\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n);\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n);\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                false;\n$enable-rounded:              true;\n$enable-shadows:              false;\n$enable-gradients:            false;\n$enable-transitions:          true;\n$enable-reduced-motion:       true;\n$enable-smooth-scroll:        true;\n$enable-grid-classes:         true;\n$enable-cssgrid:              false;\n$enable-button-pointers:      true;\n$enable-rfs:                  true;\n$enable-validation-icons:     true;\n$enable-negative-margins:     true;\n$enable-deprecation-messages: true;\n$enable-important-utilities:  true;\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs-;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0));\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem;\n$spacers: (\n  0: 0,\n  1: $spacer * 0.25,\n  2: $spacer * 0.5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n);\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null);\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n);\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-heading-color:        var(--#{$variable-prefix}body-heading-color);\n$body-bg:                   #fafafa;\n$body-color:                $gray-700;\n$body-text-align:           null;\n\n// Utilities maps\n//\n// Extends the default `$theme-colors` maps to help create our utilities.\n\n// Come v6, we'll de-dupe these variables. Until then, for backward compatibility, we keep them to reassign.\n// scss-docs-start utilities-colors\n$utilities-colors: $theme-colors-rgb;\n// scss-docs-end utilities-colors\n\n// scss-docs-start utilities-text-colors\n$utilities-text: map-merge(\n  $utilities-colors,\n  (\n    \"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\":  to-rgb($body-color)\n  )\n);\n$utilities-text-colors: map-loop($utilities-text, rgba-css-var, \"$key\", \"text\");\n// scss-docs-end utilities-text-colors\n\n// scss-docs-start utilities-bg-colors\n$utilities-bg: map-merge(\n  $utilities-colors,\n  (\n    \"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-bg)\n  )\n);\n$utilities-bg-colors: map-loop($utilities-bg, rgba-css-var, \"$key\", \"bg\");\n// scss-docs-end utilities-bg-colors\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              rgba(var(--bs-primary-rgb), 1);\n$link-decoration:                         none;\n$link-shade-percentage:                   15%;\n$link-hover-color:                        rgba(var(--bs-primary-rgb), 0.9);\n$link-hover-decoration:                   underline;\n\n$stretched-link-pseudo-element:           after;\n$stretched-link-z-index:                  1;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n);\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n);\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 ;\n$grid-gutter-width:           20px;\n$grid-row-columns:            6;\n\n$gutters: $spacers;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width * 0.5;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n);\n\n$border-color:                var(--#{$variable-prefix}border-color);\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .25rem;\n$border-radius-sm:            .2rem;\n$border-radius-lg:            .4rem;\n$border-radius-pill:          50rem;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 2px 4px rgba(15,34,58,.12);\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075);\n$box-shadow-lg:                0 1rem 3rem rgba($black, .175);\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075);\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white;\n$component-active-bg:         $primary;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em;\n$caret-vertical-align:        $caret-width * .85;\n$caret-spacing:               $caret-width * .85;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out;\n$transition-fade:             opacity .15s linear;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease;\n$transition-collapse-width:   width .35s ease;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 * 0.25 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 * 0.51 * 100%)\n);\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Cerebri Sans,sans-serif\";\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif);\n$font-family-code:            var(--#{$variable-prefix}font-monospace);\n\n// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base effects the font size of the body text\n$font-size-root:              null;\n$font-size-base:              0.9375rem; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875;\n$font-size-lg:                $font-size-base * 1.25;\n\n$font-weight-lighter:         lighter;\n$font-weight-light:           300;\n$font-weight-normal:          400;\n$font-weight-bold:            700;\n$font-weight-bolder:          bolder;\n\n$font-weight-base:            $font-weight-normal;\n\n$line-height-base:            1.5;\n$line-height-sm:              1.25;\n$line-height-lg:              2;\n\n$h1-font-size:                $font-size-base * 2.5;\n$h2-font-size:                $font-size-base * 2;\n$h3-font-size:                $font-size-base * 1.75;\n$h4-font-size:                $font-size-base * 1.4;\n$h5-font-size:                $font-size-base * 1.25;\n$h6-font-size:                $font-size-base;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n);\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * 0.5;\n$headings-font-family:        null;\n$headings-font-style:         null;\n$headings-font-weight:        500;\n$headings-line-height:        1.2;\n$headings-color:              null;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n);\n\n$display-font-weight: 300;\n$display-line-height: $headings-line-height;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25;\n$lead-font-weight:            300;\n\n$small-font-size:             .875em;\n\n$sub-sup-font-size:           .75em;\n\n$text-muted:                   var(--#{$variable-prefix}text-muted);\n\n$initialism-font-size:        $small-font-size;\n\n$blockquote-margin-y:         $spacer;\n$blockquote-font-size:        $font-size-base * 1.25;\n$blockquote-footer-color:     $gray-600;\n$blockquote-footer-font-size: $small-font-size;\n\n$hr-margin-y:                 $spacer;\n$hr-color:                    $gray-500;\n$hr-height:                   $border-width;\n$hr-opacity:                  .25;\n\n$legend-margin-bottom:        .5rem;\n$legend-font-size:            1.5rem;\n$legend-font-weight:          null;\n\n$mark-padding:                .2em;\n\n$dt-font-weight:              $font-weight-bold;\n\n$nested-kbd-font-weight:      $font-weight-bold;\n\n$list-inline-padding:         .5rem;\n\n$mark-bg:                     #fcf8e3;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem;\n$table-cell-padding-x:        .5rem;\n$table-cell-padding-y-sm:     .25rem;\n$table-cell-padding-x-sm:     .25rem;\n\n$table-cell-vertical-align:   top;\n\n$table-color:                 $body-color;\n$table-bg:                    null;\n\n$table-th-font-weight:        null;\n\n$table-striped-color:         $table-color;\n$table-striped-bg-factor:     .05;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor);\n\n$table-active-color:          $table-color;\n$table-active-bg-factor:      .1;\n$table-active-bg:             $table-hover-bg;\n\n$table-hover-color:           $table-color;\n$table-hover-bg-factor:       .075;\n$table-hover-bg:              $gray-100;\n\n$table-border-factor:         .1;\n$table-border-width:          $border-width;\n$table-border-color:          $border-color;\n\n$table-striped-order:         odd;\n\n$table-group-separator-color: currentColor;\n\n$table-caption-color:         $text-muted;\n\n$table-bg-scale:              -80%;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n);\n// scss-docs-end table-variables\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .5rem;\n$input-btn-padding-x:         1rem;\n$input-btn-font-family:       null;\n$input-btn-font-size:         $font-size-base;\n$input-btn-line-height:       $line-height-base;\n\n$input-btn-focus-width:         0;\n$input-btn-focus-color-opacity: .25;\n$input-btn-focus-color:         rgba($component-active-bg, .25);\n$input-btn-focus-blur:          0;\n$input-btn-focus-box-shadow:    0 0 0 $input-btn-focus-width $input-btn-focus-color;\n\n$input-btn-padding-y-sm:      .25rem;\n$input-btn-padding-x-sm:      .5rem;\n$input-btn-font-size-sm:      $font-size-sm;\n\n$input-btn-padding-y-lg:      .5rem;\n$input-btn-padding-x-lg:      1rem;\n$input-btn-font-size-lg:      $font-size-lg;\n\n$input-btn-border-width:      $border-width;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y;\n$btn-padding-x:               $input-btn-padding-x;\n$btn-font-family:             $input-btn-font-family;\n$btn-font-size:               $input-btn-font-size;\n$btn-line-height:             $input-btn-line-height;\n$btn-white-space:             null; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm;\n$btn-padding-x-sm:            $input-btn-padding-x-sm;\n$btn-font-size-sm:            $input-btn-font-size-sm;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg;\n$btn-padding-x-lg:            $input-btn-padding-x-lg;\n$btn-font-size-lg:            $input-btn-font-size-lg;\n\n$btn-border-width:            $input-btn-border-width;\n\n$btn-font-weight:             $font-weight-normal;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075);\n$btn-focus-width:             $input-btn-focus-width;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow;\n$btn-disabled-opacity:        .65;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125);\n\n$btn-link-color:              $link-color;\n$btn-link-hover-color:        $link-hover-color;\n$btn-link-disabled-color:     $gray-600;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius;\n$btn-border-radius-sm:        $border-radius-sm;\n$btn-border-radius-lg:        $border-radius-lg;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$btn-hover-bg-shade-amount:       15%;\n$btn-hover-bg-tint-amount:        15%;\n$btn-hover-border-shade-amount:   20%;\n$btn-hover-border-tint-amount:    10%;\n$btn-active-bg-shade-amount:      20%;\n$btn-active-bg-tint-amount:       20%;\n$btn-active-border-shade-amount:  25%;\n$btn-active-border-tint-amount:   10%;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem;\n$form-text-font-size:                   $small-font-size;\n$form-text-font-style:                  null;\n$form-text-font-weight:                 null;\n$form-text-color:                       $text-muted;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem;\n$form-label-font-size:                  null;\n$form-label-font-style:                 null;\n$form-label-font-weight:                null;\n$form-label-color:                      null;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y;\n$input-padding-x:                       $input-btn-padding-x;\n$input-font-family:                     $input-btn-font-family;\n$input-font-size:                       0.875rem;\n$input-font-weight:                     $font-weight-base;\n$input-line-height:                     $input-btn-line-height;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm;\n$input-padding-x-sm:                    $input-btn-padding-x-sm;\n$input-font-size-sm:                    $input-btn-font-size-sm;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg;\n$input-padding-x-lg:                    $input-btn-padding-x-lg;\n$input-font-size-lg:                    14px;\n\n$input-bg:                              var(--#{$variable-prefix}input-bg);\n$input-disabled-bg:                     var(--#{$variable-prefix}input-disabled-bg);\n$input-disabled-border-color:           null;\n\n$input-color:                           var(--#{$variable-prefix}body-color);\n$input-border-color:                    var(--#{$variable-prefix}input-border-color);\n$input-border-width:                    $input-btn-border-width;\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075);\n\n$input-border-radius:                   $border-radius;\n$input-border-radius-sm:                $border-radius-sm;\n$input-border-radius-lg:                $border-radius-lg;\n\n$input-focus-bg:                        $input-bg;\n$input-focus-border-color:              var(--#{$variable-prefix}input-border-color);\n$input-focus-color:                     $input-color;\n$input-focus-width:                     $input-btn-focus-width;\n$input-focus-box-shadow:                none;\n\n$input-placeholder-color:               var(--#{$variable-prefix}input-placeholder-color);\n$input-plaintext-color:                 var(--#{$variable-prefix}body-color);\n\n$input-height-border:                   $input-border-width * 2;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2);\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y);\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * 0.5);\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border));\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border));\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border * 3));\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$form-color-width:                      3rem;\n// scss-docs-end form-input-variablesaccordion-button-active-bg\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em;\n$form-check-min-height:                   $font-size-base * $line-height-base;\n$form-check-padding-start:                $form-check-input-width + .5em;\n$form-check-margin-bottom:                .125rem;\n$form-check-label-color:                  null;\n$form-check-label-cursor:                 null;\n$form-check-transition:                   background-color .15s ease-in-out, background-position .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$form-check-input-active-filter:          brightness(90%);\n\n$form-check-input-bg:                     $input-bg;\n$form-check-input-border:                 1px solid var(--#{$variable-prefix}input-check-border);\n$form-check-input-border-radius:          .25em;\n$form-check-radio-border-radius:          50%;\n$form-check-input-focus-border:           $input-focus-border-color;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow;\n\n$form-check-input-checked-color:          $component-active-color;\n$form-check-input-checked-bg-color:       rgba(var(--#{$variable-prefix}primary-rgb),1);\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\");\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\");\n\n$form-check-input-indeterminate-color:          $component-active-color;\n$form-check-input-indeterminate-bg-color:       $component-active-bg;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\");\n\n$form-check-input-disabled-opacity:        .5;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity;\n\n$form-check-inline-margin-end:    1rem;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba(0, 0, 0, .25);\n$form-switch-width:               2em;\n$form-switch-padding-start:       $form-switch-width + .5em;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\");\n$form-switch-border-radius:       $form-switch-width;\n$form-switch-transition:          background-position .15s ease-in-out;\n\n$form-switch-focus-color:         $input-focus-border-color;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\");\n\n$form-switch-checked-color:       $component-active-color;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\");\n$form-switch-checked-bg-position: right center;\n\n$form-check-inline-margin-end:    1rem;\n// scss-docs-end form-switch-variables\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y;\n$input-group-addon-padding-x:           $input-padding-x;\n$input-group-addon-font-weight:         $input-font-weight;\n$input-group-addon-color:               $input-color;\n$input-group-addon-bg:                  $gray-200;\n$input-group-addon-border-color:        $input-border-color;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y;\n$form-select-padding-x:             $input-padding-x;\n$form-select-font-family:           $input-font-family;\n$form-select-font-size:             $input-font-size;\n$form-select-indicator-padding:     1.75rem; // Extra padding to account for the presence of the background-image based indicator\n$form-select-font-weight:           $input-font-weight;\n$form-select-line-height:           $input-line-height;\n$form-select-color:                 $input-color;\n$form-select-disabled-color:        $gray-600;\n$form-select-bg:                    $input-bg;\n$form-select-disabled-bg:           $gray-200;\n$form-select-disabled-border-color: $input-disabled-border-color;\n$form-select-bg-position:           right $form-select-padding-x center;\n$form-select-bg-size:               8px 10px; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\");\n\n$form-select-feedback-icon-padding-end: add(1em * .75, (2 * $form-select-padding-y * .75) + $form-select-padding-x + $form-select-indicator-padding);\n$form-select-feedback-icon-position:    center right ($form-select-padding-x + $form-select-indicator-padding);\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half;\n\n$form-select-border-width:        $input-border-width;\n$form-select-border-color:        $input-border-color;\n$form-select-border-radius:       $border-radius;\n$form-select-box-shadow:          inset 0 1px 2px rgba($black, .075);\n\n$form-select-focus-border-color:  $input-focus-border-color;\n$form-select-focus-width:         $input-focus-width;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color;\n\n$form-select-padding-y-sm:        $input-padding-y-sm;\n$form-select-padding-x-sm:        $input-padding-x-sm;\n$form-select-font-size-sm:        $input-font-size-sm;\n$form-select-border-radius-sm:    $input-border-radius-sm;\n\n$form-select-padding-y-lg:        $input-padding-y-lg;\n$form-select-padding-x-lg:        $input-padding-x-lg;\n$form-select-font-size-lg:        $input-font-size-lg;\n$form-select-border-radius-lg:    $input-border-radius-lg;\n\n$form-select-transition:          $input-transition;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100%;\n$form-range-track-height:         .5rem;\n$form-range-track-cursor:         pointer;\n$form-range-track-bg:             $gray-300;\n$form-range-track-border-radius:  1rem;\n$form-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1);\n\n$form-range-thumb-width:                   1rem;\n$form-range-thumb-height:                  $form-range-thumb-width;\n$form-range-thumb-bg:                      $component-active-bg;\n$form-range-thumb-border:                  0;\n$form-range-thumb-border-radius:           1rem;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1);\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%);\n$form-range-thumb-disabled-bg:             $gray-500;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color;\n$form-file-button-bg:             $input-group-addon-bg;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%);\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:            add(3.5rem, $input-height-border);\n$form-floating-line-height:       1.25;\n$form-floating-padding-x:         $input-padding-x;\n$form-floating-padding-y:         1rem;\n$form-floating-input-padding-t:   1.625rem;\n$form-floating-input-padding-b:   .625rem;\n$form-floating-label-opacity:     .65;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem);\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top;\n$form-feedback-font-size:           $form-text-font-size;\n$form-feedback-font-style:          $form-text-font-style;\n$form-feedback-valid-color:         $success;\n$form-feedback-invalid-color:       $danger;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\");\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\");\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n);\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000;\n$zindex-sticky:                     1020;\n$zindex-fixed:                      1030;\n$zindex-modal-backdrop:             1040;\n$zindex-offcanvas:                  1050;\n$zindex-modal-backdrop:             1050;\n$zindex-modal:                      1060;\n$zindex-popover:                    1070;\n$zindex-tooltip:                    1080;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem;\n$nav-link-padding-x:                1rem;\n$nav-link-font-size:                null;\n$nav-link-font-weight:              null;\n$nav-link-color:                    null;\n$nav-link-hover-color:              null;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out;\n$nav-link-disabled-color:           $gray-600;\n\n$nav-tabs-border-color:             $gray-400;\n$nav-tabs-border-width:             $border-width;\n$nav-tabs-border-radius:            $border-radius;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color;\n$nav-tabs-link-active-color:        $gray-700;\n$nav-tabs-link-active-bg:           $body-bg;\n$nav-tabs-link-active-border-color: $gray-400 $gray-400 $nav-tabs-link-active-bg;\n\n$nav-pills-border-radius:           $border-radius;\n$nav-pills-link-active-color:       $component-active-color;\n$nav-pills-link-active-bg:          $component-active-bg;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * 0.5;\n$navbar-padding-x:                  $spacer;\n\n$navbar-nav-link-padding-x:         .5rem;\n\n$navbar-brand-font-size:            $font-size-lg;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * 0.5;\n$navbar-brand-margin-end:           1rem;\n\n$navbar-toggler-padding-y:          .25rem;\n$navbar-toggler-padding-x:          .75rem;\n$navbar-toggler-font-size:          $font-size-lg;\n$navbar-toggler-border-radius:      $btn-border-radius;\n$navbar-toggler-focus-width:        $btn-focus-width;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color:                 rgba($white, .55);\n$navbar-dark-hover-color:           rgba($white, .75);\n$navbar-dark-active-color:          $white;\n$navbar-dark-disabled-color:        rgba($white, .25);\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-dark-toggler-border-color:  rgba($white, .1);\n\n$navbar-light-color:                rgba($black, .55);\n$navbar-light-hover-color:          rgba($black, .7);\n$navbar-light-active-color:         rgba($black, .9);\n$navbar-light-disabled-color:       rgba($black, .3);\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-light-toggler-border-color: rgba($black, .1);\n\n$navbar-light-brand-color:                $navbar-light-active-color;\n$navbar-light-brand-hover-color:          $navbar-light-active-color;\n$navbar-dark-brand-color:                 $navbar-dark-active-color;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem;\n$dropdown-padding-x:                0;\n$dropdown-padding-y:                .5rem;\n$dropdown-spacer:                   .125rem;\n$dropdown-font-size:                $font-size-base;\n$dropdown-color:                    $body-color;\n$dropdown-bg:                       var(--#{$variable-prefix}dropdown-bg);\n$dropdown-border-color:             var(--#{$variable-prefix}dropdown-border-color);\n$dropdown-border-radius:            $border-radius;\n$dropdown-border-width:             var(--#{$variable-prefix}dropdown-border-width);\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width);\n$dropdown-divider-bg:               $dropdown-border-color;\n$dropdown-divider-margin-y:         $spacer * 0.5;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175);\n\n$dropdown-link-color:               var(--#{$variable-prefix}dropdown-link-color);\n$dropdown-link-hover-color:         var(--#{$variable-prefix}dropdown-link-hover-color);\n$dropdown-link-hover-bg:            var(--#{$variable-prefix}dropdown-link-hover-bg);\n\n$dropdown-link-active-color:        var(--#{$variable-prefix}dropdown-link-hover-color);\n$dropdown-link-active-bg:           var(--#{$variable-prefix}dropdown-link-hover-bg);\n\n$dropdown-link-disabled-color:      $gray-600;\n\n$dropdown-item-padding-y:           .35rem;\n$dropdown-item-padding-x:           1.5rem;\n\n$dropdown-header-color:             $gray-600;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300;\n$dropdown-dark-bg:                  $gray-800;\n$dropdown-dark-border-color:        $dropdown-border-color;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg;\n$dropdown-dark-box-shadow:          null;\n$dropdown-dark-link-color:          $dropdown-dark-color;\n$dropdown-dark-link-hover-color:    $white;\n$dropdown-dark-link-hover-bg:       rgba($white, .15);\n$dropdown-dark-link-active-color:   $dropdown-link-active-color;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg;\n$dropdown-dark-link-disabled-color: $gray-500;\n$dropdown-dark-header-color:        $gray-500;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem;\n$pagination-padding-x:              .75rem;\n$pagination-padding-y-sm:           .25rem;\n$pagination-padding-x-sm:           .5rem;\n$pagination-padding-y-lg:           .75rem;\n$pagination-padding-x-lg:           1.5rem;\n\n$pagination-color:                  $link-color;\n$pagination-bg:                     $white;\n$pagination-border-width:           $border-width;\n$pagination-border-radius:          $border-radius;\n$pagination-margin-start:           -$pagination-border-width;\n$pagination-border-color:           $gray-300;\n\n$pagination-focus-color:            $link-hover-color;\n$pagination-focus-bg:               $gray-200;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow;\n$pagination-focus-outline:          0;\n\n$pagination-hover-color:            $link-hover-color;\n$pagination-hover-bg:               $gray-200;\n$pagination-hover-border-color:     $gray-300;\n\n$pagination-active-color:           $component-active-color;\n$pagination-active-bg:              $component-active-bg;\n$pagination-active-border-color:    $pagination-active-bg;\n\n$pagination-disabled-color:         $gray-600;\n$pagination-disabled-bg:            $white;\n$pagination-disabled-border-color:  $gray-300;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$pagination-border-radius-sm:       $border-radius-sm;\n$pagination-border-radius-lg:       $border-radius-lg;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5;\n$placeholder-opacity-min:           .2;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     1.25rem;\n$card-spacer-x:                     1.25rem;\n$card-title-spacer-y:               $spacer * 0.5;\n$card-border-width:                 0;\n$card-border-radius:                $border-radius;\n$card-border-color:                 var(--#{$variable-prefix}card-border-color);\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width);\n$card-cap-padding-y:                .75rem;\n$card-cap-padding-x:                1.25rem;\n$card-cap-bg:                       var(--#{$variable-prefix}card-cap-bg);\n$card-cap-color:                    null;\n$card-height:                       null;\n$card-color:                        null;\n$card-bg:                           var(--#{$variable-prefix}card-bg);\n\n$card-img-overlay-padding:          $spacer;\n\n$card-group-margin:                 $grid-gutter-width * 0.5;\n// scss-docs-end card-variables\n\n// Accordion\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1.25rem;\n$accordion-padding-x:                     1.25rem;\n$accordion-color:                         var(--#{$variable-prefix}body-color);                                                           \n$accordion-bg:                            var(--#{$variable-prefix}card-bg);\n$accordion-border-width:                  1px;\n$accordion-border-color:                  var(--#{$variable-prefix}border-color);\n$accordion-border-radius:                 $border-radius;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width);\n\n$accordion-body-padding-y:                $accordion-padding-y;\n$accordion-body-padding-x:                $accordion-padding-x;\n\n$accordion-button-padding-y:              .75rem;\n$accordion-button-padding-x:              $accordion-padding-x;\n$accordion-button-color:                  $accordion-color;\n$accordion-button-bg:                     $accordion-bg;\n$accordion-transition:                    $btn-transition, border-radius .15s ease;\n$accordion-button-active-bg:              var(--#{$variable-prefix}accordion-button-active-bg);\n$accordion-button-active-color:           $accordion-color;\n\n$accordion-button-focus-border-color:     $accordion-border-color;\n$accordion-button-focus-box-shadow:       none;\n\n$accordion-icon-width:                    .75rem;\n$accordion-icon-color:                    $accordion-color;\n$accordion-icon-active-color:             $accordion-button-active-color;\n$accordion-icon-transition:               transform .2s ease-in-out;\n$accordion-icon-transform:                rotate(180deg);\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm;\n$tooltip-max-width:                 200px;\n$tooltip-color:                     $white;\n$tooltip-bg:                        $black;\n$tooltip-border-radius:             $border-radius;\n$tooltip-opacity:                   .9;\n$tooltip-padding-y:                 $spacer * 0.25;\n$tooltip-padding-x:                 $spacer * 0.5;\n$tooltip-margin:                    0;\n\n$tooltip-arrow-width:               .8rem;\n$tooltip-arrow-height:              .4rem;\n$tooltip-arrow-color:               $tooltip-bg;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x;\n$form-feedback-tooltip-font-size:     $tooltip-font-size;\n$form-feedback-tooltip-line-height:   null;\n$form-feedback-tooltip-opacity:       $tooltip-opacity;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius;\n// scss-docs-start tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm;\n$popover-bg:                        $white;\n$popover-max-width:                 276px;\n$popover-border-width:              $border-width;\n$popover-border-color:              rgba($black, .1);\n$popover-border-radius:             $border-radius-lg;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width);\n$popover-box-shadow:                $box-shadow;\n\n$popover-header-bg:                 shade-color($popover-bg, 6%);\n$popover-header-color:              $headings-color;\n$popover-header-padding-y:          .5rem;\n$popover-header-padding-x:          $spacer;\n\n$popover-body-color:                $body-color;\n$popover-body-padding-y:            $spacer;\n$popover-body-padding-x:            $spacer;\n\n$popover-arrow-width:               1rem;\n$popover-arrow-height:              .5rem;\n$popover-arrow-color:               $popover-bg;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05);\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px;\n$toast-padding-x:                   .75rem;\n$toast-padding-y:                   .5rem;\n$toast-font-size:                   .875rem;\n$toast-color:                       null;\n$toast-background-color:            rgba($white, .85);\n$toast-border-width:                1px;\n$toast-border-color:                rgba(0, 0, 0, .1);\n$toast-border-radius:               $border-radius;\n$toast-box-shadow:                  $box-shadow;\n$toast-spacing:                     $container-padding-x;\n\n$toast-header-color:                $gray-600;\n$toast-header-background-color:     rgba($white, .85);\n$toast-header-border-color:         rgba(0, 0, 0, .05);\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em;\n$badge-font-weight:                 $font-weight-medium;\n$badge-color:                       $white;\n$badge-padding-y:                   .25em;\n$badge-padding-x:                   .6em;\n$badge-border-radius:               $border-radius;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem;\n\n$modal-dialog-margin:               .5rem;\n$modal-dialog-margin-y-sm-up:       1.75rem;\n\n$modal-title-line-height:           $line-height-base;\n\n$modal-content-color:               null;\n$modal-content-bg:                  var(--#{$variable-prefix}modal-bg);\n$modal-content-border-color:        $border-color;\n$modal-content-border-width:        $border-width;\n$modal-content-border-radius:       $border-radius-lg;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width);\n$modal-content-box-shadow-xs:       $box-shadow-sm;\n$modal-content-box-shadow-sm-up:    $box-shadow;\n\n$modal-backdrop-bg:                 $black;\n$modal-backdrop-opacity:            .5;\n$modal-header-border-color:         $border-color;\n$modal-footer-border-color:         $modal-header-border-color;\n$modal-header-border-width:         $modal-content-border-width;\n$modal-footer-border-width:         $modal-header-border-width;\n$modal-header-padding-y:            $modal-inner-padding;\n$modal-header-padding-x:            $modal-inner-padding;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x; // Keep this for backwards compatibility\n\n$modal-sm:                          300px;\n$modal-md:                          500px;\n$modal-lg:                          800px;\n$modal-xl:                          1140px;\n\n$modal-fade-transform:              translate(0, -50px);\n$modal-show-transform:              none;\n$modal-transition:                  transform .3s ease-out;\n$modal-scale-transform:             scale(1.02);\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:                   .75rem;\n$alert-padding-x:                   1.25rem;\n$alert-margin-bottom:               1rem;\n$alert-border-radius:               $border-radius;\n$alert-link-font-weight:            $font-weight-bold;\n$alert-border-width:                $border-width;\n\n$alert-bg-scale:                    -80%;\n$alert-border-scale:                -70%;\n$alert-color-scale:                 40%;\n\n$alert-dismissible-padding-r:       $alert-padding-x * 3; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem;\n$progress-font-size:                $font-size-base * .75;\n$progress-bg:                       $gray-200;\n$progress-border-radius:            $border-radius;\n$progress-box-shadow:               $box-shadow-inset;\n$progress-bar-color:                $white;\n$progress-bar-bg:                   $primary;\n$progress-bar-animation-timing:     1s linear infinite;\n$progress-bar-transition:           width .6s ease;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  null;\n$list-group-bg:                     $white;\n$list-group-border-color:           $gray-400;\n$list-group-border-width:           $border-width;\n$list-group-border-radius:          $border-radius;\n\n$list-group-item-padding-y:         $spacer * 0.5;\n$list-group-item-padding-x:         $spacer;\n$list-group-item-bg-scale:          -80%;\n$list-group-item-color-scale:       40%;\n\n$list-group-hover-bg:               $gray-100;\n$list-group-active-color:           $component-active-color;\n$list-group-active-bg:              $component-active-bg;\n$list-group-active-border-color:    $list-group-active-bg;\n\n$list-group-disabled-color:         $gray-600;\n$list-group-disabled-bg:            $list-group-bg;\n\n$list-group-action-color:           $gray-700;\n$list-group-action-hover-color:     $list-group-action-color;\n\n$list-group-action-active-color:    $body-color;\n$list-group-action-active-bg:       $gray-200;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem;\n$thumbnail-bg:                      var(--#{$variable-prefix}body-bg);\n$thumbnail-border-width:            $border-width;\n$thumbnail-border-color:            var(--#{$variable-prefix}border-color);\n$thumbnail-border-radius:           $border-radius;\n$thumbnail-box-shadow:              $box-shadow-sm;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size;\n$figure-caption-color:              $gray-600;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null;\n$breadcrumb-padding-y:              0;\n$breadcrumb-padding-x:              0;\n$breadcrumb-item-padding-x:         .5rem;\n$breadcrumb-margin-bottom:          1rem;\n$breadcrumb-bg:                     null;\n$breadcrumb-divider-color:          $gray-600;\n$breadcrumb-active-color:           $gray-600;\n$breadcrumb-divider:                quote(\"/\");\n$breadcrumb-divider-flipped:        $breadcrumb-divider;\n$breadcrumb-border-radius:          null;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white;\n$carousel-control-width:             15%;\n$carousel-control-opacity:           .5;\n$carousel-control-hover-opacity:     .9;\n$carousel-control-transition:        opacity .15s ease;\n\n$carousel-indicator-width:           30px;\n$carousel-indicator-height:          3px;\n$carousel-indicator-hit-area-height: 10px;\n$carousel-indicator-spacer:          3px;\n$carousel-indicator-opacity:         .5;\n$carousel-indicator-active-bg:       $white;\n$carousel-indicator-active-opacity:  1;\n$carousel-indicator-transition:      opacity .6s ease;\n\n$carousel-caption-width:             70%;\n$carousel-caption-color:             $white;\n$carousel-caption-padding-y:         1.25rem;\n$carousel-caption-spacer:            1.25rem;\n\n$carousel-control-icon-width:        2rem;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\");\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\");\n\n$carousel-transition-duration:       .6s;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black;\n$carousel-dark-caption-color:        $black;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100);\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem;\n$spinner-height:          $spinner-width;\n$spinner-vertical-align:  -.125em;\n$spinner-border-width:    .25em;\n$spinner-animation-speed: .75s;\n\n$spinner-width-sm:        1rem;\n$spinner-height-sm:       $spinner-width-sm;\n$spinner-border-width-sm: .2em;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            .6em;\n$btn-close-height:           $btn-close-width;\n$btn-close-padding-x:        .25em;\n$btn-close-padding-y:        $btn-close-padding-x;\n$btn-close-color:            $black;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\");\n$btn-close-focus-shadow:     none;\n$btn-close-opacity:          .5;\n$btn-close-hover-opacity:    .75;\n$btn-close-focus-opacity:    1;\n$btn-close-disabled-opacity: .25;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%);\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding;\n$offcanvas-padding-x:               $modal-inner-padding;\n$offcanvas-horizontal-width:        400px;\n$offcanvas-vertical-height:         30vh;\n$offcanvas-transition-duration:     .3s;\n$offcanvas-border-color:            $modal-content-border-color;\n$offcanvas-border-width:            $modal-content-border-width;\n$offcanvas-title-line-height:       $modal-title-line-height;\n$offcanvas-bg-color:                $modal-content-bg;\n$offcanvas-color:                   $modal-content-color;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    87.5%;\n$code-color:                        $pink;\n\n$kbd-padding-y:                     .2rem;\n$kbd-padding-x:                     .4rem;\n$kbd-font-size:                     $code-font-size;\n$kbd-color:                         $white;\n$kbd-bg:                            $gray-900;\n\n$pre-color:                         null;\n", "//\n// _helper.scss\n//\n\n.font-size-10 {\n    font-size: 10px !important;\n}\n\n.font-size-11 {\n    font-size: 11px !important;\n}\n\n.font-size-12 {\n    font-size: 12px !important;\n}\n\n.font-size-13 {\n    font-size: 13px !important;\n}\n\n.font-size-14 {\n    font-size: 14px !important;\n}\n\n.font-size-15 {\n    font-size: 15px !important;\n}\n\n.font-size-16 {\n    font-size: 16px !important;\n}\n\n.font-size-17 {\n    font-size: 17px !important;\n}\n\n.font-size-18 {\n    font-size: 18px !important;\n}\n\n.font-size-20 {\n    font-size: 20px !important;\n}\n\n.font-size-22 {\n    font-size: 22px !important;\n}\n\n.font-size-24 {\n    font-size: 24px !important;\n}\n\n// Font weight help class\n\n.fw-medium {\n    font-weight: $font-weight-medium;\n}\n\n.fw-semibold {\n    font-weight: $font-weight-semibold;\n}\n\n// Social\n\n.social-list-item {\n    height: 2rem;\n    width: 2rem;\n    line-height: calc(2rem - 4px);\n    display: block;\n    border: 2px solid $gray-500;\n    border-radius: 50%;\n    color: $gray-500;\n    text-align: center;\n    transition: all 0.4s;\n    &:hover {\n        color: $gray-600;\n        background-color: $gray-200;\n    }\n}\n\n.w-xs {\n    min-width: 80px;\n}\n.w-sm {\n    min-width: 95px;\n}\n.w-md {\n    min-width: 110px;\n}\n.w-lg {\n    min-width: 140px;\n}\n\n.w-xl {\n    min-width: 160px;\n}\n\n.bg-overlay {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    top: 0;\n    background-color: rgba($dark, 0.7);\n}\n\n.border-primary {\n    border-color: rgba(var(--bs-primary-rgb), 1) !important;\n}\n\n// border-color\n.border-dark {\n    border-color: var(--#{$variable-prefix}dark) !important;\n}\n.border-light {\n    border-color: var(--#{$variable-prefix}light) !important;\n}\n\n[data-layout-mode=\"dark\"] {\n    .text-body {\n        color: var(--#{$variable-prefix}gray-500) !important;\n    }\n    .btn-close {\n        filter: invert(1) grayscale(100%) brightness(200%);\n    }\n}\n", "// \r\n// _progress.scss\r\n// \r\n\r\n.loader-line{\r\n  height: 28px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n\r\n  .line:nth-last-child(1) {\r\n      animation: loadingLine 1.25s 1s infinite;\r\n  }\r\n  .line:nth-last-child(2) {\r\n      animation: loadingLine 1.25s 0.75s infinite;\r\n  }\r\n  .line:nth-last-child(3) {\r\n      animation: loadingLine 1.25s 0.5s infinite;\r\n  }\r\n  .line:nth-last-child(4) {\r\n      animation: loadingLine 1.25s 0.25s infinite;\r\n  }\r\n  .line:nth-last-child(5) {\r\n      animation: loadingLine 1.25s 0s infinite;\r\n  }\r\n}\r\n\r\n.line {\r\n    display: inline-block;\r\n    width: 4px;\r\n    height: 10px;\r\n    border-radius: 14px;\r\n    background-color: rgba($text-muted, 0.7);\r\n}\r\n\r\n@keyframes loadingLine {\r\n    0% {\r\n      height: 10px;\r\n    }\r\n    50% {\r\n      height: 28px;\r\n    }\r\n    100% {\r\n      height: 10px;\r\n    }\r\n}\r\n\r\n", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .card {\r\n        + .card {\r\n            margin-top: 0.5rem;\r\n        }\r\n    }\r\n\r\n    a {\r\n        i.accor-plus-icon {\r\n            font-size: 16px;\r\n        }\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0142\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.accordion-button {\r\n    &:after {\r\n        margin-left: auto/*rtl:0*/;\r\n        margin-right:0/*rtl:auto*/;\r\n    }\r\n}\r\n\r\n// theme dark\r\n\r\n[data-layout-mode=\"dark\"]{\r\n    .accordion-button {\r\n        &:after{\r\n            background-image: escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$gray-500}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\"));\r\n        }\r\n    }\r\n}", "// \r\n// _modal.scss\r\n// \r\n\r\n.modal-header-colored {\r\n    .modal-header {\r\n        background-color:  rgba(var(--bs-primary-rgb), 1);\r\n        margin: 0 -1px -1px -1px;\r\n    }\r\n}", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n[type=\"tel\"],\r\n[type=\"url\"],\r\n[type=\"email\"],\r\n[type=\"number\"] {\r\n &::placeholder{\r\n   text-align: left; /*rtl: right*/\r\n }\r\n}\r\n\r\n\r\n.form-check,\r\n.form-check-input,\r\n.form-check-label {\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-check-input:checked {\r\n  background-color: rgba(var(--bs-primary-rgb), 1);\r\n  border-color: rgba(var(--bs-primary-rgb), 1);\r\n}\r\n", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 7px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "//\r\n// emoji-picker.scss\r\n//\r\n\r\n.fg-emoji-picker{\r\n    background-color: $card-bg !important;\r\n    width: 250px !important;\r\n    box-shadow: $box-shadow !important;\r\n    top: auto !important;\r\n    bottom: 70px;\r\n    * {\r\n        font-family: $font-family-base !important;\r\n        color: $body-color !important;\r\n    }\r\n    @media (max-width:991.98px) {\r\n        left: 14px !important;\r\n        top: auto !important;\r\n        bottom: 60px;\r\n    }\r\n\r\n    .fg-emoji-picker-container-title{\r\n        color: var(--#{$variable-prefix}dark) !important;\r\n    }\r\n\r\n    .fg-emoji-picker-search{\r\n        height: 40px !important;\r\n        input{\r\n            background-color: $input-bg !important;\r\n            color: $input-color !important;\r\n            border: 1px solid $border-color;\r\n            padding: $input-btn-padding-y $input-btn-padding-x !important;\r\n            font-size: $font-size-base !important;\r\n            &::placeholder{\r\n                color: $input-placeholder-color !important;\r\n            }\r\n        }\r\n\r\n        svg{\r\n            fill: var(--#{$variable-prefix}body-color) !important;\r\n            right: 11px;\r\n            top: 12px;\r\n        }\r\n    }\r\n\r\n    .fg-emoji-picker-categories{\r\n        background-color: var(--#{$variable-prefix}light) !important;\r\n        li.active{\r\n            background-color: rgba(var(--bs-primary-rgb), 0.2);\r\n        }\r\n        a{\r\n            &:hover{\r\n                background-color: rgba(var(--bs-primary-rgb), 0.2);\r\n            }\r\n        }\r\n        \r\n    }\r\n}\r\n\r\n.fg-emoji-picker-grid > li:hover{\r\n    background-color: rgba(var(--bs-primary-rgb), 0.2) !important;\r\n}\r\n\r\na.fg-emoji-picker-close-button{\r\n    background-color: var(--#{$variable-prefix}light) !important;\r\n}\r\n\r\n\r\n.fg-emoji-picker-categories svg{\r\n    fill: var(--#{$variable-prefix}body-color) !important;\r\n}\r\n\r\n\r\n.fg-emoji-picker-grid > li{\r\n    background-color: $card-bg !important;\r\n}", "\r\n.ml-44{\r\n    margin-left: 44px;\r\n}", "//\r\n// Sidemenu\r\n//\r\n\r\n.side-menu {\r\n    min-width: 75px;\r\n    max-width: 75px;\r\n    height: 100vh;\r\n    min-height: 570px;\r\n    background-color: $sidebar-bg;\r\n    display: flex;\r\n    z-index: 9;\r\n    border-right: 1px solid $sidebar-bg;\r\n    padding: 0 8px;\r\n\r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        bottom: 0;\r\n        height: 60px;\r\n        min-width: 100%;\r\n        min-height: auto;\r\n        border-top: 1px solid $sidebar-bg;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        text-align: center;\r\n\r\n        svg {\r\n            fill: rgba(var(--bs-primary-rgb), 1);\r\n        }\r\n\r\n        @media (max-width: 991.98px) {\r\n            display: none;\r\n        }\r\n\r\n        .logo {\r\n            line-height: 70px;\r\n        }\r\n\r\n        .logo-dark {\r\n            display: $display-block;\r\n        }\r\n\r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n}\r\n\r\n.sidemenu-navigation {\r\n    height: 100%;\r\n    @media (max-width: 991.98px) {\r\n        width: 100%;\r\n\r\n        .tooltip {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n.side-menu-nav {\r\n    height: 100%;\r\n    flex-direction: column;\r\n    @media (max-width: 991.98px) {\r\n        justify-content: space-between !important;\r\n        flex-direction: row;\r\n    }\r\n    .nav-item {\r\n        margin: 7px 0;\r\n        display: block;\r\n        width: 100%;\r\n\r\n        @media (max-width: 991.98px) {\r\n            flex-basis: 0;\r\n            flex-grow: 1;\r\n            margin: 5px 0;\r\n        }\r\n        .nav-link {\r\n            text-align: center;\r\n            font-size: 28px;\r\n            color: $sidebar-menu-item-color;\r\n            width: 100%;\r\n            height: 56px;\r\n            line-height: 56px;\r\n            border-radius: 0;\r\n            padding: 0;\r\n            position: relative;\r\n\r\n            i {\r\n                vertical-align: middle;\r\n            }\r\n\r\n            &::before {\r\n                position: absolute;\r\n                content: \"\";\r\n                height: 20px;\r\n                width: 2px;\r\n                right: -8px;\r\n                top: 18px;\r\n                @media (max-width: 991.98px) {\r\n                    width: 20px;\r\n                    height: 2px;\r\n                    right: auto;\r\n                    top: -5px;\r\n                    left: 50%;\r\n                    transform: translateX(-50%);\r\n                }\r\n            }\r\n\r\n            @media (max-width: 991.98px) {\r\n                font-size: 20px;\r\n                width: 48px;\r\n                height: 48px;\r\n                line-height: 48px;\r\n                margin: 0px auto;\r\n            }\r\n\r\n            &.active {\r\n                background-color: transparent;\r\n                color: rgba(var(--bs-primary-rgb), 1);\r\n\r\n                &::before {\r\n                    background-color: rgba(var(--bs-primary-rgb), 1);\r\n                }\r\n            }\r\n        }\r\n\r\n        &.show > .nav-link {\r\n            color: rgba(var(--bs-primary-rgb), 1);\r\n        }\r\n    }\r\n\r\n    .profile-user {\r\n        height: 36px;\r\n        width: 36px;\r\n        background-color: var(--#{$variable-prefix}gray-300);\r\n        padding: 3px;\r\n    }\r\n}\r\n\r\n.light-mode {\r\n    display: none;\r\n}\r\n\r\nbody[data-layout-mode=\"dark\"] {\r\n    .light-mode {\r\n        display: inline-block;\r\n    }\r\n\r\n    .dark-mode {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout-mode=\"dark\"] {\r\n    .side-menu-nav {\r\n        .nav-item {\r\n            .nav-link {\r\n                &.light-dark {\r\n                    .bx-moon {\r\n                        &:before {\r\n                            content: \"\\eb90\";\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .light-mode {\r\n        display: inline-block;\r\n    }\r\n\r\n    .dark-mode {\r\n        display: none;\r\n    }\r\n}\r\n", "// \r\n// chat-leftsidebar.scss\r\n//\r\n\r\n\r\n.chat-leftsidebar {\r\n    height: calc(100vh - 60px);;\r\n    position: relative;\r\n    background-color: $sidebar-sub-bg;\r\n    box-shadow: $box-shadow;\r\n\r\n    @media (min-width: 992px) {\r\n        min-width: 300px;\r\n        max-width: 300px;\r\n        height: 100vh;\r\n    }\r\n\r\n    .user-status-box {\r\n        background-color: $light;\r\n        padding: 8px;\r\n        border-radius: 8px;\r\n        text-align: center;\r\n        margin-top: 16px;\r\n        display: block;\r\n\r\n        .chat-user-img {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.chat-search-box {\r\n    .form-control {\r\n        border: 0;\r\n    }\r\n\r\n    .search-icon-btn {\r\n        font-size: 16px;\r\n        position: absolute;\r\n        right: 13px;\r\n        top: 0;\r\n    }\r\n}\r\n\r\n.chat-room-list {\r\n    max-height: calc(100vh - 130px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 190px);\r\n    }\r\n}\r\n\r\n.chat-group-list {\r\n    height: calc(100vh - 140px);\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 198px);\r\n    }\r\n}\r\n\r\n\r\n.chat-list {\r\n    margin: 0;\r\n\r\n    li {\r\n        &.active {\r\n            a {\r\n                background-color: rgba(var(--bs-primary-rgb), 1);\r\n                color: $white;\r\n\r\n                span.avatar-title {\r\n                    color: $white !important;\r\n                    &.bg-light{\r\n                        background-color: rgba($light, 0.25) !important;\r\n                    }\r\n                }\r\n\r\n                .badge {\r\n                    background-color: rgba($light, 0.25) !important;\r\n                    color: $white !important;\r\n                }\r\n\r\n                .bg-primary {\r\n                    background-color: rgba($white, 0.25) !important;\r\n                }\r\n            }\r\n        }\r\n\r\n        a {\r\n            display: block;\r\n            padding: 5px 24px;\r\n            color: var(--#{$variable-prefix}gray-700);\r\n            transition: all 0.4s;\r\n            font-size: 14px;\r\n        }\r\n\r\n        .chat-user-message {\r\n            font-size: 14px;\r\n        }\r\n\r\n        .unread-msg-user {\r\n            font-weight: 600;\r\n        }\r\n\r\n        &.typing {\r\n            .chat-user-message {\r\n                color: rgba(var(--bs-primary-rgb), 1);\r\n                font-weight: $font-weight-medium;\r\n\r\n                .dot {\r\n                    background-color: rgba(var(--bs-primary-rgb), 1);\r\n                }\r\n            }\r\n        }\r\n\r\n        .unread-message {\r\n            position: absolute;\r\n            display: inline-block;  \r\n            right: 24px/*rtl:auto*/;\r\n            left: auto/*rtl:0*/;\r\n            top: 33px;\r\n\r\n            .badge {\r\n                line-height: 16px;\r\n                font-weight: $font-weight-semibold;\r\n                font-size: 10px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.chat-user-img {\r\n    position: relative;\r\n\r\n    .user-status {\r\n        width: 10px;\r\n        height: 10px;\r\n        background-color: $gray-500;\r\n        border-radius: 50%;\r\n        border: 2px solid var(--#{$variable-prefix}card-bg);\r\n        position: absolute;\r\n        right: 0;\r\n        left: auto;\r\n        bottom: 0;\r\n    }\r\n\r\n    &.online {\r\n        .user-status {\r\n            background-color: $success;\r\n        }\r\n    }\r\n\r\n    &.away {\r\n        .user-status {\r\n            background-color: $warning;\r\n        }\r\n    }\r\n}\r\n\r\n// contact list\r\n\r\n.contact-list {\r\n    li {\r\n        cursor: pointer;\r\n        padding: 8px 24px;\r\n    }\r\n}\r\n\r\n.contact-list-title{\r\n    padding: 6px 24px;\r\n    color: rgba(var(--bs-primary-rgb), 1);\r\n    font-weight: $font-weight-medium;\r\n    position: relative;\r\n    font-size: 12px;\r\n    &:after{\r\n        content: \"\";\r\n        height: 1px;\r\n        position: absolute;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        left: 50px;\r\n        right: 0;\r\n        background-color: $border-color;\r\n    }\r\n}\r\n\r\n// Calls\r\n\r\n.chat-call-list, .chat-bookmark-list{\r\n    max-height: calc(100vh - 68px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 125px);\r\n    }\r\n    li{\r\n        position: relative;\r\n        padding: 10px 24px;\r\n        border-bottom: 1px solid $border-color;\r\n\r\n        &:last-child{\r\n            border-bottom: 0;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// settings\r\n\r\n\r\n.profile-user {\r\n    position: relative;\r\n    display: inline-block;\r\n\r\n    .profile-photo-edit {\r\n        position: absolute;\r\n        right: 0/*rtl:auto*/;\r\n        left: auto/*rtl:0*/;\r\n        bottom: 0;\r\n        cursor: pointer;\r\n    }\r\n\r\n    .user-profile-img{\r\n        object-fit: cover;\r\n    }\r\n\r\n    .profile-img-file-input{\r\n        display: none;\r\n    }\r\n}\r\n\r\n.theme-btn-list{\r\n    &.theme-color-list{\r\n        .form-check .form-check-input:checked+.form-check-label::before{\r\n            color: $white;\r\n        }\r\n    }\r\n    .form-check{\r\n        padding: 0;\r\n        .form-check-input{\r\n            display: none;\r\n        }\r\n\r\n        .form-check-label{\r\n            position: relative;\r\n        }\r\n\r\n        .form-check-input:checked + .form-check-label {\r\n            &::before{\r\n                content: \"\\e9a4\";\r\n                font-family: boxicons!important;\r\n                position: absolute;\r\n                top: 50%;\r\n                left: 50%;\r\n                transform: translateX(-50%) translateY(-50%);\r\n                color: rgba(var(--bs-primary-rgb), 1);\r\n                font-size: 16px;\r\n                z-index: 1;\r\n            }\r\n\r\n            &.light-background{\r\n                &::before{\r\n                    color: rgba(var(--bs-primary-rgb), 1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.theme-btn-list-img{\r\n        .theme-btn{\r\n            background-color: transparent !important;\r\n        }\r\n    }\r\n    \r\n}\r\n\r\n.theme-btn{\r\n    cursor: pointer;\r\n    border: 1px solid var(--#{$variable-prefix}gray-400);\r\n}", "// \r\n// user chat.scss\r\n//\r\n\r\n\r\n// user chat\r\n\r\n.user-chat {\r\n    background: url(\"../images/bg-pattern/pattern-05.png\");\r\n    transition: all 0.4s;\r\n    position: relative;\r\n    background-color: var(--#{$variable-prefix}body-bg);\r\n\r\n    .user-chat-overlay{\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        bottom: 0;\r\n        right: 0;\r\n        background-color: transparent;\r\n        opacity: 0.1;\r\n    }\r\n\r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        left: 0;\r\n        top: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        visibility: hidden;\r\n        transform: translateX(100%);\r\n        z-index: 99;\r\n\r\n        &.user-chat-show{\r\n            visibility: visible;\r\n            transform: translateX(0);\r\n        }\r\n    }\r\n\r\n    .chat-content{\r\n        position: relative;\r\n    }\r\n\r\n    &.user-chat-show{\r\n        .chat-welcome-section{\r\n            display: none;\r\n        }\r\n        .chat-content{\r\n            @media (min-width: 992px) {\r\n                display: flex !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.chat-welcome-section{\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100vh;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: relative;\r\n    @media (max-width: 991.98px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.copyclipboard-alert{\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 50%;\r\n    transform:translateX(-50%);\r\n}\r\n\r\n.user-chat-topbar{\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    z-index: 1;\r\n    background-color: rgba($white,0.05);\r\n    border-bottom: 1px solid $border-color;\r\n    backdrop-filter: blur(7px);\r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        background-color: rgba($white,0.8);\r\n    }\r\n\r\n    .topbar-bookmark{\r\n        position: absolute;\r\n        bottom: -51px;\r\n        left: 0;\r\n        right: 0;\r\n        border-radius: 0;\r\n\r\n        .bookmark-tabs{\r\n            .tab-links{\r\n                color: darken($warning, 30%);\r\n                font-size: 14px;\r\n                padding: 1px 16px;\r\n                border-right: 1px solid rgba($warning, 0.7);\r\n                white-space: nowrap;\r\n                &:first-child{\r\n                    padding-left: 4px;\r\n                }\r\n            }\r\n            .tab-list-link{\r\n                display: flex;\r\n                overflow-x: auto;\r\n\r\n                &::-webkit-scrollbar {\r\n                    -webkit-appearance: none;\r\n                }\r\n                \r\n                &::-webkit-scrollbar:vertical {\r\n                    width: 12px;\r\n                }\r\n                \r\n                &::-webkit-scrollbar:horizontal {\r\n                    height: 5px;\r\n                }\r\n                \r\n                &::-webkit-scrollbar-thumb {\r\n                    background-color: rgba($dark, .1);\r\n                    border-radius: 10px;\r\n                    border: 2px solid transparent;\r\n                }\r\n                \r\n                &::-webkit-scrollbar-track {\r\n                    border-radius: 10px;  \r\n                }\r\n            }\r\n        }\r\n\r\n        .btn-close{\r\n            padding: 12px 20px;\r\n        }\r\n    }\r\n}\r\n\r\n.call-close-btn{\r\n box-shadow: 0px 0px 0 6px $card-bg;   \r\n}\r\n\r\n.user-chat-nav {\r\n    .nav-btn {\r\n        height: 40px;\r\n        width: 40px;\r\n        line-height: 40px;\r\n        box-shadow: none;\r\n        padding: 0;\r\n        font-size: 22px;\r\n        color: var(--#{$variable-prefix}gray-600);\r\n    }\r\n    @media (max-width:575.98px) {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n    }\r\n}\r\n\r\n.replymessage-block{\r\n    padding: 12px 20px;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n    text-align: left;\r\n    border-radius: 4px;\r\n    background-color: rgba(var(--bs-primary-rgb), 0.1);\r\n    border-left: 2px solid rgba(var(--bs-primary-rgb), 1);\r\n\r\n    .conversation-name{\r\n        color: rgba(var(--bs-primary-rgb), 1);\r\n        font-size: 15px;\r\n    }\r\n}\r\n\r\n.chat-conversation {\r\n    height: calc(100vh - 94px);\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 80px);\r\n        margin-bottom: 78px;\r\n    }\r\n\r\n    .simplebar-content-wrapper{\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .simplebar-content{\r\n            margin-top: auto;\r\n        }\r\n    }\r\n\r\n    .chat-conversation-list{\r\n        margin-top: 90px;\r\n        padding-top: 10px;\r\n        margin-bottom: 0;\r\n        > li{\r\n            display: flex;\r\n        }\r\n    }\r\n\r\n    li {\r\n        &:last-of-type {\r\n            .conversation-list {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    .chat-list{\r\n\r\n        &.left{\r\n            .check-message-icon{\r\n                display: none;\r\n            }\r\n        }\r\n        .message-box-drop {\r\n            visibility: hidden;\r\n        }\r\n\r\n        &:hover {\r\n            .message-box-drop {\r\n                visibility: visible;\r\n            }\r\n        }\r\n    }\r\n\r\n    .chat-avatar {\r\n        margin: 0 16px 0 0/*rtl:0 0 0 16px*/;\r\n        \r\n        img {\r\n            width: 28px;\r\n            height: 28px;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    .chat-day-title {\r\n        position: relative;\r\n        text-align: center;\r\n        margin-bottom: 24px;\r\n        margin-top: 12px;\r\n        width: 100%;\r\n\r\n        .title {\r\n            background-color: $white;\r\n            position: relative;\r\n            font-size: 13px;\r\n            z-index: 1;\r\n            padding: 6px 12px;\r\n            border-radius: 5px;\r\n        }\r\n\r\n        &:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 1px;\r\n            left: 0;\r\n            right: 0;\r\n            background-color: rgba(var(--bs-primary-rgb), 0.2);\r\n            top: 10px;\r\n        }\r\n\r\n        .badge {\r\n            font-size: 12px;\r\n        }\r\n    }\r\n\r\n    .conversation-list {\r\n        margin-bottom: 24px;\r\n        display: inline-flex;\r\n        position: relative;\r\n        align-items: flex-end;\r\n        max-width: 80%;\r\n\r\n        @media (max-width: 575.98px) {\r\n            max-width: 90%;\r\n        }\r\n\r\n        .ctext-wrap {\r\n            display: flex;\r\n            margin-bottom: 10px;\r\n        }\r\n\r\n        .ctext-content{\r\n            word-wrap: break-word;\r\n            word-break: break-word;\r\n            color: $chat-text-color;\r\n\r\n            \r\n        }\r\n\r\n        .ctext-wrap-content {\r\n            padding: 12px 20px;\r\n            background-color: $chat-primary-bg;\r\n            position: relative;\r\n            border-radius: 3px;\r\n            box-shadow: $box-shadow;\r\n\r\n            .attached-file{\r\n                @media (max-width: 575.98px) {\r\n                    .attached-file-avatar{\r\n                        display: none;\r\n                    }\r\n\r\n                    .dropdown .dropdown-toggle{\r\n                        display: block;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .conversation-name {\r\n            font-weight: $font-weight-medium;\r\n            font-size: 14px;\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            align-items: center;\r\n            gap: 8px;\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-toggle {\r\n                font-size: 18px;\r\n                padding: 4px;\r\n                color: $gray-600;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        .chat-time {\r\n            font-size: 12px;\r\n            margin-top: 4px;\r\n            text-align: right;\r\n        }\r\n\r\n        .message-img {\r\n            border-radius: .2rem;\r\n            position: relative;\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            gap: 8px;\r\n\r\n            .message-img-list {\r\n                position: relative;\r\n            }\r\n\r\n            img {\r\n                max-width: 150px;\r\n            }\r\n\r\n            .message-img-link {\r\n                position: absolute;\r\n                right: 10px/*rtl:auto*/;\r\n                left: auto/*rtl:0*/;\r\n                bottom: 10px;\r\n\r\n                li {\r\n                    >a {\r\n                        font-size: 18px;\r\n                        color: $white;\r\n                        display: inline-block;\r\n                        line-height: 20px;\r\n                        width: 26px;\r\n                        height: 24px;\r\n                        border-radius: 3px;\r\n                        background-color: rgba($dark,0.7);\r\n                        text-align: center;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .right {\r\n        justify-content: flex-end;\r\n\r\n        .chat-avatar {\r\n            order: 3;\r\n            margin-right: 0px;\r\n            margin-left: 16px;\r\n        }\r\n\r\n        .chat-time {\r\n            text-align: left;\r\n            color: $gray-600;\r\n        }\r\n\r\n        .conversation-list {\r\n            text-align: right;\r\n\r\n            .ctext-wrap {\r\n                justify-content: flex-end;\r\n\r\n                .ctext-wrap-content {\r\n                    order: 2;\r\n                    background-color: $chat-secondary-bg;\r\n                    // color: #466751;\r\n                    text-align: right;\r\n                    box-shadow: none;\r\n\r\n                    .replymessage-block{\r\n                        background-color: rgba($white,0.5);\r\n                        border-color: rgba(var(--bs-primary-rgb), 1);\r\n                        color: $body-color;\r\n        \r\n                        .conversation-name{\r\n                            color: rgba(var(--bs-primary-rgb), 1);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            .conversation-name {\r\n                justify-content: flex-end;\r\n\r\n                .check-message-icon{\r\n                    order: 1;\r\n                }\r\n\r\n                .time{\r\n                    order: 2;\r\n                }\r\n\r\n                .name{\r\n                    order: 3;\r\n                }\r\n            }\r\n\r\n            .dropdown {\r\n                order: 1;\r\n            }\r\n        }\r\n\r\n        .dot {\r\n            background-color: $dark;\r\n        }\r\n    }\r\n}\r\n\r\n.videocallModal{\r\n    .modal-content{\r\n        min-height: 450px;\r\n        overflow: hidden;\r\n        @media (max-width: 575.98px) {\r\n            min-height: 350px;\r\n        }\r\n    }\r\n}\r\n\r\n.videocallModal-bg{\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.user-chat-remove {\r\n    background-color: rgba(var(--bs-primary-rgb), 1);\r\n    color: $white;\r\n    border-radius: 3px;\r\n    line-height: 1;\r\n}\r\n\r\n.chat-input-section{\r\n    background-color: $footer-bg;\r\n    border-top: 1px solid $border-color;\r\n    backdrop-filter: blur(7px);\r\n    position: relative;\r\n    z-index: 1;\r\n    \r\n    @media (max-width: 991.98px) {\r\n        position: fixed;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        z-index: 1;\r\n    }\r\n    \r\n    .chat-input-collapse{\r\n        position: absolute;\r\n        bottom: 92px;\r\n        left: 0;\r\n        right: 0;\r\n        border-top: 1px solid $border-color;\r\n        overflow: hidden;\r\n\r\n        @media (max-width: 991.98px) {\r\n            bottom: 74px;\r\n        }\r\n    }\r\n\r\n    .chat-input-feedback {\r\n        display: none;\r\n        position: absolute;\r\n        top: -18px;\r\n        left: 16px;\r\n        font-size: 12px;\r\n        color: $danger;\r\n    }\r\n    \r\n    .show{\r\n        display: block;\r\n    }\r\n\r\n    .replyCollapse{\r\n        z-index: 1;\r\n    }\r\n}\r\n\r\n.file_Upload{\r\n        background-color:$card-bg;\r\n        border-top: 1px solid $border-color;\r\n        padding: 16px 24px;\r\n\r\n        .card{\r\n            margin-bottom: 0;\r\n            border-color: rgba(var(--bs-primary-rgb), 1) !important;\r\n        }\r\n}\r\n\r\n\r\n.replyCard, .file_Upload{\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    border-top: 1px solid $border-color;\r\n    overflow: hidden;\r\n    opacity: 0;\r\n    bottom: 0;\r\n    transition: all 0.4s;\r\n\r\n    @media (max-width: 991.98px) {\r\n        bottom: -12px;\r\n    }\r\n\r\n    &.show{\r\n        transform: translateY(-92px);\r\n        opacity: 1;\r\n\r\n        @media (max-width: 991.98px) {\r\n            transform: translateY(-86px);\r\n        }\r\n    }\r\n}\r\n\r\n.contact-modal-list{\r\n    .contact-list{\r\n        li{\r\n            margin: 2px 0px;\r\n            &.selected{\r\n                background-color: rgba(var(--bs-primary-rgb), 0.1);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.chat-input-links{\r\n    display: flex;\r\n    .links-list-item{\r\n        > .btn{\r\n            box-shadow: none;\r\n            padding: 0;\r\n            font-size: 22px;\r\n            width: 43px;\r\n            height: 43px;\r\n            &.btn-link{\r\n                color: var(--#{$variable-prefix}gray-600);\r\n            }\r\n        }\r\n        // [data-bs-toggle=\"collapse\"]{\r\n        //     &[aria-expanded=\"true\"]{\r\n        //         .bx-up-arrow-alt{\r\n        //             &:before{\r\n        //                 content: \"\\ea19\";\r\n        //             }\r\n        //         }\r\n        //     }\r\n        // }\r\n        \r\n    }\r\n}\r\n\r\n.animate-typing {\r\n\t.dot {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 4px;\r\n\t\theight: 4px;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: -1px;\r\n\t\tbackground: $dark;\r\n        animation: wave 1.3s linear infinite;\r\n        opacity: 0.6;\r\n\r\n\t\t&:nth-child(2) {\r\n\t\t\tanimation-delay: -1.1s;\r\n\t\t}\r\n\r\n\t\t&:nth-child(3) {\r\n\t\t\tanimation-delay: -0.9s;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@keyframes wave {\r\n\t0%, 60%, 100% {\r\n\t\ttransform: initial;\r\n\t}\r\n\r\n\t30% {\r\n\t\ttransform: translateY(-5px);\r\n\t}\r\n}\r\n\r\n\r\n// only for firefox browser\r\n@-moz-document url-prefix() {\r\n    .user-chat-topbar, .chat-input-section{\r\n        background-color: darken($body-bg, 3%) !important;\r\n    }\r\n}", "// \r\n//  User profile details.scss\r\n//\r\n\r\n// User profile details\r\n\r\n.user-profile-sidebar {\r\n    height: 100vh;\r\n    background-color: $card-bg;\r\n    display: none;\r\n    min-width: 380px;\r\n    max-width: 380px;\r\n\r\n    @media (min-width: 992px) {\r\n        border-left: 4px solid $border-color;\r\n    }\r\n\r\n    @media (max-width: 1199.98px) {\r\n        position: fixed;\r\n        right: 0;\r\n        top: 0;\r\n        z-index: 99;\r\n    }\r\n\r\n    @media (max-width: 575.98px) {\r\n        min-width: 100%;\r\n    }\r\n}\r\n\r\n.user-profile-img{\r\n    position: relative;\r\n    \r\n    .overlay-content{\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        bottom: 0;\r\n        right: 0;\r\n        background: linear-gradient(180deg,rgba(0,0,0,0.5) 10%, rgba(0,0,0,0) 60%,rgba(0,0,0,.5) 100%);\r\n        display: flex;\r\n        height: 100%;\r\n        color: rgba($white, 0.6);\r\n        flex-direction: column;\r\n    }\r\n\r\n    .user-name{\r\n        font-size: 16px;\r\n        color: $white;\r\n    }\r\n\r\n    .profile-img{\r\n        width: 100%;\r\n        height: 250px;\r\n        object-fit: cover;\r\n\r\n        @media (max-width:991px) {\r\n            height: 160px;\r\n        }\r\n    }\r\n\r\n    .profile-foreground-img-file-input{\r\n        display: none;\r\n    }\r\n\r\n    .profile-photo-edit{\r\n        cursor: pointer;\r\n    }\r\n}\r\n\r\n.user-profile-image{\r\n    object-fit: cover;\r\n}\r\n\r\n.user-profile-desc {\r\n    height: calc(100vh - 285px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 194px);\r\n    }\r\n}\r\n\r\n.profile-desc{\r\n    height: calc(100vh - 285px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 330px);\r\n    }\r\n}\r\n\r\n.profile-media-img{\r\n    display: flex;\r\n    gap: 8px;\r\n\r\n    .media-img-list{\r\n        position: relative;\r\n        a{\r\n            display: block;\r\n            position: relative;\r\n            border-radius: 4px;\r\n            overflow: hidden;\r\n            img{\r\n                width: 76px;\r\n                height: 76px;\r\n                object-fit: cover;\r\n            }\r\n\r\n            .bg-overlay{\r\n                display: flex;\r\n                justify-content: center;\r\n                align-items: center;\r\n                color: $white;\r\n            }\r\n        }\r\n\r\n        .image-remove{\r\n            position: absolute;\r\n            top: 0;\r\n            right: 0;\r\n            color: $white;\r\n            background: rgba($dark, 0.7);\r\n            width: 18px;\r\n            height: 18px;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            border-radius: 2px;\r\n            margin: 4px;\r\n        }\r\n    }\r\n}\r\n\r\n.favourite-btn{\r\n    &.active{\r\n        .bx-heart{\r\n            color: $danger;\r\n            &:before{\r\n                content: \"\\ed36\";\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// edit input\r\n.edit-input{\r\n    &.form-control[readonly]{\r\n        padding: 0;\r\n        font-weight: $headings-font-weight;\r\n        color: $headings-color;\r\n        &:focus{\r\n            border-color: transparent;\r\n        }\r\n    }\r\n}\r\n\r\n// setting\r\n\r\n.user-setting{\r\n    height: calc(100vh - 288px);\r\n\r\n    @media (max-width: 991.98px) {\r\n        height: calc(100vh - 320px);\r\n    }\r\n}\r\n\r\n", "// \r\n// authentication.scss\r\n//\r\n\r\n.auth-logo{\r\n    .logo{\r\n        margin: 0px auto;\r\n    }\r\n\r\n    .logo-dark{\r\n        display: $display-block;\r\n    }\r\n\r\n    .logo-light{\r\n        display: $display-none;\r\n    }\r\n}\r\n\r\n.auth-bg{\r\n    background-color: rgba(var(--bs-primary-rgb), 1);\r\n    min-height: 100vh;\r\n    background-size: cover;\r\n    background-position: center;\r\n}\r\n\r\n.auth-logo-section{\r\n    display: flex;\r\n    flex-direction: column;\r\n    @media (min-width: 992px) {\r\n        height: 100vh;\r\n    }\r\n\r\n    @media (max-width: 991.98px) {\r\n        text-align: center\r\n    }\r\n}\r\n\r\n.authentication-page-content{\r\n    height: calc(100% - 48px);\r\n    background-color: $white;\r\n    border-radius: 16px;\r\n    margin: 24px;\r\n}\r\n\r\n.auth-img{\r\n    position: relative;\r\n    @media (min-width: 992px) and (max-width: 1549.98px) {\r\n        max-width: 220%;\r\n    }\r\n    @media (min-width: 1550px) {\r\n        max-width: 200%;\r\n    }\r\n    @media (max-width: 991.98px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n// auth-pass-inputgroup\r\n\r\n.auth-pass-inputgroup{\r\n    input[type=\"text\"] + .btn .ri-eye-fill{\r\n        &:before{\r\n            content: \"\\ec80\";\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// signin card title\r\n\r\n.signin-other-title{\r\n    position: relative;\r\n    &:after{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 100%;\r\n        height: 1px;\r\n        left: 0;\r\n        right: 0;\r\n        background-color: $border-color;\r\n        top: 10px;\r\n    }\r\n\r\n    .title{\r\n        display: inline-block;\r\n        position: relative;\r\n        z-index: 9;\r\n        background-color: $card-bg;\r\n        padding: 2px 16px;\r\n    }\r\n}\r\n"]}